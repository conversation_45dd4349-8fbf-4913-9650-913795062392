{"name": "oelove_union", "version": "v10.2.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@vueuse/core": "^10.7.0", "axios": "^0.26.0", "core-js": "^3.6.5", "cropperjs": "^1.5.12", "html2canvas": "^1.4.1", "qiniu-js": "^3.4.1", "vue": "^3.0.0", "vue-hash-calendar": "^1.4.30", "vue-router": "^4.0.0-0", "vuex": "^4.0.0-0", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-eslint": "~4.5.15", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-plugin-vuex": "~4.5.15", "@vue/cli-service": "~4.5.15", "@vue/compiler-sfc": "^3.0.0", "babel-eslint": "^10.1.0", "compression-webpack-plugin": "^6.1.1", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "lib-flexible": "^0.3.2", "sass": "^1.26.5", "sass-loader": "^8.0.2", "uglifyjs-webpack-plugin": "^2.2.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}
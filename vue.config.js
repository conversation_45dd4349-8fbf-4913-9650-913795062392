const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const CompressionWebpackPlugin = require('compression-webpack-plugin');
const productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i;
const isProduction = process.env.NODE_ENV === 'production';
const Version = String(new Date().getTime()).slice(0, 10);
let path = require('path');
module.exports = {
  publicPath: isProduction ? './' : '/',
  lintOnSave: false,
  productionSourceMap: false,
  css: {
    loaderOptions: {
      scss: {
        prependData: `@import "./src/style/main.scss";`
      }
    },
    extract: {
      filename: `css/[name].${Version}.css`,
      chunkFilename: `css/[name].${Version}.css`
    }
  },
  devServer: {
    host: '',
    port: 9595, // 设置端口号
    open: false,
    proxy: {
      '/': {
        target: 'http://localhmm.oelove.com', //API服务器的地址
        //target: 'http://oe116.oelove.com/',
        ws: true, //代理websockets
        secure: false, // 如果是https接口，需要配置这个参数
        changeOrigin: true // 是否跨域，虚拟的站点需要更管origin
      }
    },
    disableHostCheck: true,
    historyApiFallback: true,
    compress: true
  },
  chainWebpack: config => {
    config.plugin('html').tap(args => {
      args[0].title = '';
      return args;
    });
  },
  configureWebpack: config => {
    const plugins = [];
    if (isProduction) {
      plugins.push(
        new UglifyJsPlugin({
          uglifyOptions: {
            output: {
              comments: false // 去掉注释
            },
            warnings: false,
            compress: {
              drop_console: true,
              drop_debugger: false,
              pure_funcs: ['console.log'] //移除console
            }
          }
        })
      );
    }
    config.output.filename = `js/[name].${Version}.js`;
    config.output.chunkFilename = `js/[name].${Version}.js`;
    if (process.env.NODE_ENV === 'production') {
      // 为生产环境修改配置...
      config.mode = 'production';
      return {
        plugins: [
          new CompressionWebpackPlugin({
            test: productionGzipExtensions, //匹配文件名
            threshold: 10240, //对超过10k的数据进行压缩
            deleteOriginalAssets: false //是否删除原文件
          })
        ]
      };
    }
    Object.assign(config.resolve, {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    });
  }
};

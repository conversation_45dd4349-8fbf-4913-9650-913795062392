/** @format */

let wx = require('weixin-js-sdk') || window['wx']
import OEUI from '@/oeui/js/oeui'
import axios from '@/utils/axios'

const isWeiXin = () => {
  var ua = window.navigator.userAgent.toLowerCase()
  //通过正则表达式匹配ua中是否含有MicroMessenger字符串
  if (ua.match(/MicroMessenger/i) == 'micromessenger') {
    return true
  } else {
    return false
  }
}

const getCheckLogin = newurl => {
  //检查是否需要微信
  let url = sessionStorage.getItem('forwardUrl')
  http
    .post('h5.php/wxauth/scope', {
      redirect_url: newurl || url || ''
    })
    .then(res => {
      if (res.ret == 1) {
        localStorage.setItem('paytype', '1')
        window.location.replace(res.result)
      } else {
        console.log('微信功能暂时无法使用')
      }
    })
}

const getOpenid = (code, call) => {
  //获取openid
  OEUI.loading.show()
  http
    .post('h5.php/wxauth/getopenid', {
      code: code
    })
    .then(res => {
      OEUI.loading.hide()
      if (res.ret == 1) {
        if (call) call(res.result.openid)
      } else {
        alert('获取微信openid失败')
      }
    })
}

const getWeinxinPay = params => {
  return axios.post('', {
    m: 'wap',
    c: 'pay',
    a: 'wxpay3',
    paynum: params
  })
}

const wechatPay = (params, callback) => {
  //公用微信支付
  wx.ready(() => {
    wx.chooseWXPay({
      timestamp: params.timeStamp,
      nonceStr: params.nonceStr,
      package: params.package,
      signType: params.signType,
      paySign: params.paySign,
      success: () => {
        if (callback) callback('success')
      },
      cancel: () => {
        if (callback) callback('cancel')
      },
      fail: () => {
        if (callback) callback('error')
      }
    })
  })
}

const getWechatShare = (name, id) => {
  //获取微信分享参数
  if (isWeiXin()) {
    axios
      .post('', {
        m: 'union',
        vuemod: 'vue',
        c: 'wxjsapi',
        a: 'share',
        idmark: name,
        id
      })
      .then(res => {
        if (res.ret == 1) {
          setWechatShare(res.result.data)
        }
      })
  }
}

const setWechatShare = data => {
  if (isWeiXin()) {
    wx.ready(() => {
      // 自定义“分享给朋友”及“分享到QQ”按钮的分享内容
      wx.updateAppMessageShareData({
        title: data.share_title || '',
        desc: data.share_des || '',
        link: data.share_url || '',
        imgUrl: data.share_img,
        success: function () {}
      })
      // 自定义“分享到朋友圈”及“分享到QQ空间”按钮的分享内容
      wx.updateTimelineShareData({
        title: data.share_title || '',
        link: data.share_url || '',
        imgUrl: data.share_img,
        success: function () {}
      })
    })
  }
}

export { isWeiXin, getCheckLogin, getWeinxinPay, getOpenid, wechatPay, getWechatShare, setWechatShare }

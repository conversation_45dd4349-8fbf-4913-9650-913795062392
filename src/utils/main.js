
import axios from '@/utils/axios'

const getPickerData = (item, limit) => {//获取picker数据
  let itemArr = localStorage.getItem(item + 'Picker');
  let configTime = localStorage.getItem('newConfigTime');
  let jsonArr = JSON.parse(itemArr);
  if (jsonArr && configTime == jsonArr[1]) {
    return new Promise((resolve, reject) => {
      let arr = jsonArr[0]
      if (limit) {
        arr.unshift({ i: 0, value: 0, text: "不限" });
      }
      resolve({ ret: 1, result: arr });
    })
  } else {
    return new Promise((resolve, reject) => {
      const p = axios.post('', {
        m: 'vuewap',
        c: 'picker',
        a: 'varcache',
      })
      p.then(res => {
        if (res.ret == 1) {
          res.result.forEach(v => {
            for (let key in v) {
              let data = []
              data[0] = v[key]
              data[1] = configTime
              localStorage.setItem(key + 'Picker', JSON.stringify(data));
              if (key == item) {
                if (limit) {
                  v[key].unshift({ i: 0, value: 0, text: "不限" });
                }
                resolve({ ret: 1, result: v[key] })
              }
            }
          })
        }
      })
    })
  }
}

const getDistPicker = (text) => {//获取地区数据
  let itemArr = localStorage.getItem('distPicker');
  let configTime = localStorage.getItem('newConfigTime');
  let jsonArr = JSON.parse(itemArr);
  if (jsonArr && configTime == jsonArr[1]) {
    return new Promise((resolve, reject) => {
      resolve(jsonArr[0]);
    })
  } else {
    const p = axios.post('', {
      m: 'vuewap',
      c: 'picker',
      a: 'area',
      text: text || ''
    })
    p.then(res => {
      if (res.ret == 1) {
        localStorage.setItem('distPicker', JSON.stringify([res, configTime]));
      }
    })
    return p;
  }
}
const getHomePicker = (text) => {//获取户籍地数据
  let itemArr = localStorage.getItem('homePicker');
  let configTime = localStorage.getItem('newConfigTime');
  let jsonArr = JSON.parse(itemArr);
  if (jsonArr && configTime == jsonArr[1]) {
    return new Promise((resolve, reject) => {
      resolve(jsonArr[0]);
    })
  } else {
    const p = axios.post('', {
      m: 'vuewap',
      c: 'picker',
      a: 'hometown',
      text: text || ''
    })
    p.then(res => {
      if (res.ret == 1) {
        localStorage.setItem('homePicker', JSON.stringify([res, configTime]));
      }
    })
    return p;
  }
}
const getDistPickerCond = (text) => {//获取择偶地区数据
  let itemArr = localStorage.getItem('distPickerCond');
  let configTime = localStorage.getItem('newConfigTime');
  let jsonArr = JSON.parse(itemArr);
  if (jsonArr && configTime == jsonArr[1]) {
    return new Promise((resolve, reject) => {
      resolve(jsonArr[0]);
    })
  } else {
    const p = axios.post('', {
      c: 'picker',
      a: 'area',
      text: text || ''
    })
    p.then(res => {
      if (res.ret == 1) {
        localStorage.setItem('distPickerCond', JSON.stringify([res, configTime]));
      }
    })
    return p;
  }
}

const getHomePickerCond = (text) => {
  //获取择偶户籍地数据
  let itemArr = localStorage.getItem("homePickerCond");
  let configTime = localStorage.getItem("newConfigTime");
  let jsonArr = JSON.parse(itemArr);
  if (jsonArr && configTime == jsonArr[1]) {
    return new Promise((resolve, reject) => {
      resolve(jsonArr[0]);
    });
  } else {
    const p = axios.post("index.php/picker/hometown2", {
      text: text || "",
    });
    p.then((res) => {
      if (res.ret == 1) {
        localStorage.setItem(
          "homePickerCond",
          JSON.stringify([res, configTime])
        );
      }
    });
    return p;
  }
};
const setHtmlExp = (result) => {
  let exp = new RegExp('&amp;nbsp;', 'g');
  result = result.replace(result ? /&(?!#?\w+;)/g : /&/g, '&amp;')
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&quot;/g, "\"")
    .replace(/&#39;/g, "\'");
  return result.replace(exp, '\u3000');
}

export {
  getPickerData,
  getDistPicker,
  getDistPickerCond,
  getHomePickerCond,
  getHomePicker,
  setHtmlExp
}
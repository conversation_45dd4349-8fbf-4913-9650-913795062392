/** @format */

// 导入默认图片
import defaultImg from '@/assets/images/loading_img.png'
import userdefaultImg from '@/assets/images/gender_0.png'
import uniondefaultImg from '@/assets/images/union_headimg.png'

// 引入监听是否进入视口
import { useIntersectionObserver } from '@vueuse/core'
export default {
  install(app) {
    // app 实例身上有我们想要的全局注册指令方法  调用即可
    app.directive('lazy', {
      mounted(el, binding) {
        // el:img dom对象
        // binding.value  图片url地址
        // 使用 vueuse/core 提供的监听 api 对图片 dom 进行监听 正式进入视口才加载
        // img.src = url
        let type = el.getAttribute('type')
        let defaultUrl = defaultImg
        if (type == 'user') {
          defaultUrl = userdefaultImg
        } else if (type == 'union') {
          defaultUrl = uniondefaultImg
        }

        el.classList.add('oe_imgload')
        const { stop } = useIntersectionObserver(el, ([{ isIntersecting }], observerElement) => {
          if (isIntersecting) {
            // ◆图片加载失败显示默认图片
            el.classList.add('show')
            // ◆这里显示传过来的图片数据
            el.src = binding.value || defaultUrl
            el.onerror = () => {
              el.src = defaultUrl
            }
            stop() // 中止监听
          }
        })
      }
    })
  }
}

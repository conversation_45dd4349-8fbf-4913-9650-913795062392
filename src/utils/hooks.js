


//解析时间戳
const getTime = (nS = 0, isNoTime = false, isNoYear = false) => {
  let dateText = new Date(parseInt(nS) * 1000).toLocaleString()
  dateText = dateText.replace(/年|月/g, '-').replace(/日/g, ' ').replace(/\//g, '-')
  if (isNoTime) {
    dateText = /\d{4}-\d{1,2}-\d{1,2}/g.exec(dateText)[0]
  } else if (isNoYear) {
    dateText = dateText.replace(/\d{4}-\d{1,2}-\d{1,2}/g.exec(dateText)[0], ' ')
  }
  return dateText
}



const getWeekText = (nS = 0) => {
  let dateTime = new Date(parseInt(nS) * 1000)
  const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];

  return days[dateTime.getDay()]
}
const getTimeText = (nS = 0, isNoTime = true) => {
  let time = new Date(parseInt(nS) * 1000)
  let dateText = time.getFullYear() + ' 年 ' + (time.getMonth() + 1) + ' 月 ' + time.getDate() + ' 日'
  if (!isNoTime) {
    dateText += ' ' + time.getHours() + ':' + time.getMinutes() + ':' + time.getSeconds()
  }
  return dateText
}


//手机号脱敏
const filterMobile = (mobile) => {
  if(!mobile) return
  let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/
  return mobile.replace(reg, "$1****$2")
}


export { getTime, getWeekText, getTimeText, filterMobile }
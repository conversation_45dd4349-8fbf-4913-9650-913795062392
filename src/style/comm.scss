/** @format */

.oe_imgload {
  opacity: 0.3;
  &.show {
    opacity: 1;
    transition: opacity 500ms;
  }
}

//骨架
.list_skelecton {
  background: #fff;
  padding: 0.4267rem;
  border-radius: 0.32rem;
  margin-bottom: 0.32rem;
  .head {
    width: 60px;
    height: 60px;
    background: #f1f1f1;
    border-radius: 50%;
    margin-right: 0.2133rem;
  }
  p {
    display: block;
    width: 90%;
    background: #f1f1f1;
    height: 0.5333rem;
    border-radius: 0.1333rem;
  }
  span {
    margin-top: 0.2133rem;
    display: block;
    width: 40%;
    background: #f1f1f1;
    height: 0.5333rem;
    border-radius: 0.08rem;
  }
}

.party_skelecton {
  background: #fff;
  border-radius: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.32rem;
  .head {
    height: 4.4267rem;
    background: #f1f1f1;
  }
  .info {
    padding: 0.32rem;
    box-sizing: border-box;
    border-bottom: 0.0267rem solid #f2f4f5;

    p {
      display: block;
      width: 75%;
      height: 0.5867rem;
      background: #f1f1f1;
      border-radius: 0.1333rem;
    }
    span {
      margin-top: 0.2133rem;
      display: inline-block;
      background: #f1f1f1;
      height: 0.4533rem;
      width: 45px;
      margin-right: 0.32rem;
      border-radius: 0.08rem;
    }
  }
}

.color_2a2 {
  color: $color_3;
}
.color_green {
  color: #15ce7b !important;
}
.color_red {
  color: #ed1616 !important;
}
.color_main {
  color: $color_main !important;
}
.color_9{color: #999;}
.fz10 {
  font-size: 0.2667rem;
}
.fz12 {
  font-size: 0.32rem;
}
.fz14 {
  font-size: 0.3733rem;
}
.fz16 {
  font-size: 0.4267rem;
}
.fz18 {
  font-size: 0.48rem;
}
.fz20 {
  font-size: 0.5333rem;
}

.oh {
  overflow: hidden;
}
.ws {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.vam {
  vertical-align: middle !important;
}

.flex {
  display: flex;
}
.flex_v {
  flex-direction: column;
}
.flex_warp {
  flex-wrap: wrap;
}
.flex_dc {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex_ac {
  align-items: center;
}
.flex_jsb {
  justify-content: space-between;
}
.flex_s {
  flex-shrink: 0;
}
.flex_1 {
  flex: 1;
}
.flex_el {
  justify-content: space-evenly;
}
.w100 {
  width: 100%;
}

.ws {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.fw5 {
  font-weight: 500;
}
.fn_i {
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: normal;
}
.fn {
  font-weight: normal;
}
.fb {
  font-weight: bold;
}
.pf {
  position: fixed;
}
.pa {
  position: absolute;
}
.pr {
  position: relative;
}
.w100per {
  width: 100%;
}
.bg_f {
  background: #fff;
}
.h50 {
  height: 1.3333rem;
}
.h20 {
  height: 0.5333rem;
}
.h60 {
  height: 1.6rem;
}
.h70 {
  height: 1.8667rem;
}
.h80 {
  height: 2.1333rem;
}
.h44 {
  height: 1.1733rem;
}
.ob_c{
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.lbr {
  left: 0;
  right: 0;
  bottom: 0;
}
.bsb {
  box-sizing: border-box;
}
input,
input:focus,
textarea,
textarea:focus,
select,
select:focus {
  font-size: 0.4267rem !important;
}

.group {
  padding-left: 0.3733rem !important;
  padding-right: 0.16rem !important;
  height: 0.3733rem !important;
  border-radius: 0.16rem !important;
  position: relative !important;
  margin-right: 0.2667rem !important;
  em {
    font-size: 0.2667rem !important;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal !important;
    position: relative !important;
    top: -0.1067rem !important;
  }
  i {
    position: absolute;
    width: 0.48rem;
    height: 0.48rem;
    left: -0.16rem;
    top: -0.0533rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  &.group1 {
    background: linear-gradient(180deg, #caecff 0%, #99bbff 100%);
    color: #648fd0 !important;
  }
  &.group2 {
    background: linear-gradient(360deg, #bfadf6 2%, #e2d9ff 97%);
    color: #7c64d0 !important;
  }
  &.group3 {
    background: linear-gradient(360deg, #ffd37a 1%, #fff59c 97%);
    color: #ed8e00 !important;
  }
  &.group4 {
    background: linear-gradient(360deg, #ffb5cf 4%, #ffc5e1 99%);
    color: #ff4397 !important;
  }
  &.group5 {
    background: linear-gradient(360deg, #89e2dc 6%, #b5ffe7 99%);
    color: #00ceba !important;
  }
  &.group6 {
    background: linear-gradient(360deg, #87c9e4 9%, #a5f6ff 100%);
    color: #1ba5cb !important;
  }
  &.group7 {
    background: linear-gradient(360deg, #78e27d 3%, #ceffbb 99%);
    color: #30c428 !important;
  }
  &.group8 {
    background: linear-gradient(360deg, #ff9c9c 8%, #ffd1d1 100%);
    color: #ff5151 !important;
  }
  &.group9 {
    background: linear-gradient(360deg, #e2b1ff 1%, #f0caff 100%);
    color: #a864d0 !important;
  }
  &.group10 {
    background: linear-gradient(360deg, #000000 4%, #6c6c6c 100%);
    color: #ffe121 !important;
  }
}
.top_box {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  width: 100%;
  img {
    width: 100%;
  }
}
.clear {
  clear: both;
}

.top_nav {
  background: #f7f8fa;
  height: 1.1733rem;
  top: 0;
  width: 100%;
  z-index: 100;
  font-family: PingFang SC-Regular, PingFang SC;
  span {
    width: 1.1733rem;
    height: 1.1733rem;
    i {
      font-size: 0.64rem;
      position: relative;
      top: 0.0267rem;
    }
  }
  .back {
    position: absolute;
    left: 0;
  }
  .search {
    position: absolute;
    right: 0.4267rem;
    background: #7d68fe;
    color: #fff;
    font-size: 0.3467rem;
    line-height: 0.48rem;

    font-weight: normal;
    padding: 0.16rem 0.4267rem;
    border-radius: 0.4rem;
    cursor: pointer;
  }
  .more {
    position: absolute;
    right: 0.4267rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #333;
    line-height: 1.1733rem;
    width: fit-content !important;
  }
  .title {
    font-size: 0.48rem;
    line-height: 0.64rem;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #2a2546;
  }
}

//提示弹窗
.intro_dialog {
  padding: 0.64rem;
  font-family: PingFang SC, PingFang SC;
  .title {
    font-size: 0.4267rem;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    text-align: center;
  }
  .tip {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.64rem;
  }
  .tips {
    font-size: 0.32rem;
    font-weight: normal;
    color: #999999;
    line-height: 0.5867rem;
  }
  .event {
    margin: 0 0.6133rem;
    margin-top: 0.5333rem;
    font-size: 0.4267rem;
    font-weight: normal;
    color: #ffffff;
    line-height: 0.5867rem;
    background: $color_main;
    text-align: center;
    padding: 0.24rem 0;
    border-radius: 0.5333rem;
  }
}

//分享弹窗
.shareDialog {
  padding: 0.8533rem;
  text-align: center;
  position: relative;
  .close {
    position: absolute;
    right: 0.48rem;
    top: 0.48rem;
    font-size: 0.5333rem;
  }
  .title {
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #999999;
    line-height: 0.5867rem;
  }
  .channel {
    margin-top: 0.8533rem;
    display: flex;
    align-items: center;
    justify-content: center;
    div {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      span {
        width: 1.28rem;
        height: 1.28rem;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      p {
        font-size: 0.3733rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #31293b;
        line-height: 0.5333rem;
        margin-top: 0.2133rem;
      }
    }
  }
}

//失败弹窗
.fail_dialog {
  border-radius: 0.64rem 0.64rem 0 0;
  padding: 0.64rem 0.48rem;
  text-align: center;
  .tips_img {
    position: absolute;
    left: 50%;
    top: -2rem;
    transform: translateX(-50%);
    width: 3.2rem;
    height: 2.88rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .title {
    margin-top: 0.6933rem;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
  }
  .tips {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    color: #666666;
  }
  .event {
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin: 0 0.7733rem;
    margin-top: 0.64rem;
    padding: 0.24rem 0;
  }
}

// 等级样式
.grade1 {
  background: linear-gradient(176deg, #eefffe 3%, #e3edff 100%);
}
.grade2 {
  background: linear-gradient(175deg, #f5ecff 1%, #e9e4ff 100%);
}
.grade3 {
  background: linear-gradient(177deg, #fff7ee 1%, #ffebe7 100%);
}
.grade4 {
  background: linear-gradient(177deg, #ffeef6 1%, #ffeded 100%);
}
.grade5 {
  background: linear-gradient(176deg, #ddffff 3%, #d0ffdf 100%);
}
.grade6 {
  background: linear-gradient(175deg, #ecfff6 5%, #aad9ff 100%);
}
.grade7 {
  background: linear-gradient(176deg, #f3ffdd 3%, #d7ffd0 99%);
}
.grade8 {
  background: linear-gradient(177deg, #fff6ed 1%, #ffd3d3 99%);
}
.grade9 {
  background: linear-gradient(177deg, #ffeafc 7%, #e4d9ff 100%);
}
.grade10 {
  background: linear-gradient(175deg, #534936 1%, #313643 96%);
  .goup,
  .people {
    color: #f9e1c4 !important;
  }
  .detail > div {
    // background: url('~@/assets/images/level10.png') !important;
  }
}

*{
  -webkit-tap-highlight-color: rgba(0,0,0,0);
  -webkit-overflow-scrolling: touch;
}
html,body{
  position: relative;
}
body{
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto; 
  max-width: 750px;
  background: #F4F5F7;
  font-size: 12px;
  font-family: "PingFangSC-Semibold","Microsoft Yahei",arial,"Hiragino Sans GB","Hiragino Sans GB W3",宋体,simsun; 
  color:#121d33; 
  padding:0; 
  line-height: 0.586rem;
  height:100%;
  overflow: hidden;
}
.body_oh{
  height:100%;
  overflow: hidden;
}
a{
  cursor: pointer;
}
a:link {
  color: #333;
  text-decoration: none;
}
a:visited {
  color: #333;
  text-decoration: none;
}
a:hover {
  color: #333;
  text-decoration: none;
}
a:active {
  color: #333;
  text-decoration: none;
}
input{  
	background:none;  
	outline:none;  
	border:none;
  &:-moz-placeholder{color:#999999;}
  &::-moz-placeholder{color:#999999;}
  &:-ms-input-placeholder{color:#999999;}
  &::-webkit-input-placeholder{color:#999999;}
}
input:focus{   
	border:none;
}
p,h1,h2,h3,h4,h5,h6,ul,li,dl,dd,dt,form{list-style:none; padding:0;margin:0;}
h1,h2,h3,h4,h5,h6,textarea{font-family:"Microsoft Yahei",arial,"Hiragino Sans GB","Hiragino Sans GB W3",宋体,simsun;}
img{border:none;vertical-align: top;}
.clear{clear:both;}
.clearfix:after{content:''; display: block; clear: both;}
span,label,b,em,i{font-style:normal; display:inline-block;}
a,texterea,input{outline:none;}
img[src=""],img:not([src]){opacity: 0;}
textarea,button,select{outline: none;padding: 0;appearance: none;-webkit-appearance: none;margin: 0;}
textarea{line-height:0.586rem;}
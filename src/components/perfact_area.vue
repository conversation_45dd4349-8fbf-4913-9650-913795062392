<template>
  <div class="oe_area">
    <div class="tab_list">
      <div ref="distOne" class="tab" :class="dist1 == 0 ? 'c9' : ''" @click="selectDist('distOne', 0)">
        {{ dist1Text }}
      </div>
      <div ref="distTwo" class="tab" :class="dist2 == 0 ? 'c9' : ''" @click="selectDist('distTwo', 1)" v-show="dist1 > 0">
        {{ dist2Text }}
      </div>
      <div ref="distThree" class="tab" :class="dist3 == 0 ? 'c9' : ''" @click="selectDist('distThree', 2)" v-show="dist2 > 0 && row > 2">
        {{ dist3Text }}
      </div>
      <span class="solid" :style="{ width: width + 'px', transform: 'translateX(' + left + 'px)' }"></span>
    </div>
    <div class="tab_pane" ref="pane">
      <div class="ripple" :style="{ transform: 'translateX(-' + ind * 33.33 + '%)' }">
        <ul class="ullist" ref="ulOne" v-if="arrList.length > 0">
          <li v-for="(item, i) in arrList" :key="i" @click="selectArea(item, i, 1)" :class="dist1 == item.value ? 'current' : ''">
            <span>{{ item.text }}</span><i class="oeuifont oeui-checkbox"></i>
          </li>
        </ul>
        <ul class="ullist" ref="ulTwo" v-if="arrList.length > 0">
          <li v-for="(item, i) in arrList[index1].children" :key="i" @click="selectArea(item, i, 2)" :class="dist2 == item.value ? 'current' : ''">
            <span>{{ item.text }}</span><i class="oeuifont oeui-checkbox"></i>
          </li>
        </ul>
        <ul class="ullist" ref="ulThree" v-if="arrList.length > 0">
          <li v-for="(item, i) in arrList[index1].children[index2].children" :key="i" @click="selectArea(item, i, 3)" :class="dist3 == item.value ? 'current' : ''">
            <span>{{ item.text }}</span><i class="oeuifont oeui-checkbox"></i>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="jump" @click="jump">跳过</div>
</template>

<script setup>
import { ref, getCurrentInstance, defineEmits, defineProps, reactive, watch, nextTick } from "vue"
const { proxy, context } = getCurrentInstance()
const OEUI = proxy.OEUI;
const http = proxy.http;
const emit = defineEmits(['change', 'jump'])
const props = defineProps({
  list: {
    type: Array,
    required: true
  },
  row: {
    type: Number,
    default: 3
  },
  title: {
    type: String,
    default: '请选择居住地'
  },
  current: {
    type: Array
  },
  type: {
    type: String,
    default: ''
  }
})

const width = ref('40')
const left = ref('16')
const ind = ref(0);
const dist1Text = ref('请选择');
const dist2Text = ref('请选择');
const dist3Text = ref('请选择');
const dist1 = ref('0');
const dist2 = ref('0');
const dist3 = ref('0');
const index1 = ref(0);
const index2 = ref(0);
const index3 = ref(0);
const status = ref(true);
const arrList = ref(props.list);
let { row, list, current } = reactive(props);

const type = ref(props.type)


const changeArea = () => {
  let arr;
  let obj1, obj2, obj3;
  let list1 = arrList.value[index1.value] || '';
  obj1 = list1 ? { i: list1.i || index1.value + 1, value: list1.value || '', text: list1.text || '' } : {};
  obj2 = {};
  obj3 = {};
  arr = [obj1];
  if (row >= 2) {
    let list2 = arrList.value[index1.value].children[index2.value] || '';
    obj2 = list2 ? { i: list2.i || index2.value + 1, value: list2.value || '', text: list2.text || '' } : {};
    arr.push(obj2)
  }
  if (row == 3) {
    let list3 = arrList.value[index1.value].children[index2.value].children[index3.value] || '';
    obj3 = list3 ? { i: list3.i || index3.value + 1, value: list3.value || '', text: list3.text || '' } : {};
    arr.push(obj3)
  }
  emit('change', { value: arr, type: type.value });
}

const setCurrent = async () => {
  if (current && current[0] > 0) {
    let index = arrList.value.map(item => item.value).indexOf(current[0]);
    dist1.value = current[0];
    index1.value = index > 0 ? index : 0;
    dist1Text.value = list[index1.value].text;
  }
  if (current && current[1] && current[1] != 0 && current[1] != '') {
    ind.value = 1;
    let index = arrList.value[index1.value].children.map(item => item.value).indexOf(current[1]);
    dist2.value = current[1];
    index2.value = index > 0 ? index : 0;
    dist2Text.value = list[index1.value].children[index2.value].text;
  }
  if (current && current[2] && row > 2 && current[2] != 0 && current[2] != '') {
    ind.value = 2;
    let index = arrList.value[index1.value].children[index2.value].children.map(item => item.value).indexOf(current[2]);
    dist3.value = current[2];
    index3.value = index > 0 ? index : 0;
    dist3Text.value = arrList.value[index1.value].children[index2.value].children[index3.value].text;
  }
  if (current) {
    await nextTick(() => {
      scrollCurrent();
      animationArea();
    })
  }
}
setCurrent();
const scrollCurrent = () => {
  let list = proxy.$refs.pane.querySelectorAll('.ullist');
  let top1 = list[0].querySelector('.current') ? list[0].querySelector('.current').offsetTop : '';
  let top2 = list[1].querySelector('.current') ? list[1].querySelector('.current').offsetTop : '';
  let top3 = list[2].querySelector('.current') ? list[2].querySelector('.current').offsetTop : '';
  if (top1 >= 0) {
    proxy.$refs.ulOne.scrollTop = top1
  }
  if (top2 >= 0) {
    proxy.$refs.ulTwo.scrollTop = top2
  }
  if (top3 >= 0) {
    proxy.$refs.ulThree.scrollTop = top3
  }
}

const selectDist = (e, i) => {
  width.value = proxy.$refs[e].offsetWidth;
  left.value = proxy.$refs[e].offsetLeft;
  ind.value = i;
}

const animationArea = () => {
  if (ind.value == 0) {
    width.value = proxy.$refs.distOne.offsetWidth;
    left.value = proxy.$refs.distOne.offsetLeft;
  } else if (ind.value == 1) {
    width.value = proxy.$refs.distTwo.offsetWidth;
    left.value = proxy.$refs.distTwo.offsetLeft;
  } else if (ind.value == 2) {
    width.value = proxy.$refs.distThree.offsetWidth;
    left.value = proxy.$refs.distThree.offsetLeft;
  }
}

const selectArea = async (item, index, type) => {
  if (type == 1) {
    index1.value = index;
    dist1.value = item.value;
    dist1Text.value = item.text;
    dist2.value = 0;
    index2.value = 0;
    dist2Text.value = "请选择";
    dist3.value = 0;
    index3.value = 0;
    dist3Text.value = "请选择";
    if (item.value == 0 || item.value == '') {
      await nextTick(() => {
        changeArea();
      })
      return;
    }
    ind.value = 1;
    proxy.$refs.ulTwo.scrollTop = 0;
  } else if (type == 2) {
    index2.value = index;
    dist2.value = item.value;
    dist2Text.value = item.text;
    if (row == 2 || arrList.value[index1.value].children[index2.value].children.length == 0) {
      await nextTick(() => {
        changeArea();
      })
      return;
    }
    dist3.value = 0;
    index3.value = 0;
    dist3Text.value = "请选择";
    ind.value = 2;
    proxy.$refs.ulThree.scrollTop = 0;
  } else if (type == 3) {
    index3.value = index;
    dist3.value = item.value;
    dist3Text.value = item.text;
    await nextTick(() => {
      changeArea();
    })
  }
  await nextTick(() => {
    animationArea();
  })
}

const jump = () => {
  emit('jump', type.value)
}

watch(() => props.list, (newValue) => {
  arrList.value = newValue
}, { deep: true, immediate: true })

watch(() => props.current, (newValue) => {
  if (newValue[0]) {
    current = newValue
    setCurrent();
  }
}, { deep: true, immediate: true })

watch(() => props.type, (newValue) => {
  type.value = newValue
}, { deep: true, immediate: true })



</script>

<style lang="scss" scoped>
.oe_area {
  position: relative;

  .tab_list {
    position: relative;
    display: flex;
    line-height: 1.28rem;
    height: 1.28rem;
    padding: 0 .16rem;
    z-index: 10;

    .tab {
      margin: 0 .267rem;
      font-size: .373rem;
      cursor: pointer;

      &.c9 {
        color: $color_9;
      }
    }

    .solid {
      display: inline-block;
      position: absolute;
      width: 1.067rem;
      height: .08rem;
      background: $color_main;
      left: 0;
      bottom: 0;
      transform: translateX(.427rem);
      transition: all .3s;
    }
  }

  .tab_pane {
    position: relative;
    height: 5.6rem;
    overflow: hidden;
    padding-top: .267rem;

    .ripple {
      width: 300%;
      height: 100%;
      display: flex;
      transition: all .3s;
      transform: translateX(0);

      .ullist {
        flex: 1;
        overflow-y: auto;

        li {
          padding: .267rem .4rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: $color_3;
          font-size: .373rem;

          i {
            display: none;
          }

          &.current {
            color: $color_main;

            i {
              font-size: .48rem;
              display: block;
            }
          }
        }
      }
    }
  }
}

.jump {
  color: $color_main;
  text-align: center;
  font-size: .373rem;
  position: fixed;
  bottom: .2667rem;
  left: 50%;
  transform: translateX(-50%);
}
</style>
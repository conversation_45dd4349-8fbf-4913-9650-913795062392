<!-- @format -->

<template>
  <div class="pop_custom" v-show="pickerDisplay" :style="['z-index:' + zindex]" :class="pickerStatus ? 'show' : 'hide'">
    <div class="mask" @click="selectSwitch(false)"></div>
    <div :class="bgrount ? 'content' : 'no_content'" :style="['background:' + bg]">
      <slot></slot>
      <iconpark-icon v-if="isClose" name="Close" size="24" fill="#4E5969" class="close" @click="selectSwitch(false)" />
    </div>
  </div>
</template>

<script setup>
  import { ref, defineProps, defineExpose, defineEmits } from 'vue'
  const emit = defineEmits(['callback'])
  const props = defineProps({
    bg: {
      type: String,
      default: 'background: linear-gradient(0deg, rgba(105, 69, 194, 0.05), rgba(105, 69, 194, 0.05)), #FFFBFE;'
    },
    zindex: {
      type: String,
      default: '400'
    },
    isClose: {
      type: Boolean,
      default: false
    },
    bgrount: {
      type: Boolean,
      default: true
    }
  })

  // 可优化为v-show的过渡属性 —— 待优化
  const pickerStatus = ref(false)
  const pickerDisplay = ref(false)
  const open = () => {
    pickerStatus.value = true
    pickerDisplay.value = true
  }
  const close = () => {
    pickerStatus.value = false
    pickerDisplay.value = false
  }

  defineExpose({
    open,
    close
  })
</script>
<style lang="scss" scoped>
  .pop_custom {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    .mask {
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.45);
      cursor: pointer;
      &.show {
        animation: show 0.3s ease;
      }
      &.hide {
        animation: hide 0.3s ease;
      }
    }
    .content {
      width: 100%;
      background: #fffbfe;
      border-radius: 15px 15px 0px 0px;
      position: absolute;
      left: 0;
      bottom: 0;
      // overflow: hidden;
    }
    .no_content {
      width: 100%;
      position: absolute;
      left: 0;
      bottom: 0;
      overflow: hidden;
    }
    &.show {
      .mask {
        animation: show 0.3s ease;
      }
      .content {
        animation: bottomShow 0.3s ease;
      }
    }
    &.hide {
      .mask {
        animation: hide 0.3s ease;
      }
      .content {
        animation: bottomHide 0.3s ease;
      }
    }
    .close {
      position: absolute;
      top: 15px;
      right: 15px;
    }
  }

  @keyframes show {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  @keyframes hide {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
  @keyframes bottomShow {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }
  @keyframes bottomHide {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(100%);
    }
  }
</style>

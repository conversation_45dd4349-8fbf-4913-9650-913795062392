<template>
  <div class="list">
    <dl class="item" :class="arr.includes(item.value) ? 'current' : ''" v-for="(item, i) in arrList" :key="i" @click="selectValue(item)">
      <dt><i class="oeuifont oeui-duoxuankuangdagou"></i></dt>
      <dd class="text">{{ item.text }}</dd>
    </dl>
  </div>
  <div class="btn_box">
    <div class="btn" @click="confirm"><i>提交</i></div>
  </div>
  <div class="jump" @click="jump">跳过</div>
</template>

<script setup>
import { ref, getCurrentInstance, defineEmits, defineProps, reactive, watch, nextTick } from "vue"
const { proxy } = getCurrentInstance()
const emit = defineEmits(['change', 'jump'])
const props = defineProps({
  list: {
    type: Array,
    required: true
  },
  current: {
    type: String,
    default: ''
  },
  row: {
    type: Number,
    default: 2
  },
  type: {
    type: String,
    default: ''
  }
})
const arr = ref([]);
const arrList = ref(props.list)

const type = ref(props.type)
const current = ref(props.current)
const setCurrent = () => {
  if (!current.value) return
  let curArr = current.value.split(',');
  arr.value = [];
  curArr.forEach((v) => {
    let index = arrList.value.map(item => item.value).indexOf(Number(v));
    if (index >= 0) {
      arr.value.push(arrList.value[index].value)
    }
  });
}
setCurrent()
const selectValue = (item) => {
  if (arr.value.includes(item.value)) {
    arr.value.splice(arr.value.indexOf(item.value), 1)
  } else {
    if (item.value == 0) {
      arr.value = [item.value];
    } else {
      if (arr.value.includes(0)) {
        arr.value.splice(arr.value.indexOf(0), 1)
      }
      arr.value.push(item.value);
    }
  }
}

const jump = () => {
  emit('jump', type.value)
}

const confirm = () => {
  emit('change', {
    value: arr.value,
    type: type.value
  })
}

watch(() => props.list, (newValue) => {
  arrList.value = newValue
}, { deep: true, immediate: true })

watch(() => props.current, (newValue) => {
  current.value = newValue
}, { deep: true, immediate: true })

watch(() => props.type, (newValue) => {
  type.value = newValue
}, { deep: true, immediate: true })

</script>

<style lang="scss" scoped>
.list {
  padding: 0 1.333rem;
  display: flex;
  flex-wrap: wrap;
  height: 5.3333rem;
  overflow-y: scroll;

  .item {
    width: 47%;
    height: 1.3333rem;
    border-radius: 1rem;
    border: 1px solid #e2e2e2;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    margin-bottom: .373rem;
    background: #ffffff;
    cursor: pointer;

    dt {
      margin-right: .133rem;
      width: .347rem;
      height: .347rem;
      line-height: .347rem;
      border: 1px solid #e2e2e2;
      text-align: center;
      margin-left: .267rem;
      border-radius: .08rem;
      color: #ffffff;

      .oeuifont {
        font-size: .32rem;
        font-weight: bold;
      }
    }

    .text {
      font-size: .373rem;
      text-align: center;
      line-height: .533rem;
      padding: .08rem .267rem .08rem 0;
    }

    &:nth-child(even) {
      margin-left: 6%;
    }

    &.current {
      background: $color_main;
      border: 1px solid $color_main;

      dt {
        background: #ffffff;
        color: $color_main;
      }

      .text {
        color: #ffffff;
      }
    }
  }
}

.btn_box {
  margin-top: .2667rem;
  padding: 0 1.333rem;

  .btn {
    position: relative;
    margin: 0 auto;
    height: 1.28rem;
    line-height: 1.28rem;
    border-radius: 1.28rem;
    text-align: center;
    color: #ffffff;
    font-size: .427rem;
    background: $color_main;

    i {
      position: relative;
      z-index: 1;
    }
  }
}

.jump {
  color: $color_main;
  text-align: center;
  font-size: .373rem;
  position: fixed;
  bottom: .2667rem;
  left: 50%;
  transform: translateX(-50%);
}
</style>
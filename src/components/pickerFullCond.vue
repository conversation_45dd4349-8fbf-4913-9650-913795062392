<template>
  <div class="oe_picker_full">
    <div class="content" :class="row < 3 ? 'padding' : ''">
      <div class="mask_top"></div>
      <div class="mask_center"></div>
      <div class="mask_bottom"></div>
      <div class="wheel_wrapper">
        <div class="wheel" v-if="row >= 1">
          <oeui-wheel 
          @changeWheel="oneWheel" 
          :index="oneIndex"
          :list="list">
            <ul>
              <li v-for="item in list" :key="item.i" :value="item.value">{{item.text}}</li>
            </ul>
          </oeui-wheel>
        </div>
        <div class="wheel" v-if="row >= 2">
          <oeui-wheel 
          @changeWheel="twoWheel" 
          :index="twoIndex"
          :list="list2">
            <ul>
              <li v-for="item in list2" :key="item.i" :value="item.value">{{item.text}}</li>
            </ul>
          </oeui-wheel>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, ref, watch } from 'vue';
import oeuiWheel from '../oeui/wheel.vue';
export default {
  props:{
    list:{
      type:Array,
      required: true
    },
    list2:{
      type:Array,
      required: true
    },
    row:{
      type:Number,
      default:2
    },
    current:{
      type:Array
    },
  },
  components:{
    oeuiWheel
  },
  setup(props,context) {
    const oneIndex = ref(0);
    const twoIndex = ref(0);
    const arrList = ref([]);
    const arrList2 = ref([]);
    let { current,list,list2,row } = reactive(props);
    arrList.value = list;
    arrList2.value = list2;
    
    const setCurrent = ()=>{
      if(current && current[0]){
        let index = arrList.value.map(item=>item.value).indexOf(current[0]);
        oneIndex.value = index > 0 ? index : 0;
      }
      if(current && current[1]){
        let index = arrList2.value.map(item=>item.value).indexOf(current[1]);
        twoIndex.value = index > 0 ? index : 0;
      }
    }
    setCurrent();

    const oneWheel = (index)=>{
      if(oneIndex.value != index){
        oneIndex.value = index;
        if(index > twoIndex.value){
          twoIndex.value = index;
        }
        confirm();
      }
    }

    const twoWheel = (index)=>{
      if(twoIndex.value != index){
        twoIndex.value = index;
        confirm();
      }
    }

    const confirm = () =>{
      let arr;
      let obj1,obj2;
      let list1 = arrList.value[oneIndex.value] || '';
      obj1 = list1 ? {i:list1.i || oneIndex.value+1,value:list1.value ||'',text:list1.text||''} : '';
      obj2 = {};
      arr = [obj1];
      if(row >= 2){
        let list2 = arrList2.value[twoIndex.value] || '';
        obj2 = list2 ? {i:list2.i || twoIndex.value+1,value:list2.value ||'',text:list2.text||''} : '';
        arr.push(obj2)
      }

      context.emit('change',arr);
    }

    watch(()=>props.list,(newValue)=>{
      arrList.value = newValue
    },{deep:true,immediate: true})

    watch(()=>props.list2,(newValue)=>{
      arrList2.value = newValue
    },{deep:true,immediate: true})

    watch(()=>props.current,(newValue)=>{
      if(newValue[0]){
        current = newValue
        setCurrent();
      }
    },{deep:true,immediate: true})

    return {
      oneIndex,
      twoIndex,
      confirm,
      oneWheel,
      twoWheel,
    }
  }
}
</script>
<style lang='scss' scoped>
.oe_picker_full{
  padding:.8rem;
  .content{
    position: relative;
    height:4.667rem;
    margin-top:.4rem;
    &.padding{
      padding:0 .533rem;
      .mask_top,.mask_center,.mask_bottom{
        left:.533rem;
        right:.533rem;
      }
    }
    .mask_top{
      position: absolute;
      top:0;
      left:0;
      right:0;
      z-index:10;
      height:1.813rem;
      pointer-events: none;
      transform: translateZ(0);
      background: linear-gradient(0deg, #f4f5f75d, #f4f5f7d0);
      &::after{
        content: "";
        border-bottom: 1px solid #f7f7f7;
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        transform-origin: 0 top;
      }
    }
    .mask_center{
      position: absolute;
      top:1.813rem;
      left:0;
      right:0;
      height:.96rem;
      background: $color_main;
      opacity:.1;
      border-radius:.027rem;
    }
    .mask_bottom{
      position: absolute;
      bottom:.08rem;
      left:0;
      right:0;
      z-index:10;
      height:1.813rem;
      pointer-events: none;
      transform: translateZ(0);
      background: linear-gradient(0deg, #f4f5f75d, #f4f5f7d0);
      &::after{
        content: "";
        border-top: 1px solid #f7f7f7;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        transform-origin: 0 top;
      }
    }
    .wheel_wrapper{
      position: relative;
      display: flex;
      padding:0 16px;
      z-index:1;
      .wheel{
        flex:1;
        height:4.533rem;
        flex-basis: 1e-9px;
        width: 1%;
        overflow: hidden;
        font-size:.427rem;
        cursor:pointer;
        ul{
          padding:1.813rem 0;
          line-height:36px;
          li{
            height:36px;
            color:$color_3;
            overflow: hidden;
            text-align: center;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
<!-- @format -->

<template>
  <div class="h50"></div>
  <div class="pf lbr h50 bg_f w100per bo_t01 flex_dc z100 tab" @click="initMsg">
    <router-link class="bar_item flex_1 flex_dc" :to="{ name: 'Home' }" :class="{ current: tab_active == 'Home' }">
      <div class="flex_dc flex_v">
        <span>
          <img v-if="tab_active == 'Home'" src="@/assets/images/tabbar/tab1s.png" alt="" />
          <img v-else src="@/assets/images/tabbar/tab1.png" alt="" />
        </span>
        <p>工作台</p>
      </div>
    </router-link>
    <!-- <router-link class="bar_item flex_1 flex_dc" :to="{ name: 'Income' }" :class="{ current: tab_active == 'Income' }">
      <div class="flex_dc flex_v">
        <span>
          <img v-if="tab_active == 'Income'" src="@/assets/images/tabbar/tab2s.png" alt="" />
          <img v-else src="@/assets/images/tabbar/tab2.png" alt="" />
        </span>
        <p>收益</p>
      </div>
    </router-link> -->
    <router-link class="bar_item flex_1 flex_dc" :to="{ name: 'Msg' }" :class="{ current: tab_active == 'Msg' }">
      <div class="flex_dc flex_v pr">
        <span>
          <img v-if="tab_active == 'Msg'" src="@/assets/images/tabbar/tab3s.png" alt="" />
          <img v-else src="@/assets/images/tabbar/tab3.png" alt="" />
        </span>
        <p>消息</p>
        <i class="tip" v-if="newMsg > 0"></i>
      </div>
    </router-link>
    <router-link class="bar_item flex_1 flex_dc" :to="{ name: 'Cp' }" :class="{ current: tab_active == 'Cp' }">
      <div class="flex_dc flex_v">
        <span>
          <img v-if="tab_active == 'Cp'" src="@/assets/images/tabbar/tab4s.png" alt="" />
          <img v-else src="@/assets/images/tabbar/tab4.png" alt="" />
        </span>
        <p>我的</p>
      </div>
    </router-link>
  </div>
</template>

<script setup>
import { computed, ref, getCurrentInstance, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { getNewMsg } from '@/api/msg.js'
const { proxy } = getCurrentInstance()
const store = useStore()
const config = computed(() => store.state.config)
const route = useRoute()

const tab_active = ref('Home')
if (route.meta.level == 1) {
  tab_active.value = route.name
}

const newMsg = ref(0)
const initMsg = () => {
  getNewMsg().then(res => {
    if (res.ret == 1) {
      newMsg.value = res.result
    }
  })
}
onMounted(() => {
  initMsg()
})
</script>
<style lang="scss" scoped>
.tab {
  font-size: 0.3733rem;
  line-height: 1.6rem;
  height: 1.6rem;
  font-weight: 500;
}
.bo_t01 {
  box-shadow: 0px -1px 0px 0px #f2f4f5;
}

.bar_item {
  span {
    width: 0.64rem;
    height: 0.64rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  p {
    color: #31293b;
    font-size: 0.2667rem;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: normal;
    line-height: 0.3733rem;
  }
}
.tip {
  width: 0.16rem;
  height: 0.16rem;
  background: #e93321;
  border-radius: 50%;
  position: absolute;
  right: -0.0533rem;
  top: 0;
}
</style>

<template>
  <pop-custom class="popMore" ref="popMore" bg="#FFFFFF">
    <ul>
      <li @click="selectPicker(item, i)" :class="{ color_main: item.value == cur && is_current }" v-for="(item, i) of list" :key="i">
        {{ item.text }}
      </li>
      <li @click="close">取消</li>
    </ul>
  </pop-custom>
</template>

<script setup>
import { ref, getCurrentInstance, defineExpose, defineEmits, defineProps, watch } from 'vue'
import popCustom from '@/components/pop_custom.vue'
const { proxy } = getCurrentInstance()
const emit = defineEmits(['callback'])
const props = defineProps({
  list: {
    type: Array,
    required: true
  },
  current: {
    type: Number,
    default: 0
  },
  is_current: {
    type: Boolean,
    default: true
  }
})
const cur = ref(props.current)
const open = () => {
  proxy.$refs.popMore.selectSwitch(true)
}
const close = () => {
  proxy.$refs.popMore.selectSwitch(false)
}
const selectPicker = (item, i) => {
  close()
  emit('callback', i, item)
  cur.value = item.value
}
watch(
  () => props.current,
  newv => {
    cur.value = newv
  }
)
defineExpose({ open })
</script>
<style lang="scss" scoped>
.popMore {
  ul {
    width: 335px;
    // min-height: 150px;
    padding: 16px 20px;
    box-shadow: border-box;
    margin: 0 auto;

    li {
      width: 335px;
      height: 42px;
      text-align: center;
      line-height: 42px;
      font-size: 14px;
      color: #000000;

      &:not(:last-child) {
        border-bottom: 1px solid #f2f3f5;
      }
    }
  }
}
</style>

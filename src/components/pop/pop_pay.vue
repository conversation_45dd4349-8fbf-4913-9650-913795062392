<template>
  <pop-custom class="popMore" ref="popMore" zindex="400" :isClose="false">
    <span class="pt8 pa rt pr8" @click="close">
      <span class="img_20">
        <img src="@/assets/images/home/<USER>" class="ob_c br" alt="" />
      </span>
    </span>
    <div class="pay bg_wxcardlg brt10">
      <p class="tit fz17 fb lh1 color_221 mt24_5">请选择支付方式</p>
      <div class="meal lh1 fz30 fb t_center mt26">
        <p class="money lh1">￥{{ money }}</p>
        <p class="grade lh1 fz14 color_5 fn mt14_5">{{ desc }}</p>
      </div>
      <div class="methods">
        <div class="item bsb bo_btran_08" @click="selectRadioStatus(1)">
          <div class="name">
            <span class="img_25 mr7">
              <img src="@/assets/images/home/<USER>" class="ob_c" alt="" />
            </span>
            <span class="fz15 color_3 lh25">在线支付</span>
          </div>
          <span class="img_17">
            <img src="@/assets/images/home/<USER>" v-show="radioStatus != 2" class="ob_c" alt="" />
            <img src="@/assets/images/home/<USER>" v-show="radioStatus == 2" class="ob_c" alt="" />
          </span>
          <!-- <span class="radio" v-show="radioStatus != 1"></span>
          <iconpark-icon name="Vector-5lklh301" size="20" fill="#6945C2" v-show="radioStatus == 1"></iconpark-icon> -->
        </div>
        <div class="item bsb" v-if="useBalance && loginInfo.money > money" @click="selectRadioStatus(2)">
          <div class="name">
            <span class="img_25 mr7">
              <img src="@/assets/images/home/<USER>" class="ob_c" alt="" />
            </span>
            <span class="fz15 color_3 lh25">余额：{{ loginInfo.money }}</span>
          </div>
          <span class="img_17">
            <img src="@/assets/images/home/<USER>" v-show="radioStatus != 1" class="ob_c" alt="" />
            <img src="@/assets/images/home/<USER>" v-show="radioStatus == 1" class="ob_c" alt="" />
          </span>
        </div>
      </div>
      <span class="btn cursor h43 lh43 color_f fb fz16 bg_vipsend br100 t_center" v-debounce="[send, 'click']">确定</span>
    </div>
  </pop-custom>
</template>
<script setup>
import { ref, getCurrentInstance, defineExpose, defineProps, defineEmits, computed, watch } from 'vue'
import popCustom from '@/components/pop_custom.vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
const { proxy } = getCurrentInstance()
const emit = defineEmits(['sendPay', 'sendPayBalance'])
const OEUI = proxy.OEUI
const store = useStore()
const vip_grade = ref(1)
const vip_meal = ref(1)
const router = useRouter()
const loginInfo = computed(() => store.state.loginInfo)
/**
 * 1 在线
 * 2 余额
 */
const radioStatus = ref(1)
const props = defineProps({
  useBalance: {
    type: Boolean,
    default: false
  },
  money: 0.0,
  desc: ''
})
const send = () => {
  let type = radioStatus.value
  if (type == 1) {
    emit('sendPay')
  } else if (type == 2) {
    emit('sendPayBalance')
  }
}
const selectRadioStatus = val => {
  if (val == 2 && loginInfo.value.money < props.money) {
    return OEUI.toast('余额不足')
  } else {
    radioStatus.value = val
  }
}

const open = () => {
  proxy.$refs.popMore.selectSwitch(true)
}
const close = () => {
  proxy.$refs.popMore.selectSwitch(false)
}

watch(
  () => loginInfo.value,
  newInfo => {
    if (newInfo.userid > 0 && newInfo.money < props.money) {
      radioStatus.value = 2
    }
  },
  { immediate: true }
)

defineExpose({ open, close })
</script>
<style lang="scss" scoped>
.popMore {
  .pay {
    width: 375px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;

    .methods {
      width: 316px;
      background: #ffffff;
      border-radius: 10px;
      padding: 0 25px;
      box-sizing: border-box;
      margin-top: 20px;

      .item {
        height: 75px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .name {
          display: flex;
        }
      }
    }
  }

  .btn {
    width: 9.2rem;
    margin: 0.72rem auto 0.6533rem;
  }
}
</style>

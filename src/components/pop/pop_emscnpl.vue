<template>
  <pop-custom class="popcustom" ref="popcustom" bg="#FFFFFF" :isClose="true">
    <div class="customer_emscnpl">
      <p class="color_font5 fz20 fw5">关注公众号</p>
      <p class="fz14 color_font4 mt20">由公众号转账，请先关注公众号</p>
      <img
        class="qrcode mt20"
        src="../../assets/images/qrcode.png"
        alt="qrcode"
      />
      <p class="fz14 color_font3 mt20">长按扫码关注公众号</p>
    </div>
  </pop-custom>
</template>

<script setup>
import popCustom from "@/components/pop_custom.vue";
import { getCurrentInstance, defineExpose } from "vue";
const { proxy } = getCurrentInstance();
const open = () => {
  proxy.$refs.popcustom.selectSwitch(true);
};
const close = () => {
  proxy.$refs.popcustom.selectSwitch(false);
};
defineExpose({ open });
</script>
<style lang="scss" scoped>
.popcustom {
  .customer_emscnpl {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 282px;
    padding-top: 20px;
    box-sizing: border-box;
    .qrcode {
      width: 100px;
      height: 100px;
      object-fit: cover;
    }
    .close {
      position: absolute;
      top: 15px;
      right: 15px;
    }
  }
}
</style>

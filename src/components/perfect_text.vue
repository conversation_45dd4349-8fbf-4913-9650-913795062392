<template>
  <div class="edit_text">
    <div class="input_box">
      <input type="text" :placeholder="inputText" v-model="current">
    </div>
    <div class="btn_box">
      <div class="btn" @click="confirm"><i>提交</i></div>
    </div>
    <div class="jump" @click="jump" v-if="isJump">跳过</div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, defineEmits, defineProps, reactive, watch } from "vue"
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI;
const http = proxy.http;
const emit = defineEmits(['change', 'jump'])
const props = defineProps({

  inputText: {
    type: String,
    default: '请输入'
  },
  isJump: {
    type: Boolean,
    default: true
  },
  current: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: ''
  }
})

const current = ref(props.current)
const type = ref(props.type)


const jump = () => {
  emit('jump', type.value)
  current.value = ''
}

const confirm = () => {

  emit('change', {
    value: current.value,
    type: type.value
  })
  current.value = ''
}

watch(() => props.current, (newValue) => {
  current.value = ''
  current.value = newValue
}, { deep: true, immediate: true })
watch(() => props.type, (newValue) => {
  type.value = newValue
}, { deep: true, immediate: true })

</script>

<style lang="scss" scoped>
.edit_text {
  padding: 0 1.3333rem;


  .input_box {
    padding-top: 1.3333rem;
    padding-bottom: .8rem;
    height: 1rem;

    input {
      width: 100%;
      height: 100%;
      font-size: .427rem;
      text-align: center;
      border-bottom: 1px solid #E5E6EB;
    }
  }



  .btn_box {
    margin-top: .2667rem;
    padding: .4rem 0 .8rem;

    .btn {
      position: relative;
      margin: 0 auto;
      height: 1.28rem;
      line-height: 1.28rem;
      border-radius: 1.28rem;
      text-align: center;
      color: #ffffff;
      font-size: .427rem;
      background: $color_main;

      i {
        position: relative;
        z-index: 1;
      }
    }
  }

  .jump {
    color: $color_main;
    text-align: center;
    font-size: .373rem;
    position: fixed;
    bottom: .2667rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
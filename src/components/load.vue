<template>
  <div class="loading">
    <div :style="{ background: color }"></div>
    <div :style="{ background: color }"></div>
    <div :style="{ background: color }"></div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
const props = defineProps({
  color: ''
})
</script>
<style lang="scss" scoped>
.loading,
.loading > div {
  position: relative;
  box-sizing: border-box;
}

.loading {
  display: block;
  font-size: 0;
  color: #000;
}

.loading > div {
  display: inline-block;
  float: none;
  background-color: #7d68ff;
}

.loading {
  width: 54px;
  height: 18px;
}

.loading > div:nth-child(1) {
  animation-delay: -200ms;
}

.loading > div:nth-child(2) {
  animation-delay: -100ms;
}

.loading > div:nth-child(3) {
  animation-delay: 0ms;
}

.loading > div {
  width: 10px;
  height: 10px;
  margin: 4px;
  border-radius: 100%;
  animation: ball-pulse 1s ease infinite;
}

@keyframes ball-pulse {
  0%,
  60%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  30% {
    opacity: 0.1;
    transform: scale(0.01);
  }
}
</style>

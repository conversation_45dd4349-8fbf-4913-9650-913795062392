<template>
  <div class="content">
    <div class="mask_top"></div>
    <div class="mask_center"></div>
    <div class="mask_bottom"></div>
    <div class="wheel_wrapper">
      <div class="wheel" v-if="row >= 1">
        <oeui-wheel @changeWheel="oneWheel" :index="oneIndex" :list="arrList">
          <ul>
            <li v-for="item in arrList" :key="item.i" :value="item.value">{{ item.text }}</li>
          </ul>
        </oeui-wheel>
      </div>
      <div class="wheel" v-if="row >= 2">
        <oeui-wheel @changeWheel="twoWheel" :index="twoIndex" :list="arrList2">
          <ul>
            <li v-for="item in arrList2" :key="item.i" :value="item.value">{{ item.text }}</li>
          </ul>
        </oeui-wheel>
      </div>
    </div>
  </div>
  <div class="btn_box">
    <div class="btn" @click="confirm"><i>提交</i></div>
  </div>
  <div class="jump" @click="jump">跳过</div>
</template>

<script setup>
import oeuiWheel from '@/oeui/wheel.vue';
import { ref, getCurrentInstance, defineEmits, defineProps, reactive, watch } from "vue"
const { proxy } = getCurrentInstance()
const emit = defineEmits(['change', 'jump'])
const props = defineProps({
  list: {
    type: Array,
    required: true
  },
  list2: {
    type: Array,
    required: true
  },
  current: {
    type: Array,
    default: []
  },
  type: {
    type: String,
    default: ''
  },
  row: {
    type: Number,
    default: 2
  },
})

const oneIndex = ref(0);
const twoIndex = ref(0);
const arrList = ref([]);
const arrList2 = ref([]);
let { current, list, list2, row } = reactive(props);
arrList.value = list;
arrList2.value = list2;
const type = ref(props.type)

const setCurrent = () => {
  if (current && current[0]) {
    let index = arrList.value.map(item => item.value).indexOf(current[0]);
    oneIndex.value = index > 0 ? index : 0;
  }
  if (current && current[1]) {
    let index = arrList2.value.map(item => item.value).indexOf(current[1]);
    twoIndex.value = index > 0 ? index : 0;
  }
}
setCurrent();

const oneWheel = (index) => {
  oneIndex.value = index;
  if (index > twoIndex.value) {
    twoIndex.value = index;
  }
}

const twoWheel = (index) => {
  twoIndex.value = index;
}


const jump = () => {
  emit('jump', type.value)
}

const confirm = () => {

  let val1 = arrList.value[oneIndex.value] ? arrList.value[oneIndex.value].value : 0;
  let val2 = arrList2.value[twoIndex.value] ? arrList2.value[twoIndex.value].value : 0;
  emit('change', {
    value: [val1, val2],
    type: type.value
  })
}

watch(() => props.list, (newValue) => {
  arrList.value = newValue
}, { deep: true, immediate: true })

watch(() => props.list2, (newValue) => {
  arrList2.value = newValue
}, { deep: true, immediate: true })

watch(() => props.current, (newValue) => {
  if (newValue) {
    current = newValue;
    setCurrent();
  }
}, { deep: true, immediate: true })

watch(() => props.type, (newValue) => {
  type.value = newValue
}, { deep: true, immediate: true })

</script>

<style lang="scss" scoped>
.content {
  position: relative;
  height: 4.667rem;
  margin-top: .4rem;

  &.padding {
    padding: 0 1.467rem;

    .mask_top,
    .mask_center,
    .mask_bottom {
      left: 1.467rem;
      right: 1.467rem;
    }
  }

  .mask_top {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    height: 1.813rem;
    pointer-events: none;
    transform: translateZ(0);
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8));

    &::after {
      content: "";
      border-bottom: 1px solid #ebebeb;
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      transform-origin: 0 top;
    }
  }

  .mask_center {
    position: absolute;
    top: 1.813rem;
    left: 0;
    right: 0;
    height: .96rem;
    background: #f7f7f7;
  }

  .mask_bottom {
    position: absolute;
    bottom: .08rem;
    left: 0;
    right: 0;
    z-index: 10;
    height: 1.813rem;
    pointer-events: none;
    transform: translateZ(0);
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8));

    &::after {
      content: "";
      border-top: 1px solid #ebebeb;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      transform-origin: 0 top;
    }
  }

  .wheel_wrapper {
    position: relative;
    display: flex;
    padding: 0 16px;
    z-index: 1;

    .wheel {
      flex: 1;
      height: 4.533rem;
      flex-basis: 1e-9px;
      width: 1%;
      overflow: hidden;
      font-size: .427rem;
      cursor: pointer;

      ul {
        padding: 1.813rem 0;
        line-height: 36px;

        li {
          height: 36px;
          color: $color_3;
          overflow: hidden;
          text-align: center;
          white-space: nowrap;
        }
      }
    }
  }
}

.btn_box {
  margin-top: .5333rem;
  padding: 0 1.467rem;

  .btn {
    position: relative;
    margin: 0 auto;
    height: 1.28rem;
    line-height: 1.28rem;
    border-radius: 1.28rem;
    text-align: center;
    color: #ffffff;
    font-size: .427rem;
    background: $color_main;

    i {
      position: relative;
      z-index: 1;
    }
  }
}

.jump {
  color: $color_main;
  text-align: center;
  font-size: .373rem;
  position: fixed;
  bottom: .2667rem;
  left: 50%;
  transform: translateX(-50%);
}


@keyframes show {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes hide {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes bottomShow {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes bottomHide {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(100%);
  }
}
</style>
<template>
  <template v-if="arrList.length > 6">
    <div class="content" :class="row < 3 ? 'padding' : ''">
      <div class="mask_top"></div>
      <div class="mask_center"></div>
      <div class="mask_bottom"></div>
      <div class="wheel_wrapper">
        <div class="wheel" v-if="row >= 1">
          <oeui-wheel @changeWheel="oneWheel" :index="oneIndex" :list="arrList">
            <ul>
              <li v-for="item in arrList" :key="item.i" :value="item.value">{{ item.text }}</li>
            </ul>
          </oeui-wheel>
        </div>
      </div>
      <div class="btn_box">
        <div class="btn" @click="confirm"><i>提交</i></div>
      </div>
      <div class="jump" @click="jump" v-if="isJump">跳过</div>
    </div>
  </template>
  <template v-else>
    <div class="select_box2">
      <template v-for="(item, i) in arrList" :key="i">
        <div class="btn" :class="current == item.value ? 'current' : ''" @click="selectVal(item.value)">
          {{ item.text }}
        </div>
      </template>
    </div>
    <div class="jump" @click="jump" v-if="isJump">跳过</div>
  </template>
</template>

<script setup>
import oeuiWheel from '@/oeui/wheel.vue';
import { ref, getCurrentInstance, defineEmits, defineProps, reactive, watch } from "vue"
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI;
const http = proxy.http;
const emit = defineEmits(['change', 'jump'])
const props = defineProps({
  list: {
    type: Array,
    required: true
  },
  row: {
    type: Number,
    default: 1
  },
  title: {
    type: String,
    default: '请选择居住地'
  },
  text: {
    type: String,
    default: ''
  },
  current: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: ''
  },
  isJump: {
    type: Boolean,
    default: true
  },
})

const oneIndex = ref(0);
const twoIndex = ref(0);
const threeIndex = ref(0);
const arrList = ref([]);
const current = ref(props.current)
const type = ref(props.type)

let { list, row } = reactive(props);
arrList.value = list;

const setCurrent = () => {
  if (current.value) {
    let index = arrList.value.map(item => item.value).indexOf(current.value);
    oneIndex.value = index > 0 ? index : 0;
  }
}
setCurrent();

const oneWheel = (index) => {
  oneIndex.value = index
}


const selectVal = (val) => {
  current.value = val
  emit('change', {
    value: val,
    type: type.value
  })

}

const jump = () => {
  emit('jump', type.value)
}

const confirm = () => {
  let val = arrList.value[oneIndex.value] ? arrList.value[oneIndex.value].value : 0
  emit('change', {
    value: val,
    type: type.value
  })
}


watch(() => props.list, (newValue) => {
  arrList.value = newValue
}, { deep: true, immediate: true })

watch(() => props.current, (newValue) => {
  current.value = newValue
}, { deep: true, immediate: true })
watch(() => props.type, (newValue) => {
  type.value = newValue
}, { deep: true, immediate: true })



</script>

<style lang="scss" scoped>
.content {
  position: relative;
  height: 4.667rem;
  margin-top: .4rem;
  padding: 0 .5333rem;

  &.padding {
    padding: 0 1.467rem;

    .mask_top,
    .mask_center,
    .mask_bottom {
      left: 1.467rem;
      right: 1.467rem;
    }
  }

  .mask_top {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    height: 1.813rem;
    pointer-events: none;
    transform: translateZ(0);
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8));

    &::after {
      content: "";
      border-bottom: 1px solid #ebebeb;
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      transform-origin: 0 top;
    }
  }

  .mask_center {
    position: absolute;
    top: 1.813rem;
    left: 0;
    right: 0;
    height: .96rem;
    background: #f7f7f7;
  }

  .mask_bottom {
    position: absolute;
    bottom: .08rem;
    left: 0;
    right: 0;
    z-index: 10;
    height: 1.813rem;
    pointer-events: none;
    transform: translateZ(0);
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8));

    &::after {
      content: "";
      border-top: 1px solid #ebebeb;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      transform-origin: 0 top;
    }
  }

  .wheel_wrapper {
    position: relative;
    display: flex;
    padding: 0 16px;
    z-index: 1;

    .wheel {
      flex: 1;
      height: 4.533rem;
      flex-basis: 1e-9px;
      width: 1%;
      overflow: hidden;
      font-size: .427rem;
      cursor: pointer;

      ul {
        padding: 1.813rem 0;
        line-height: 36px;

        li {
          height: 36px;
          color: $color_3;
          overflow: hidden;
          text-align: center;
          white-space: nowrap;
        }
      }
    }
  }

  .jump {
    color: $color_main;
    text-align: center;
    font-size: .373rem;
    position: fixed;
    bottom: .2667rem;
    left: 50%;
    transform: translateX(-50%);
  }

  .btn_box {
    margin-top: .5333rem;

    .btn {
      position: relative;
      margin: 0 auto;
      height: 1.28rem;
      line-height: 1.28rem;
      border-radius: 1.28rem;
      text-align: center;
      color: #ffffff;
      font-size: .427rem;
      background: $color_main;

      i {
        position: relative;
        z-index: 1;
      }
    }
  }
}


@keyframes show {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes hide {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes bottomShow {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes bottomHide {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(100%);
  }
}

.select_box {
  padding: 1.333rem .747rem 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .btn {
    margin-bottom: .533rem;
    width: 4rem;
    font-size: .427rem;
    color: #333333;
    background: #ffffff;
    border-radius: 1rem;
    padding: .373rem 0;
    text-align: center;
    cursor: pointer;

    &.current {
      background: $color_main;
      color: #ffffff;
    }
  }
}

.select_box2 {
  padding-top: .5333rem;
  text-align: center;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 250px;
  padding: 0 .5333rem;

  .btn {
    display: block;
    height: 1.333rem;
    border-radius: 1rem;
    color: $color_main;
    font-size: .427rem;
    cursor: pointer;
    margin: 0 auto .4rem;
    padding: .373rem 0;
    box-sizing: border-box;
    border: 1px solid $color_main;

    &.current {
      background: $color_main;
      color: #ffffff;
    }
  }
}

.jump {
  color: $color_main;
  text-align: center;
  font-size: .373rem;
  position: fixed;
  bottom: .2667rem;
  left: 50%;
  transform: translateX(-50%);
}
</style>
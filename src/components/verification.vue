<!-- @format -->

<template>
  <oeui-popup ref="smsVerif" mode="bottom" :round="true">
    <div class="oe_sms">
      <span class="close iconfont icon-cha" @click="close"></span>
      <div class="head">
        <img :src="topImg" />
        <span @click="getSmsList">刷新</span>
      </div>
      <div class="neiron">
        <div class="item" v-for="(item, i) in imgList" :key="i" :class="i == index ? status : ''" @click="selectCode(item.validcode, i)">
          <img class="img" :src="item.img" />
          <img class="ico" src="../assets/images/m_ico.png" />
        </div>
      </div>
    </div>
  </oeui-popup>
</template>

<script>
  import { getCurrentInstance, onMounted, ref } from 'vue'
  import oeuiPopup from '../oeui/popup.vue'
  export default {
    components: {
      oeuiPopup
    },
    setup(props, context) {
      const { proxy } = getCurrentInstance()
      const http = proxy.http
      const OEUI = proxy.OEUI
      const topImg = ref('')
      const imgList = ref([])
      const index = ref(1000)
      const status = ref()
      const start = () => {
        getSmsList()
      }

      const getSmsList = () => {
        OEUI.loading.show()
        http
          .post('', {
            m: 'vuewap',
            c: 'picker',
            a: 'brush'
          })
          .then(res => {
            OEUI.loading.hide()
            if (res.ret == 1) {
              topImg.value = res.result.wzpic
              imgList.value = res.result.data
              index.value = 1000
              status.value = ''
              proxy.$refs.smsVerif.open()
            } else {
              OEUI.toast({
                text: res.msg || '系统繁忙，请稍后再试'
              })
            }
          })
      }

      const selectCode = (code, i) => {
        OEUI.loading.show()
        index.value = i
        context.emit('callback', code)
      }
      const success = () => {
        status.value = 'success'
        setTimeout(() => {
          close()
        }, 500)
      }
      const error = () => {
        status.value = 'error'
        setTimeout(() => {
          getSmsList()
        }, 500)
      }
      const close = () => {
        proxy.$refs.smsVerif.close()
      }
      return {
        topImg,
        imgList,
        start,
        selectCode,
        getSmsList,
        close,
        index,
        status,
        success,
        error
      }
    }
  }
</script>
<style lang="scss" scoped>
  .oe_sms {
    position: relative;
    padding: 0.267rem 0 0.133rem;
    .close {
      position: absolute;
      right: 0.08rem;
      top: 0.08rem;
      padding: 0.267rem;
      color: #999999;
      cursor: pointer;
    }
    .head {
      padding: 0 0.4rem;
      padding-bottom: 0.267rem;
      border-bottom: 1px solid #f7f7f7;
      img {
        vertical-align: middle;
      }
      span {
        display: inline-block;
        line-height: 0.667rem;
        padding: 0 0.267rem;
        font-size: 0.373rem;
        cursor: pointer;
        vertical-align: middle;
      }
    }
    .neiron {
      padding: 0.4rem 0 0 0.32rem;
      display: flex;
      flex-wrap: wrap;
      .item {
        position: relative;
        width: 2.133rem;
        height: 2.133rem;
        margin-right: 0.16rem;
        margin-bottom: 0.16rem;
        cursor: pointer;
        border: 2px solid transparent;
        .img {
          width: 100%;
          height: 100%;
        }
        .ico {
          width: 0.613rem;
          height: 0.72rem;
          position: absolute;
          left: 50%;
          top: 50%;
          margin-left: -0.307rem;
          margin-top: -0.36rem;
          display: none;
        }
        &.success {
          border: 2px solid #45b97c;
          .ico {
            display: block;
          }
        }
        &.error {
          border: 2px solid #f40;
        }
      }
    }
  }
</style>

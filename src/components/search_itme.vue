<!-- @format -->

<template>
  <div class="search_time" @click="close" v-show="is_time">
    <div class="content_box oh" :id="'search_time_' + type" style="height: 120px">
      <div v-for="item in list" :key="item.val" class="item flex flex_ac flex_jsb" @click.stop="selectItem(item.val)">
        <p class="name">{{ item.name }}</p>
        <span v-if="current == item.val" class="iconfont icon-duihao"></span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, defineProps, defineExpose, defineEmits } from 'vue'
  const { proxy } = getCurrentInstance()
  const emit = defineEmits(['changItem', 'close'])
  const props = defineProps({
    list: {
      type: Array,
      required: true
    },
    current: {
      type: Number,
      default: 0
    },
    type: {
      type: String,
      default: '1'
    }
  })

  const is_time = ref(false)

  const open = () => {
    if (!is_time.value) {
      is_time.value = true
      if(props.list.length > 8){
        var height = 360
      }else{
        var height = (props.list.length * 40) + 20
      }
      setTimeout(() => {
        document.querySelector('#search_time_' + props.type).style.height = height + 'px'
      }, 0)
    } else {
      close()
    }
  }

  const selectItem = val => {
    close(() => {
      emit('changItem', val)
    })
  }

  const close = callback => {
    emit('close')
    document.querySelector('#search_time_' + props.type).style.height = '0'
    setTimeout(() => {
      is_time.value = false
      if (typeof callback == 'function') callback()
    }, 250)
  }

  defineExpose({
    open,
    close
  })
</script>

<style lang="scss" scoped>
  .search_time {
    position: fixed;
    width: 100%;
    height: calc(100% - 2.4rem);
    top: 2.56rem;
    background: rgba(0, 0, 0, 0.2);
    transition: height 0.3s ease-in-out;

    .content_box {
      transition: height 0.3s ease-in-out;
      background: #fff;
      border-radius: 0.32rem;
      margin-top: -0.2667rem;
      padding: 0.2133rem 0.4267rem;
      overflow-y: scroll;
      .item {
        height: 1.0667rem;
        .name {
          font-size: 0.3733rem;
          line-height: 1.0667rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: normal;
          color: #2a2546;
        }
        .iconfont {
          color: $color_main;
          font-size: 0.32rem;
        }
      }
    }
  }
</style>

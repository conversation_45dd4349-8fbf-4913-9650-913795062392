<!-- @format -->

<template>
  <div class="oe_nav" :class="bsd ? 'bsd' : ''" :style="{ background: background }">
    <span class="back" @click="back" v-if="isback">
      <i class="iconfont icon-zuo" :style="{ color: color }"></i>
    </span>
    <h1 class="title" :style="{ color: color }">{{ title }}</h1>
    <span class="more" v-if="ismore" @click="eventMore" :style="{ color: moreColor }">{{ moretext }}</span>
  </div>
  <div style="height: 1.28rem" v-if="istop"></div>
</template>
<script>
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  export default {
    emits: ['navMore'],
    props: {
      title: {
        type: String,
        default: ''
      },
      background: {
        type: String,
        default: '#ffffff'
      },
      color: {
        type: String,
        default: ''
      },
      bsd: {
        type: Boolean,
        default: false
      },
      istop: {
        type: Boolean,
        default: true
      },
      isback: {
        type: Boolean,
        default: true
      },
      ismore: {
        type: Boolean,
        default: false
      },
      moretext: {
        type: String,
        default: ''
      },
      moreColor: {
        type: String,
        default: '#C9CDD4'
      }
    },
    setup(props, context) {
      const router = useRouter()
      const store = useStore()
      const back = () => {
        if (window.history.length <= 1) {
          router.replace({ path: '/' })
        } else {
          router.back()
        }
      }
      const eventMore = () => {
        context.emit('navMore')
      }

      return {
        back,
        eventMore
      }
    }
  }
</script>
<style lang="scss" scoped>
  @import '../assets/iconfont/iconfont.css';
  .oe_nav {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    height: 1.28rem;
    z-index: 100;
    max-width: 750px;
    margin: 0 auto;
    .back {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      line-height: 1.28rem;
      padding: 0 0.267rem;
      cursor: pointer;
      z-index: 1;
      .iconfont {
        font-size: 0.48rem;
        color: #333333;
        vertical-align: top;
      }
    }
    .title {
      line-height: 1.28rem;
      text-align: center;
      color: #333333;
      font-size: 0.427rem;
      font-weight: 500;
    }
    .more {
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      line-height: 1.28rem;
      padding: 0 0.267rem;
      cursor: pointer;
      font-size: 0.373rem;
    }
    &.bsd {
      box-shadow: 0 0.08rem 0.453rem 0 rgba(235, 235, 235, 0.46);
    }
  }
</style>

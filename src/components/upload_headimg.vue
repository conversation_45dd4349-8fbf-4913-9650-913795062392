<!-- @format -->

<template>
  <input ref="oeUploadHead" type="file" accept="image/*" @change="changeUpload($event)" multiple="multiple"
    style="display: none" />
  <oeui-cropper :is_square="is_square" v-if="cropperModal" :imgData="cropperData" @save="saveHeadImg"
    @cancel="cancelHeadImg"> </oeui-cropper>
  <oeui-popup ref="oeExamine" mode="bottom" :round="true">
    <div class="oe_examine">
      <h3 class="title">头像审核标准</h3>
      <p class="state">上传不真实的照片审核会被拒绝哦</p>
      <div class="list">
        <dl>
          <dt><img src="@/assets/images/up_1.jpg" /></dt>
          <dd>
            <p class="iconfont icon-gou"></p>
            <p class="text success">真实居中</p>
          </dd>
        </dl>
        <dl>
          <dt><img src="@/assets/images/up_2.jpg" /></dt>
          <dd>
            <p class="iconfont icon-gou"></p>
            <p class="text success">上半身照</p>
          </dd>
        </dl>
        <dl>
          <dt><img src="@/assets/images/up_3.jpg" /></dt>
          <dd>
            <p class="iconfont icon-cha"></p>
            <p class="text fail">模糊不清</p>
          </dd>
        </dl>
        <dl>
          <dt><img src="@/assets/images/up_4.jpg" /></dt>
          <dd>
            <p class="iconfont icon-cha"></p>
            <p class="text fail">过于暴露</p>
          </dd>
        </dl>
        <dl>
          <dt><img src="@/assets/images/up_5.jpg" /></dt>
          <dd>
            <p class="iconfont icon-cha"></p>
            <p class="text fail">P图过度</p>
          </dd>
        </dl>
      </div>
      <div class="btn_box">
        <div class="btn" @click="upload">上传头像</div>
      </div>
    </div>
  </oeui-popup>
</template>

<script>
import oeuiPopup from '@/oeui/popup.vue'
import oeuiCropper from '@/oeui/cropper.vue'
import { getCurrentInstance, ref, computed, nextTick } from 'vue'
import wx from 'weixin-js-sdk'
import { useStore } from 'vuex'
export default {
  components: {
    oeuiCropper,
    oeuiPopup
  },
  emits: {
    callback: 'callback'
  },
  props: {
    is_square: {
      type: Boolean,
      default: false
    }
  },
  setup (props, context) {
    const { proxy } = getCurrentInstance()
    const store = useStore()
    const http = proxy.http
    const OEUI = proxy.OEUI
    const config = computed(() => store.state.config)
    const cropperData = ref({}) //裁剪图片的数据
    const cropperModal = ref(false) //裁剪图片显示
    const uploadType = computed(() => store.state.uploadType)

    //是否开启裁剪
    const is_cropper = ref(false)
    const is_square = ref(props.is_square)

    const wechatUpload = () => {
      wx.checkJsApi({
        jsApiList: ['chooseImage', 'uploadImage', 'downloadImage', 'getLocalImgData'], // 需要检测的JS接口列表，所有JS接口列表见附录2,
        success: res => {
          if (res.checkResult.getLocation == false) {
            alert('你的微信版本太低，请升级到最新的微信版本！')
            return
          }
          choolUpload()
        }
      })
    }
    const choolUpload = () => {
      wx.chooseImage({
        //
        count: 1, // 默认9
        sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: data => {
          if (data.localIds.length == 0) {
            OEUI.toast({
              text: '请选择照片'
            })
            return
          }
          OEUI.loading.show()
          let localIds = data.localIds[0] || ''
          wx.uploadImage({
            localId: localIds, // 需要上传的图片的本地ID，由chooseImage接口获得
            isShowProgressTips: 0,
            success: result => {
              let serverId = result.serverId //微信素材 id
              // ajaxWechatImg(serverId);
              downloadImage(serverId)
            },
            error: () => {
              OEUI.toast({
                text: '下载微信图片失败，请检查！'
              })
            }
          })
        }
      })
    }
    const ajaxWechatImg = id => {
      OEUI.modal({
        title: '温馨提示',
        text: id,
        confirm: () => { },
        cancel: () => { }
      })
      OEUI.loading.show()
      http
        .post('', {
          c: 'wxjsapi',
          a: 'getimage',
          mediaid: id
        })
        .then(res => {
          OEUI.loading.hide()
          if (res.ret == 1) {
            cropperData.value.src = res.result.base64img
            cropperModal.value = true
          } else {
            OEUI.toast({
              text: res.msg || '上传头像失败，请检查'
            })
          }
        })
    }

    //显示审核机制
    const open = flag => {
      if (flag) {
        is_cropper.value = false
      } else {
        is_cropper.value = true
      }
      upload()
      //proxy.$refs.oeExamine.open()
    }
    const close = () => {
      proxy.$refs.oeExamine.close()
    }

    //触发上传
    const upload = () => {
      //proxy.$refs.oeExamine.close()
      if (uploadType.value == 1) {
        proxy.$refs.oeUploadHead.dispatchEvent(new MouseEvent('click'))
      } else {
        wechatUpload()
      }
    }
    //上传回调
    const changeUpload = e => {
      let inputFile = Array.from(e.target.files)[0]
      nextTick(() => {
        if (is_cropper.value) {
          cropperData.value.src = URL.createObjectURL(inputFile)
          cropperData.value.name = inputFile.name
          cropperModal.value = true
        } else {
          cropperModal.value = false
          fileToBase64(inputFile)
        }
      })
    }

    const fileToBase64 = fileAddress => {
      const file = new FileReader()
      file.readAsDataURL(fileAddress)
      file.onload = function () {
        context.emit('callback', { src: file.result })
      }
    }


    //上传裁剪头像
    const saveHeadImg = obj => {
      cropperModal.value = false
      context.emit('callback', obj)
    }

    //取消裁剪头像
    const cancelHeadImg = () => {
      cropperModal.value = false
    }
    const downloadImage = serverId => {
      wx.downloadImage({
        serverId: serverId, // 需要下载的图片的服务器端ID，由uploadImage接口获得
        isShowProgressTips: 1, // 默认为1，显示进度提示
        success: res => {
          let localId = res.localId // 返回图片下载后的本地ID
          getLocalImgData(localId)
        },
        fail: () => {
          OEUI.loading.hide()
        }
      })
    }
    //获取本地图片接口
    const getLocalImgData = localId => {
      wx.getLocalImgData({
        localId: localId, // 图片的localID
        success: res => {
          OEUI.loading.hide()
          let localData = res.localData // localData是图片的base64数据，可以用img标签显示
          let imageBase64 = ''
          if (localData.indexOf('data:image') == 0) {
            imageBase64 = localData
          } else {
            imageBase64 = 'data:image/jpeg;base64,' + localData.replace(/\n/g, '')
          }
          if (is_cropper.value) {
            cropperData.value.src = imageBase64
            cropperModal.value = true
          } else {
            cropperModal.value = false
            context.emit('callback', { src: imageBase64 })
          }
        },
        fail: () => {
          OEUI.loading.hide()
          OEUI.toast({
            text: '上传图片失败,请联系客服'
          })
        }
      })
    }

    return {
      close,
      cropperData,
      cropperModal,
      open,
      upload,
      changeUpload,
      saveHeadImg,
      cancelHeadImg,
      is_cropper,
      is_square
    }
  }
}
</script>
<style lang="scss" scoped>
.oe_examine {
  .title {
    font-size: 0.427rem;
    color: #333333;
    text-align: center;
    padding-top: 0.48rem;
    font-weight: normal;
  }

  .state {
    padding-top: 0.08rem;
    color: #4e5969;
    font-size: 0.32rem;
    text-align: center;
  }

  .list {
    padding: 0.8rem 0.667rem 0;
    display: flex;
    justify-content: space-between;

    dl {
      width: 1.547rem;

      dt {
        border-radius: 8px;
        overflow: hidden;

        img {
          width: 100%;
        }
      }

      dd {
        padding-top: 0.213rem;
        text-align: center;

        .icon-gou {
          color: #4ed48d;
        }

        .icon-cha {
          color: #ff414b;
        }

        .text {
          color: #666666;
          font-size: 0.32rem;
        }
      }
    }
  }

  .btn_box {
    padding: 1.2rem 1.6rem 0.8rem;
    margin-bottom: 10px;

    .btn {
      height: 1.12rem;
      font-size: 0.427rem;
      color: #ffffff;
      background: $color_main;
      border-radius: 1.12rem;
      text-align: center;
      padding: 0.267rem 0;
      box-sizing: border-box;
      cursor: pointer;
      box-shadow: 0px 0.08rem 0.213rem rgba(0, 0, 0, 0.15);
    }
  }
}

.jump {
  color: $color_main;
  text-align: center;
  font-size: 0.373rem;
  position: fixed;
  bottom: 0.2667rem;
  left: 50%;
  transform: translateX(-50%);
}
</style>

<!-- @format -->

<template>
  <oe_popup ref="focus_tencent" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="focus_tencent">
      <p class="title" v-if="newTitle">{{ newTitle }}</p>
      <p class="tips" v-if="newTips" v-html="newTips"></p>
      <div class="qr">
        <img v-if="qrcode" :src="qrcode" alt="" />
        <img v-else :src="unionInfo.wxcode_url" alt="" />
      </div>
      <p class="tap" v-if="newTap" v-html="newTap"></p>
      <span @click="close" class="close iconfont icon-guanbi"></span>
    </div>
  </oe_popup>
</template>

<script setup>
  import { ref, getCurrentInstance, defineExpose, defineProps, computed } from 'vue'
  import oe_popup from '@/oeui/popup.vue'
  import { useStore } from 'vuex'
  const { proxy } = getCurrentInstance()
  const store = useStore()
  const unionInfo = computed(() => store.state.unionInfo)
  const props = defineProps({
    title: {
      type: String,
      default: '联系客服'
    },
    tips: {
      type: String,
      default: ''
    },
    tap: {
      type: String,
      default: '长按识别二维码'
    },
    qrcode: {
      type: String,
      default: ''
    }
  })

  const newTitle = ref(props.title)
  const newTips = ref(props.tips)
  const newTap = ref(props.tap)
  const qrcode = ref(props.qrcode)
  const open = option => {
    if (option?.title) newTitle.value = option.title
    if (option?.tips) newTips.value = option.tips
    if (option?.tap) newTap.value = option.tap
    if (option?.qrcode) qrcode.value = option.qrcode

    proxy.$refs.focus_tencent.open()
  }

  const close = () => {
    proxy.$refs.focus_tencent.close()
  }

  defineExpose({
    open
  })
</script>

<style lang="scss" scoped>
  .focus_tencent {
    border-radius: 0.64rem 0.64rem 0 0;
    padding: 0.64rem 0.8533rem 0.8533rem 0.8533rem;
    .title {
      text-align: center;
      font-size: 0.4267rem;
      line-height: 0.5867rem;
      color: #3d3d3d;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
    }
    .tips {
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      color: #666;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: normal;
      text-align: center;
      margin-top: 0.4267rem;
    }
    .qr {
      box-sizing: border-box;
      width: 3.7333rem;
      height: 3.7333rem;
      background: fff;
      margin: 0.4267rem auto;
      border: 0.16rem solid #fff;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .tap {
      text-align: center;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      color: #666;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: normal;
    }
    .close {
      position: absolute;
      font-size: 0.7467rem;
      color: #fff;
      left: 50%;
      transform: translateX(-50%);
      bottom: -1.3333rem;
    }
  }
</style>

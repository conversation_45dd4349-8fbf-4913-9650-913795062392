<!-- @format -->

<template>
  <oeui-popup ref="editPopup" mode="bottom" :round="true">
    <div class="edit_text">
      <h3 class="title">{{ title }}</h3>
      <p class="describe" v-if="text">{{ text }}</p>
      <div class="input_box">
        <input type="text" :maxlength="length" :placeholder="inputText" v-model="textValue" />
      </div>
      <slot></slot>
      <div class="jump" @click="jump" v-if="isJump">跳过</div>
      <div class="btn_box">
        <div class="btn" @click="next">
          <i>{{ btnText }}</i>
        </div>
      </div>
    </div>
  </oeui-popup>
</template>
<script>
  import oeuiPopup from '../oeui/popup.vue'
  import { getCurrentInstance, onMounted, ref, watch } from 'vue'

  export default {
    components: {
      oeuiPopup
    },
    props: {
      title: {
        type: String,
        default: '你的毕业院校是？'
      },
      text: {
        type: String,
        default: ''
      },
      inputText: {
        type: String,
        default: '请输入学校全称'
      },
      btnText: {
        type: String,
        default: '确定'
      },
      isJump: {
        type: Boolean,
        default: false
      },
      current: {
        type: String,
        default: ''
      },
      length: {
        type: Number,
        default: 999
      }
    },
    setup(props, context) {
      const { proxy } = getCurrentInstance()
      const OEUI = proxy.OEUI
      const textValue = ref('')
      const open = () => {
        proxy.$refs.editPopup.open()
      }
      const close = () => {
        proxy.$refs.editPopup.close()
      }
      const jump = () => {
        context.emit('jump')
        close()
      }
      const next = () => {
        if (!textValue.value) return OEUI.toast({ text: '内容不能为空!' })
        context.emit('next', textValue.value)
        close()
      }
      watch(
        () => props.current,
        newValue => {
          textValue.value = newValue
        },
        { deep: true, immediate: true }
      )
      return {
        open,
        close,
        next,
        jump,
        textValue
      }
    }
  }
</script>
<style lang="scss" scoped>
  .edit_text {
    padding: 0.4rem 1.6rem 2.1333rem;
    .title {
      font-size: 0.427rem;
      text-align: center;
      color: $color_3;
      margin: 0;
      font-weight: normal;
      line-height: 1.067rem;
    }
    .describe {
      font-size: 0.32rem;
      text-align: center;
      color: $color_9;
    }
    .input_box {
      padding-top: 0.533rem;
      padding-bottom: .4267rem;
      height: 1rem;
      input {
        width: 100%;
        height: 100%;
        font-size: 0.427rem;
        text-align: center;
        border-bottom: 1px solid #e5e6eb;
      }
    }
    .jump {
      color: $color_main;
      text-align: center;
      font-size: 0.373rem;
      padding-bottom: 0.267rem;
    }
    .btn_box {
      padding: 0.4rem 0 0.8rem;
      .btn {
        position: relative;
        margin: 0 auto;
        height: 1.28rem;
        line-height: 1.28rem;
        border-radius: 1.28rem;
        text-align: center;
        color: #ffffff;
        font-size: 0.427rem;
        background: linear-gradient(90deg, #8c62fe 0%, #ce63fd 100%);
        i {
          position: relative;
          z-index: 1;
        }
      }
    }
  }
</style>

<template>
  <div class="oe_nav">
    <span class="back" @click="back" v-if="isback && backLength">
      <i class="iconfont icon-zuo" :style="{color:color}"></i>
    </span>
    <span class="more" v-if="ismore" @click="eventMore">{{moretext}}</span>
  </div>
  <h1 class="oe_title" :style="{color:color}">{{title}}</h1>
</template>
<script>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
export default {
  emits:['navMore'],
  props:{
    title:{
      type:String,
      default:''
    },
    color:{
      type:String,
      default:''
    },
    isback:{
      type:Boolean,
      default:true
    },
    ismore:{
      type:Boolean,
      default:false
    },
    moretext:{
      type:String,
      default:''
    },
  },
  setup(props,context){
    const backLength = ref(true);
    const router = useRouter();
    const store = useStore();
    if (window.top.history.length <= 1) {
      backLength.value = false;
    }
    const back = ()=>{
      if (window.history.length <= 1) {
        router.replace({ path: '/edit' })
      }else if(props.ismore && props.moretext == '预览资料'){
        let url = sessionStorage.getItem('forwardUrl');
        sessionStorage.removeItem('forwardUrl');
        window.location.replace(url || store.state.siteUrl + 'index.php?m=wap&c=cp');
      } else {
        router.back()
      }
    }
    const eventMore = ()=>{
      context.emit('navMore');
    }

    return{
      backLength,
      back,
      eventMore
    }
  }
}
</script>
<style lang="scss" scoped>
@import '../assets/iconfont/iconfont.css';
.oe_nav{
  position: fixed;
  left:0;
  top:0;
  right:0;
  z-index:100;
  max-width:750px;
  margin:0 auto;
  .back{
    position:absolute;
    left:.4rem;
    top:.4rem;
    width:1.067rem;
    height:1.067rem;
    cursor:pointer;
    background:rgba(255, 255, 255, 0.6);
    z-index:100;
    border-radius:1rem;
    display:flex;
    align-items: center;
    justify-content: center;
    .iconfont{
      font-size:.533rem;
      color:#333333;
      vertical-align: top;
    }
  }
  .more{
    position:absolute;
    right:.4rem;
    top:.493rem;
    line-height:.88rem;
    padding:0 .267rem;
    font-size:.32rem;
    color:#333333;
    background:rgba(255, 255, 255, 0.8);
    border-radius:1rem;
    cursor:pointer;
  }
}
.oe_title{
  position: relative;
  padding-top:.4rem;
  height:1.067rem;
  line-height:1.067rem;
  text-align:center;
  color:#333333;
  font-size:.427rem;
  font-weight: normal;
  z-index:1;
}
</style>
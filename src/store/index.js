/** @format */

import { createStore } from 'vuex'
import axios from '@/utils/axios'

import OEUI from '../oeui/js/oeui'
import router from '../router/index'
import { getTjWork } from '@/api/cp.js'
import { getTjincome } from '@/api/income.js'
let wx = require('weixin-js-sdk') || window['wx']

export default createStore({
  state: {
    siteUrl: 'https://e.oephp.com/',
    config: {},
    unionInfo: {},
    listState: false,
    loginStatus: 0, //0:未登录  1:已登录
    uploadType: 1,
    tj_data: {
      after0_nums: 0,
      after1_nums: 0,
      after2_nums: 0,
      after3_nums: 0,
      after_nums: 0,
      audituser0_nums: 0,
      audituser1_nums: 0,
      audituser2_nums: 0,
      audituser_nums: 0,
      crmfinance_amount: 0,
      crmfinance_income: 0,
      crmorder_nums: 0,
      leadpay_amount: 0,
      leadunion_nums: 0,
      leaduser_nums: 0,
      reguser0_nums: 0,
      reguser1_nums: 0,
      reguser2_nums: 0,
      reguser_nums: 0,
      total_income: 0,
    },
    income: {},
  },
  mutations: {
    //同步

    //更新登录状态
    updataLoginStatus(state, id) {
      if (id) {
        state.loginStatus = 1
      } else {
        state.loginStatus = 0
      }
    },
    setUnionInfo(state, info) {
      //设置用户基本资料
      state.unionInfo = Object.assign({}, state.unionInfo, info)
    },

    setConfig(state, info) {
      //设置站点配置
      state.config = info
      state.siteUrl = info.siteurl
    },
    setUploadType(state, value) {
      state.uploadType = value
    },
    setTj(state, data) {
      state.tj_data = data
    },
    setIncome(state, data) {
      state.income = data
    },
  },
  actions: {
    //异步

    getConfig({ commit }, isGetHc = false) {
      //获取站点配置
      if (isGetHc) {
        let hcConfig = localStorage.getItem('hcConfig') || null
        if (hcConfig) {
          commit('setConfig', JSON.parse(hcConfig).result)
          return new Promise((resolve, reject) => {
            resolve(JSON.parse(hcConfig))
          })
        }
      }
      const p = axios.post('', {
        m: 'vuewap',
        c: 'picker',
        a: 'config',
      })
      p.then(res => {
        if (res.ret == 1) {
          commit('setConfig', res.result)
          localStorage.setItem('hcConfig', JSON.stringify(res))
          sessionStorage.setItem('smsCheck', res.result.regmobilecode)
        }
      })
      return p
    },
    setUserInfo({ commit }, result) {
      //设置用户登录信息
      if (result.sign) localStorage.setItem('userSign', result.sign)
      commit('setUnionInfo', result.info)
      commit('updataLoginStatus', result.info.unionid || 0)
    },

    getUserInfo({ commit }) {
      //获取用户登录信息
      let sign = localStorage.getItem('userSign')
      if (sign) {
        const p = axios.post('', {
          m: 'union',
          vuemod: 'vue',
          c: 'my',
          a: 'data',
        })
        p.then(res => {
          if (res.ret == 1) {
            commit('setUnionInfo', res.result.info)
            commit('updataLoginStatus', res.result.info.unionid)
          }
        })
        return p
      } else {
        return new Promise((resolve, reject) => {
          commit('updataLoginStatus', 0)
          resolve({ ret: -10 })
        })
      }
    },

    getLoginStatus({ commit, dispatch }) {
      //获取用户登录状态
      let sign = localStorage.getItem('userSign')
      if (sign) {
        const p = axios.post('', {
          m: 'union',
          vuemod: 'vue',
          c: 'index',
          a: 'loginstatus',
        })
        p.then(res => {
          if (res.ret == 1) {
            commit('updataLoginStatus', res.result.login_uid)
          } else if (res.ret == -10) {
            dispatch('getCookieUser')
          }
        })
        return p
      } else {
        return new Promise((resolve, reject) => {
          commit('updataLoginStatus', 0)

          resolve({ ret: -10 })
        })
      }
    },
    getCookieUser({ dispatch }) {
      //获取cookie登录用户信息
      const p = axios.post('', {
        m: 'union',
        vuemod: 'vue',
        c: 'login',
        a: 'check',
      })
      p.then(res => {
        if (res.ret == 1) {
          if (!res.result?.info?.unionid) return
          dispatch('setUserInfo', res.result)
          let url = sessionStorage.getItem('replaceLogin')
          if (url) {
            url = JSON.parse(url)
            router.replace({ path: url.path, query: url.query })
            sessionStorage.removeItem('replaceLogin')
          } else {
            router.replace('/home')
          }
        }
      })
      sessionStorage.removeItem('source')
      sessionStorage.removeItem('reg_data')
      sessionStorage.removeItem('setRouter')
      sessionStorage.removeItem('routerArr')
      return p
    },

    getWechatUp({ commit }) {
      const p = axios
        .post('', {
          m: 'union',
          vuemod: 'vue',
          c: 'wxjsapi',
          a: 'token',
          page_url: window.location.href,
        })
        .then(res => {
          if (res.ret == 1) {
            wx.config({
              debug: false,
              appId: res.result.appid, // 必填，公众号的唯一标识
              timestamp: res.result.timestamp, // 必填，生成签名的时间戳
              nonceStr: res.result.noncestr, // 必填，生成签名的随机串
              signature: res.result.signature,
              jsApiList: [
                'chooseWXPay',
                'chooseImage', //选择图片
                'uploadImage', //上传图片
                'downloadImage',
                'getLocalImgData',
                'updateTimelineShareData',
                'updateAppMessageShareData',
                'wx-open-launch-weapp',
              ], // 必填，需要使用的JS接口列表
            })
            wx.ready(() => {
              commit('setUploadType', 2)
            })
            wx.error(() => {
              commit('setUploadType', 1)
            })
          }
        })
      return p
    },
    getTj({ commit }) {
      let sign = localStorage.getItem('userSign')
      if (!sign) return
      getTjWork().then(res => {
        if (res.ret == 1) {
          commit('setTj', res.result.data)
        } else {
          OEUI.toast({
            text: res.msg || '系统繁忙，请稍后再试',
          })
        }
      })
    },
    getIncome({ commit }) {
      let sign = localStorage.getItem('userSign')
      if (!sign) return
      getTjincome().then(res => {
        if (res.ret == 1) {
          commit('setIncome', res.result.data)
        } else {
          OEUI.toast({
            text: res.msg || '系统繁忙，请稍后再试',
          })
        }
      })
    },
  },
  modules: {},
})

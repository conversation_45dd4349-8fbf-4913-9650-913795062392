import { createApp } from 'vue'
import oePreview from '../preview.vue'

let instance

const initInstance = () => {
  const app = createApp(oePreview)
  const container = document.createElement('div')
  instance = app.mount(container)
  document.body.appendChild(container) //进行挂载 - 挂载之后返回实例上下文
  return container
}

const preview = option => {
  initInstance();
  option = option || {};
  const defaultOption = {
    list:[],
    current:0
  }
  for (const key in defaultOption){
    instance[key] = option[key] || option[key] === false ? option[key] : defaultOption[key]
  }
  return instance.open()
}

export default preview
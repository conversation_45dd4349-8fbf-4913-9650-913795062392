import { createApp } from 'vue'
import oeLoading from '../loading.vue'

let instance;

const initInstance = () =>{
  const app = createApp(oeLoading)
  const container = document.createElement('div')
  instance = app.mount(container)
  document.body.appendChild(container) //进行挂载 - 挂载之后返回实例上下文
}

const show = option =>{
  if(!instance) initInstance();
  option = typeof option === 'string' ? { text: option } : option || {}
  const defaultOption = {
    text: ''
  }
  for (const key in defaultOption){
    instance[key] = option[key] || defaultOption[key]
  }
  return instance.showLoading();
}

const hide = () =>{
  if(instance){
    return instance.hideLoading();
  }
}


export default { show, hide }
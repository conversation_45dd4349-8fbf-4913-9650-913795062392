import { createApp } from 'vue'
import oeToast from '../toast.vue'

let instance

const initInstance = () => {
  const app = createApp(oeToast)
  const container = document.createElement('div')
  instance = app.mount(container)
  document.body.appendChild(container) //进行挂载 - 挂载之后返回实例上下文
  return container
}

const toast = option => {
  const container =  initInstance()
  option = typeof option === 'string' ? { text: option } : option || {}
  const defaultOption = {
    text: '',
    time: 2000,
    type:'center'
  }
  for (const key in defaultOption){
    instance[key] = option[key] || defaultOption[key]
  }
  setTimeout(()=>{container.remove()},instance.time) // 销毁
  return instance.open()
}

export default toast
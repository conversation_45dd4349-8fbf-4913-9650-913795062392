import { createApp } from 'vue'
import oeModal from '../modal.vue'

let instance

const initInstance = () => {
  const app = createApp(oeModal)
  const container = document.createElement('div')
  instance = app.mount(container)
  document.body.appendChild(container) //进行挂载 - 挂载之后返回实例上下文
  return container
}

const modal = option => {
  initInstance();
  option = option || {};
  const defaultOption = {
    title:'温馨提示',
    text: '这是一个 modal 提示框',
    confirmText:'确定',
    cancelText:'取消',
    cancelShow:true,
    confirm: null,
    cancel:null,
  }
  for (const key in defaultOption){
    instance[key] = option[key] || option[key] === false ? option[key] : defaultOption[key]
  }
  return instance.open()
}

export default modal
<!-- @format -->

<template>
  <div class="oe_picker" v-show="display">
    <div class="mask" :class="visible ? 'show' : 'hide'" @click="close"></div>
    <div class="picker" :class="visible ? 'show' : 'hide'">
      <div class="choose">
        <h3 class="title">{{ title }}</h3>
        <p class="describe" v-if="text">{{ text }}</p>
      </div>
      <div class="content" :class="row < 3 ? 'padding' : ''">
        <div class="mask_top"></div>
        <div class="mask_center"></div>
        <div class="mask_bottom"></div>
        <div class="wheel_wrapper">
          <div class="wheel" v-if="row >= 1">
            <oeui-wheel @changeWheel="oneWheel" :index="oneIndex" :list="list">
              <ul>
                <li v-for="item in list" :key="item.i" :value="item.value">{{ item.text }}</li>
              </ul>
            </oeui-wheel>
          </div>
          <div class="wheel" v-if="row >= 2">
            <oeui-wheel @changeWheel="twoWheel" :index="twoIndex" :list="list[oneIndex].children">
              <ul>
                <li v-for="item in list[oneIndex].children" :key="item.i" :value="item.value">{{ item.text }}</li>
              </ul>
            </oeui-wheel>
          </div>
          <div class="wheel" v-if="row == 3">
            <oeui-wheel @changeWheel="threeWheel" :index="threeIndex" :list="list[oneIndex].children[twoIndex].children">
              <ul>
                <li v-for="item in list[oneIndex].children[twoIndex].children" :key="item.i" :value="item.value">{{ item.text }}</li>
              </ul>
            </oeui-wheel>
          </div>
        </div>
      </div>
      <div class="jump" @click="jump" v-if="isJump">跳过</div>
      <div class="btn_box">
        <div class="btn" @click="confirm">
          <i>{{ btnText }}</i>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { reactive, ref, watch } from 'vue'
  import oeuiWheel from './wheel.vue'
  export default {
    props: {
      list: {
        type: Array,
        required: true
      },
      row: {
        type: Number,
        default: 1
      },
      title: {
        type: String,
        default: '请选择居住地'
      },
      text: {
        type: String,
        default: ''
      },
      current: {
        type: Array
      },
      btnText: {
        type: String,
        default: '确定'
      },
      isJump: {
        type: Boolean,
        default: false
      }
    },
    components: {
      oeuiWheel
    },
    setup(props, context) {
      const visible = ref(false)
      const display = ref(false)
      const oneIndex = ref(0)
      const twoIndex = ref(0)
      const threeIndex = ref(0)
      const arrList = ref([])
      let { current, list, row } = reactive(props)
      arrList.value = list

      const setCurrent = () => {
        if (current && current[0]) {
          let index = arrList.value.findIndex(item => item.value == current[0])
          oneIndex.value = index > 0 ? index : 0
        }
        if (current && current[1]) {
          let index = arrList.value[oneIndex.value].children.findIndex(item => item.value == current[1])
          twoIndex.value = index > 0 ? index : 0
        }
        if (current && current[2]) {
          let index = arrList.value[oneIndex.value].children[twoIndex.value].children.findIndex(item => item.value == current[2])
          threeIndex.value = index > 0 ? index : 0
        }
      }
      setCurrent()

      const open = () => {
        visible.value = true
        display.value = true
      }

      const close = callback => {
        visible.value = false
        setTimeout(() => {
          display.value = false
          if (typeof callback == 'function') {
            callback()
          } else {
            context.emit('cancel')
          }
        }, 280)
      }

      const oneWheel = index => {
        if (oneIndex.value != index) {
          oneIndex.value = index
          twoIndex.value = 0
          threeIndex.value = 0
        }
      }

      const twoWheel = index => {
        if (twoIndex.value != index) {
          twoIndex.value = index
          threeIndex.value = 0
        }
      }

      const threeWheel = index => {
        threeIndex.value = index
      }

      const confirm = () => {
        let arr
        let obj1, obj2, obj3
        let list1 = arrList.value[oneIndex.value] || ''
        obj1 = list1 ? { i: list1.i || oneIndex.value + 1, value: list1.value || '', text: list1.text || '' } : ''
        obj2 = {}
        obj3 = {}
        arr = [obj1]
        if (row >= 2) {
          let list2 = arrList.value[oneIndex.value].children[twoIndex.value] || ''
          obj2 = list2 ? { i: list2.i || twoIndex.value + 1, value: list2.value || '', text: list2.text || '' } : ''
          arr.push(obj2)
        }
        if (row == 3) {
          let list3 = arrList.value[oneIndex.value].children[twoIndex.value].children[threeIndex.value] || ''
          obj3 = list3 ? { i: list3.i || threeIndex.value + 1, value: list3.value || '', text: list3.text || '' } : ''
          arr.push(obj3)
        }

        close(() => context.emit('confirm', arr))
      }
      const cancel = () => {
        close()
      }

      const jump = () => {
        context.emit('jump')
        close()
      }
      watch(
        () => props.list,
        newValue => {
          arrList.value = newValue
        },
        { deep: true, immediate: true }
      )

      watch(
        () => props.current,
        newValue => {
          if (newValue[0]) {
            current = newValue
            setCurrent()
          }
        },
        { deep: true, immediate: true }
      )

      return {
        visible,
        display,
        oneIndex,
        twoIndex,
        threeIndex,
        open,
        close,
        confirm,
        cancel,
        jump,
        oneWheel,
        twoWheel,
        threeWheel
      }
    }
  }
</script>
<style lang="scss" scoped>
  .oe_picker {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
    .mask {
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.45);
      cursor: pointer;
      &.show {
        animation: show 0.3s ease;
      }
      &.hide {
        animation: hide 0.3s ease;
      }
    }
    .picker {
      position: absolute;
      left: 0;
      bottom: 0;
      right: 0;
      background: #ffffff;
      border-radius: 0.4rem 0.4rem 0 0;
      .choose {
        position: relative;
        padding-top: 0.4rem;
        .title {
          font-size: 0.427rem;
          text-align: center;
          color: $color_3;
          margin: 0;
          font-weight: normal;
          line-height: 1.067rem;
        }
        .describe {
          font-size: 0.373rem;
          text-align: center;
          color: $color_9;
        }
      }
      .content {
        position: relative;
        height: 4.667rem;
        margin-top: 0.4rem;
        &.padding {
          padding: 0 1.467rem;
          .mask_top,
          .mask_center,
          .mask_bottom {
            left: 1.467rem;
            right: 1.467rem;
          }
        }
        .mask_top {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          z-index: 10;
          height: 1.813rem;
          pointer-events: none;
          transform: translateZ(0);
          background: linear-gradient(0deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8));
          &::after {
            content: '';
            border-bottom: 1px solid #ebebeb;
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            transform-origin: 0 top;
          }
        }
        .mask_center {
          position: absolute;
          top: 1.813rem;
          left: 0;
          right: 0;
          height: 0.96rem;
          background: #f7f7f7;
        }
        .mask_bottom {
          position: absolute;
          bottom: 0.08rem;
          left: 0;
          right: 0;
          z-index: 10;
          height: 1.813rem;
          pointer-events: none;
          transform: translateZ(0);
          background: linear-gradient(180deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8));
          &::after {
            content: '';
            border-top: 1px solid #ebebeb;
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            transform-origin: 0 top;
          }
        }
        .wheel_wrapper {
          position: relative;
          display: flex;
          padding: 0 16px;
          z-index: 1;
          .wheel {
            flex: 1;
            height: 4.533rem;
            flex-basis: 1e-9px;
            width: 1%;
            overflow: hidden;
            font-size: 0.427rem;
            cursor: pointer;
            ul {
              padding: 1.813rem 0;
              line-height: 36px;
              li {
                height: 36px;
                color: $color_3;
                overflow: hidden;
                text-align: center;
                white-space: nowrap;
              }
            }
          }
        }
      }
      .jump {
        color: $color_main;
        text-align: center;
        font-size: 0.373rem;
        padding: 0.4rem 0 0.267rem;
      }
      .btn_box {
        padding: 0.4rem 1.467rem 0.8rem;
        .btn {
          position: relative;
          margin: 0 auto;
          height: 1.28rem;
          line-height: 1.28rem;
          border-radius: 1.28rem;
          text-align: center;
          color: #ffffff;
          font-size: 0.427rem;
          background: $color_main;
          i {
            position: relative;
            z-index: 1;
          }
        }
      }
      &.show {
        animation: bottomShow 0.3s ease;
      }
      &.hide {
        animation: bottomHide 0.3s ease;
      }
    }
  }

  @keyframes show {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  @keyframes hide {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
  @keyframes bottomShow {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }
  @keyframes bottomHide {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(100%);
    }
  }
</style>

<template>
  <div v-if="visible" class="oeui_loading" :class="animations ? 'show' : 'hide'" >
    <div class="icon">
      <svg class="circular" viewBox="25 25 50 50">
        <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10"/>
      </svg>
    </div>
    <p class="text">{{text}}</p>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, toRefs } from 'vue';
export default defineComponent({
  setup() {
    const state = reactive({
      text: ''
    });
    const visible = ref(false);
    const animations = ref(true);

    const showLoading = () =>{
      visible.value = true;
      animations.value = true;
    }

    const hideLoading = () =>{
      animations.value = false;
      visible.value = false;
    }

    return {
      ...toRefs(state),
      visible,
      animations,
      showLoading,
      hideLoading
    }
  }
})
</script>
<style lang='scss' scoped>
$white: #ffffff;
$width: 1.333rem;
.oeui_loading{
  position: fixed;
  left:50%;
  top:50%;
  transform:translate(-50%,-50%);
  color:#fff;
  background:rgba(0, 0, 0, 0.6);
  border-radius:.133rem;
  text-align:center;
  padding:.533rem .4rem;
  box-sizing: border-box;
  min-width:2.667rem;
  overflow: hidden;
  z-index:300;
  .icon{
    position: relative;
    margin: 0 auto;
    overflow: hidden;
    width: $width;
    &:before {
      content: '';
      display: block;
      padding-top: 100%;
    }
    .circular {
      animation: rotate 2s linear infinite;
      height: 100%;
      transform-origin: center center;
      width: 100%;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
    }
    .path {
      stroke-dasharray: 1, 200;
      stroke-dashoffset: 0;
      animation: dash 1.5s ease infinite, color 6s ease infinite;
      stroke-linecap: round;
    }
  }
  .text{
    margin:0;
    padding-top:.267rem;
    font-size:.373rem;
  }
  &.show{
    animation: showLoading .1s ease;
  }
  &.hide{
    animation: hideLoading .1s ease;
  }
}

@keyframes showLoading{
  from{
    opacity:0;
  }
  to{
    opacity:1;
  }
}
@keyframes hideLoading{
  from{
    opacity:1;
  }
  to{
    opacity:0;
  }
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}
@keyframes dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -.933rem;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -3.307rem;
  }
}

@keyframes color {
  from {
    stroke: $white;
  }
  to{
    stroke: $white;
  }
}
</style>
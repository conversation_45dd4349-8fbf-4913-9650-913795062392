<template>
  <div class="oe_switch" 
  :class="active ? 'active' : ''"
  :style="{width:size * 2 - 4 +'px',height:size+'px'}">
    <div class="node" :style="{width:size - 4 +'px',height:size - 4 +'px'}"></div>
  </div>
</template>
<script>
export default {
  props:{
    active:{
      type:<PERSON><PERSON>an,
      dafault:false
    },
    size:{
      type:Number,
      default:28
    }
  },
  setup() {
    return {
    }
  }
};
</script>
<style lang='scss' scoped>
.oe_switch{
  display: inline-block;
  vertical-align: top;
  background: #C3C3C5;
  border-radius:8rem;
  transition: all .3s ease-in-out;
  .node{
    position: relative;
    top:2px;
    left:2px;
    background:#ffffff;
    border-radius:50%;
    transform:translateX(0);
    transition: all .3s ease-in-out;
  }
  &.active{
    background-color:$color_main;
    .node{
      transform: translateX(100%);
    }
  }
}
</style>
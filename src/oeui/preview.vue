<template>
  <div v-if="visible" class="oe_preview" ref="oePreview" :class="animat ? 'show' : 'hide'">
    <div class="mask"></div>
    <div class="preview" @click="close">
      <div class="swiper"
      @touchstart="touchStart" 
      @touchmove="touchMove" 
      @touchend="touchEnd"
      :style="style">
        <div class="item" v-for="(item,i) in list" :key="i">
          <img :src="item">
        </div>
      </div>
    </div>
    <div class="nums">{{current + 1}} / {{list.length}}</div>
  </div>
</template>
<script>
import { ref, reactive, getCurrentInstance, toRefs, defineComponent } from 'vue';
export default defineComponent({
  setup() {
    const { proxy } = getCurrentInstance();
    const visible = ref(false);
    const animat = ref(true);
    const style = reactive({
      transform: 'translate3d(0px,0px,0px)',
      transition: 'transform .4s',
      width:'200%'
    });
    const state = reactive({
      bannerWidth: 0,        //轮播图宽度
      startPoint: 0,         //触摸开始的点的横坐标
      endPoint: 0,           //触摸结束的点的横坐标
      moveLength: 0,         //startPoint与endPoint的差值
      current: 0,         //当前轮播图的索引
      list:[]
    });

    const touchStart = (e)=>{
      state.startPoint = e.changedTouches[0].pageX;
    }
    const touchMove = (e)=>{
      state.endPoint = e.changedTouches[0].pageX;
      animations();
    }
    const touchEnd = ()=>{
      jump();
      state.moveLength = 0
    }
    const jump = ()=>{
      style.transition = 'transform .4s';
      //滑动超过轮播图宽度的百分之40，则跳转下一张，否则不跳转
      if(state.moveLength > 0 && state.current !== state.list.length-1){
        if(state.moveLength > state.bannerwidth * 0.15){
          state.current ++;
        }
        style.transform =`translate3d(${-state.current * state.bannerwidth}px,0px,0px)`;
      }
      else if(state.moveLength < 0 && state.current !== 0){
        if(-state.moveLength > state.bannerwidth * 0.15){
          state.current --;
        }
        style.transform =`translate3d(${-state.current * state.bannerwidth}px,0px,0px)`;
      }
    }
    const animations = ()=>{
      if(state.startPoint === state.endPoint){return}
      style.transition = 'none';
      state.moveLength = state.startPoint - state.endPoint;
      //获取轮播图的宽度
      state.bannerwidth = window.innerWidth;
      //判断是否超出滑动范围，即第一页无法再往前一页滑动，最后一页无法再往后一页滑动
      if(state.moveLength > 0 && state.current !== state.list.length - 1){
        style.transform =`translate3d(${-state.moveLength-state.current*state.bannerwidth}px,0px,0px)`;
      }
      else if(state.moveLength < 0 && state.current !== 0){
        style.transform =`translate3d(${-state.moveLength-state.current*state.bannerwidth}px,0px,0px)`;
      }
    }

    let timer;
    const open = () => {
      if (timer) clearTimeout(timer);
      visible.value = true;
      animat.value = true;
      style.width = state.list.length * 100 + '%'
      style.transform =`translate3d(${-state.current * window.innerWidth}px,0px,0px)`;
    }

    const close = () => {
      let box = proxy.$refs.oePreview.parentNode
      animat.value = false;
      timer = setTimeout(()=>{
        visible.value = false;
        box.remove();
      },180);
    }
    return {
      visible,
      animat,
      style,
      ...toRefs(state),
      touchStart,
      touchMove,
      touchEnd,
      open,
      close
    }
  }
});
</script>
<style lang='scss' scoped>
.oe_preview{
  position:fixed;
  left:0;
  top:0;
  right:0;
  bottom:0;
  z-index:300;
  .mask{
    background:rgba(0, 0, 0, 0.7);
    width:100%;
    height:100%;
  }
  .preview{
    position:absolute;
    left:0;
    top:0;
    right:0;
    bottom:0;
    cursor: pointer;
    .swiper{
      height:100%;
      display:flex;
      cursor: pointer;
      .item{
        width:100vw;
        height:100%;
        display:flex;
        align-items: center;
        justify-content: center;
        img{
          width:100%;
        }
      }
    }
  }
  .nums{
    position: absolute;
    left:50%;
    top:.427rem;
    transform: translateX(-50%);
    color:#fff;
    font-size:.373rem;
  }
  &.show{
    animation: show .2s ease-out;
  }
  &.hide{
    animation: hide .2s ease-out;
  }
}
@keyframes show{
  from{
    opacity:0;
  }
  to{
    opacity:1;
  }
}
@keyframes hide{
  from{
    opacity:1;
  }
  to{
    opacity:0;
  }
}
</style>
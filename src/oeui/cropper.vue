<!-- @format -->

<template>
  <div class="oe_cropper">
    <div class="title">裁剪头像</div>
    <div class="btn_box">
      <div class="close" @click="cancelCropImg">
        <i class="iconfont icon-cha"></i>
      </div>
      <div class="rotate" @click="rotateCropImg">
        <i class="iconfont icon-xuanzhuan"></i>
      </div>
      <div class="send" @click="saveCropImg">
        <i class="iconfont icon-gou"></i>
      </div>
    </div>
    <img :src="imgData.src" style="max-width: 100%" ref="cropperImg" />
  </div>
</template>

<script>
import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.min.css'
import { defineComponent, getCurrentInstance, onMounted, computed } from 'vue'
import { useStore } from 'vuex'
export default defineComponent({
  components: {},
  props: {
    imgData: {
      type: Object,
      default: {}
    },
    is_square: {
      type: Boolean,
      default: false
    }
  },
  setup (props, context) {
    const { proxy } = getCurrentInstance()
    const store = useStore()
    const config = computed(() => store.state.config)
    onMounted(() => {
      initCropper()
    })
    let OECropper
    const initCropper = () => {
      OECropper = new Cropper(proxy.$refs.cropperImg, {
        viewMode: 1,
        // aspectRatio: 1,
        dragMode: 'move',
        checkCrossOrigin: true,
        zoomOnWheel: false
      })
    }
    const saveCropImg = () => {
      //裁剪
      let options = {
        width: config.value.avatarwidth || 230,
        // height: config.value.avatarheight || 230,
        height: props.is_square ? config.value.avatarwidth || 230 : config.value.avatarheight || 230,
        minWidth: 110,
        minHeight: 110,
        maxWidth: 4096,
        maxHeight: 4096,
        fillColor: '#000',
        imageSmoothingEnabled: true,
        imageSmoothingQuality: 'high'
      }
      let url = OECropper.getCroppedCanvas(options).toDataURL('image/png')
      context.emit('save', { src: url, name: props.imgData.name || '' })
    }
    const rotateCropImg = () => {
      //旋转
      OECropper.rotate(90)
    }
    const cancelCropImg = () => {
      //取消
      context.emit('cancel')
    }
    return {
      saveCropImg,
      rotateCropImg,
      cancelCropImg
    }
  }
})
</script>
<style lang="scss" scoped>
.oe_cropper {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 5000;
  padding: 0 0 1.6rem;
  box-sizing: border-box;
  overflow: hidden;

  .title {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    background: #ffffff;
    height: 1.2rem;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.427rem;
    color: #333333;
  }

  .btn_box {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 1.6rem;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #ffffff;

    div {
      padding: 0 0.533rem;
      height: 100%;
      display: flex;
      align-items: center;
      cursor: pointer;
      box-sizing: border-box;

      i {
        font-size: 0.453rem;
      }

      &.send {
        color: $color_main;
        padding-bottom: 0.107rem;
      }

      &.rotate {
        color: #585eaa;

        i {
          font-size: 0.507rem;
        }
      }

      &.close {
        color: #999999;
      }
    }
  }
}
</style>

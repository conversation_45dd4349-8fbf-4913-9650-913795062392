<!-- @format -->
<template>
  <div class="oe_datetime_picker" v-show="display">
    <div class="mask" :class="visible ? 'show' : 'hide'" @click="close"></div>
    <div class="picker" :class="visible ? 'show' : 'hide'">
      <div class="choose">
        <h3 class="title">{{ title }}</h3>
        <p class="describe" v-if="text">{{ text }}</p>
      </div>
      <div class="content">
        <div class="mask_top"></div>
        <div class="mask_center"></div>
        <div class="mask_bottom"></div>
        <div class="wheel_wrapper">
          <!-- 年 -->
          <div class="wheel year-wheel">
            <oeui-wheel @changeWheel="yearWheel" :index="yearIndex" :list="yearList">
              <ul>
                <li v-for="item in yearList" :key="item.value" :value="item.value">{{ item.text }}</li>
              </ul>
            </oeui-wheel>
          </div>
          <!-- 月 -->
          <div class="wheel month-wheel">
            <oeui-wheel @changeWheel="monthWheel" :index="monthIndex" :list="monthList">
              <ul>
                <li v-for="item in monthList" :key="item.value" :value="item.value">{{ item.text }}</li>
              </ul>
            </oeui-wheel>
          </div>
          <!-- 日 -->
          <div class="wheel day-wheel">
            <oeui-wheel @changeWheel="dayWheel" :index="dayIndex" :list="dayList">
              <ul>
                <li v-for="item in dayList" :key="item.value" :value="item.value">{{ item.text }}</li>
              </ul>
            </oeui-wheel>
          </div>
          <!-- 时 -->
          <div class="wheel hour-wheel">
            <oeui-wheel @changeWheel="hourWheel" :index="hourIndex" :list="hourList">
              <ul>
                <li v-for="item in hourList" :key="item.value" :value="item.value">{{ item.text }}</li>
              </ul>
            </oeui-wheel>
          </div>
          <!-- 分 -->
          <div class="wheel minute-wheel">
            <oeui-wheel @changeWheel="minuteWheel" :index="minuteIndex" :list="minuteList">
              <ul>
                <li v-for="item in minuteList" :key="item.value" :value="item.value">{{ item.text }}</li>
              </ul>
            </oeui-wheel>
          </div>
        </div>
      </div>
      <div class="btn_box">
        <div class="btn" @click="confirm">
          <i>{{ btnText }}</i>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { ref, watch, computed } from 'vue'
import oeuiWheel from './wheel.vue'

export default {
  name: 'OeuiDatetimePicker',
  props: {
    title: {
      type: String,
      default: '请选择日期时间',
    },
    text: {
      type: String,
      default: '',
    },
    current: {
      type: Date,
      default: () => new Date(),
    },
    btnText: {
      type: String,
      default: '确定',
    },
    startYear: {
      type: Number,
      default: 1970,
    },
    endYear: {
      type: Number,
      default: () => new Date().getFullYear() + 10,
    },
    minuteStep: {
      type: Number,
      default: 1,
      validator: value => [1, 5, 10, 15, 30].includes(value),
    },
    format: {
      type: String,
      default: 'YYYY-MM-DD HH:mm',
    },
  },
  components: {
    oeuiWheel,
  },
  setup(props, context) {
    const visible = ref(false)
    const display = ref(false)
    const yearIndex = ref(0)
    const monthIndex = ref(0)
    const dayIndex = ref(0)
    const hourIndex = ref(0)
    const minuteIndex = ref(0)

    // 生成年份列表
    const yearList = computed(() => {
      const years = []
      for (let i = props.startYear; i <= props.endYear; i++) {
        years.push({
          value: i,
          text: `${i}年`,
        })
      }
      return years
    })

    // 生成月份列表
    const monthList = computed(() => {
      const months = []
      for (let i = 1; i <= 12; i++) {
        months.push({
          value: i,
          text: `${i}月`,
        })
      }
      return months
    })

    // 生成日期列表（根据年月动态计算）
    const dayList = computed(() => {
      const year = yearList.value[yearIndex.value]?.value || new Date().getFullYear()
      const month = monthList.value[monthIndex.value]?.value || 1
      const daysInMonth = new Date(year, month, 0).getDate()
      const days = []
      for (let i = 1; i <= daysInMonth; i++) {
        days.push({
          value: i,
          text: `${i}日`,
        })
      }
      return days
    })

    // 生成小时列表
    const hourList = computed(() => {
      const hours = []
      for (let i = 0; i <= 23; i++) {
        hours.push({
          value: i,
          text: `${i.toString().padStart(2, '0')}时`,
        })
      }
      return hours
    })

    // 生成分钟列表（支持步长）
    const minuteList = computed(() => {
      const minutes = []
      for (let i = 0; i <= 59; i += props.minuteStep) {
        minutes.push({
          value: i,
          text: `${i.toString().padStart(2, '0')}分`,
        })
      }
      return minutes
    })

    // 设置当前时间
    const setCurrent = () => {
      const currentDate = props.current || new Date()

      // 设置年份索引
      const yearValue = currentDate.getFullYear()
      const yearIdx = yearList.value.findIndex(item => item.value === yearValue)
      yearIndex.value = yearIdx >= 0 ? yearIdx : 0

      // 设置月份索引
      const monthValue = currentDate.getMonth() + 1
      const monthIdx = monthList.value.findIndex(item => item.value === monthValue)
      monthIndex.value = monthIdx >= 0 ? monthIdx : 0

      // 设置日期索引
      const dayValue = currentDate.getDate()
      const dayIdx = dayList.value.findIndex(item => item.value === dayValue)
      dayIndex.value = dayIdx >= 0 ? dayIdx : 0

      // 设置小时索引
      const hourValue = currentDate.getHours()
      const hourIdx = hourList.value.findIndex(item => item.value === hourValue)
      hourIndex.value = hourIdx >= 0 ? hourIdx : 0

      // 设置分钟索引（考虑步长）
      const minuteValue = currentDate.getMinutes()
      // 找到最接近的分钟值
      const closestMinute = Math.round(minuteValue / props.minuteStep) * props.minuteStep
      const minuteIdx = minuteList.value.findIndex(item => item.value === Math.min(closestMinute, 59))
      minuteIndex.value = minuteIdx >= 0 ? minuteIdx : 0
    }

    setCurrent()

    const open = () => {
      visible.value = true
      display.value = true
    }

    const close = callback => {
      visible.value = false
      setTimeout(() => {
        display.value = false
        if (typeof callback === 'function') {
          callback()
        } else {
          context.emit('cancel')
        }
      }, 280)
    }

    const yearWheel = index => {
      if (yearIndex.value !== index) {
        yearIndex.value = index
        // 年份变化时，需要重新检查日期是否有效
        if (dayIndex.value >= dayList.value.length) {
          dayIndex.value = dayList.value.length - 1
        }
      }
    }

    const monthWheel = index => {
      if (monthIndex.value !== index) {
        monthIndex.value = index
        // 月份变化时，需要重新检查日期是否有效
        if (dayIndex.value >= dayList.value.length) {
          dayIndex.value = dayList.value.length - 1
        }
      }
    }

    const dayWheel = index => {
      dayIndex.value = index
    }

    const hourWheel = index => {
      hourIndex.value = index
    }

    const minuteWheel = index => {
      minuteIndex.value = index
    }

    const confirm = () => {
      const selectedYear = yearList.value[yearIndex.value]?.value || new Date().getFullYear()
      const selectedMonth = monthList.value[monthIndex.value]?.value || 1
      const selectedDay = dayList.value[dayIndex.value]?.value || 1
      const selectedHour = hourList.value[hourIndex.value]?.value || 0
      const selectedMinute = minuteList.value[minuteIndex.value]?.value || 0

      const selectedDate = new Date(selectedYear, selectedMonth - 1, selectedDay, selectedHour, selectedMinute)

      close(() => context.emit('confirm', selectedDate))
    }

    // 监听当前时间变化
    watch(
      () => props.current,
      newValue => {
        if (newValue) {
          setCurrent()
        }
      },
      { deep: true, immediate: true },
    )

    return {
      visible,
      display,
      yearIndex,
      monthIndex,
      dayIndex,
      hourIndex,
      minuteIndex,
      yearList,
      monthList,
      dayList,
      hourList,
      minuteList,
      open,
      close,
      confirm,
      yearWheel,
      monthWheel,
      dayWheel,
      hourWheel,
      minuteWheel,
    }
  },
}
</script>
<style lang="scss" scoped>
@import '~@/style/main.scss';

.oe_datetime_picker {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  .mask {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.45);
    cursor: pointer;
    &.show {
      animation: show 0.3s ease;
    }
    &.hide {
      animation: hide 0.3s ease;
    }
  }
  .picker {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    background: #ffffff;
    border-radius: 0.4rem 0.4rem 0 0;
    z-index: 999;
    .choose {
      position: relative;
      padding-top: 0.4rem;
      .title {
        font-size: 0.427rem;
        text-align: center;
        color: $color_3;
        margin: 0;
        font-weight: normal;
        line-height: 1.067rem;
      }
      .describe {
        font-size: 0.373rem;
        text-align: center;
        color: $color_9;
      }
    }
    .content {
      position: relative;
      height: 4.667rem;
      margin-top: 0.4rem;
      .mask_top {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        z-index: 10;
        height: 1.813rem;
        pointer-events: none;
        transform: translateZ(0);
        background: linear-gradient(0deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8));
        &::after {
          content: '';
          border-bottom: 1px solid #ebebeb;
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          transform-origin: 0 top;
        }
      }
      .mask_center {
        position: absolute;
        top: 1.813rem;
        left: 0;
        right: 0;
        height: 0.96rem;
        background: #f7f7f7;
      }
      .mask_bottom {
        position: absolute;
        bottom: 0.08rem;
        left: 0;
        right: 0;
        z-index: 10;
        height: 1.813rem;
        pointer-events: none;
        transform: translateZ(0);
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8));
        &::after {
          content: '';
          border-top: 1px solid #ebebeb;
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          transform-origin: 0 top;
        }
      }
      .wheel_wrapper {
        position: relative;
        display: flex;
        padding: 0 0.2667rem;
        z-index: 1;
        .wheel {
          flex: 1;
          height: 4.533rem;
          flex-basis: 1e-9px;
          width: 1%;
          overflow: hidden;
          font-size: 0.373rem;
          cursor: pointer;

          // 年份轮子稍微宽一些
          &.year-wheel {
            flex: 1.2;
          }

          // 月日时分轮子等宽
          &.month-wheel,
          &.day-wheel,
          &.hour-wheel,
          &.minute-wheel {
            flex: 0.9;
          }

          ul {
            padding: 1.813rem 0;
            line-height: 36px;
            li {
              height: 36px;
              color: $color_3;
              overflow: hidden;
              text-align: center;
              white-space: nowrap;
              font-size: 0.373rem;
              transition: color 0.2s ease;
            }
          }
        }
      }
    }
    .btn_box {
      padding: 0.4rem 1.467rem 0.8rem;
      .btn {
        position: relative;
        margin: 0 auto;
        height: 1.28rem;
        line-height: 1.28rem;
        border-radius: 1.28rem;
        text-align: center;
        color: #ffffff;
        font-size: 0.427rem;
        background: $color_main;
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.98);
          opacity: 0.8;
        }

        i {
          position: relative;
          z-index: 1;
          font-style: normal;
        }
      }
    }
    &.show {
      animation: bottomShow 0.3s ease;
    }
    &.hide {
      animation: bottomHide 0.3s ease;
    }
  }
}

@keyframes show {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes hide {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes bottomShow {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
@keyframes bottomHide {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}
</style>

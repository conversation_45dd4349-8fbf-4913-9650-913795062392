<template>
  <div v-if="visible" class="oe_modal" ref="modal" :class="animations ? 'show' : 'hide'">
    <div class="mask"></div>
    <div class="modal" :class="animations ? 'show' : 'hide'">
      <h3 class="title">{{ title }}</h3>
      <div v-if="isHtml" class="content 22">{{ text }}</div>
      <div v-else class="content 11" v-html="text"></div>
      <div class="btn_box">
        <span class="cancel" @click="hideModal" v-if="cancelShow">{{ cancelText }}</span>
        <span class="confirm" @click="showModal">{{ confirmText }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent, ref, reactive, toRefs, getCurrentInstance } from 'vue'
export default defineComponent({
  setup() {
    const state = reactive({
      title: '',
      text: '',
      confirmText: '确定',
      cancelText: '取消',
      cancelShow: true,
      confirm: null,
      cancel: null,
      isHtml: false,
    })

    const visible = ref(false)
    const animations = ref(true)
    const { proxy } = getCurrentInstance()
    let timer

    const open = () => {
      if (timer) clearTimeout(timer)
      visible.value = true
      animations.value = true
    }

    const close = () => {
      let box = proxy.$refs.modal.parentNode
      animations.value = false
      timer = setTimeout(() => {
        visible.value = false
        box.remove()
      }, 180)
    }

    const showModal = () => {
      close()
      if (state.confirm) {
        state.confirm()
      }
    }

    const hideModal = () => {
      close()
      if (state.cancel) {
        state.cancel()
      }
    }

    return {
      ...toRefs(state),
      visible,
      animations,
      open,
      close,
      showModal,
      hideModal,
    }
  },
})
</script>
<style lang="scss" scoped>
.oe_modal {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 300;
  .mask {
    background: rgba(0, 0, 0, 0.45);
    width: 100%;
    height: 100%;
  }
  .modal {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 8.533rem;
    background: #ffffff;
    border-radius: 0.267rem;
    .title {
      font-size: 0.427rem;
      color: $color_3;
      line-height: 0.8rem;
      padding-top: 0.533rem;
      text-align: center;
    }
    .content {
      font-size: 0.373rem;
      color: $color_6;
      padding: 0.3rem 0.667rem 0.667rem;
      font-weight: normal;
      font-family: PingFang SC-Regular, PingFang SC;
      letter-spacing: 0.05em;
      text-align: center;
    }
    .btn_box {
      line-height: 1.28rem;
      height: 1.28rem;
      box-sizing: border-box;
      display: flex;
      border-top: 0.027rem solid #ebedf0;
      span {
        flex: 1;
        text-align: center;
        font-size: 0.427rem;
        box-sizing: border-box;
        cursor: pointer;
        &.cancel {
          border-right: 0.027rem solid #ebedf0;
          color: $color_9;
        }
        &.confirm {
          color: $color_main;
        }
      }
    }
    &.show {
      animation: showModal 0.2s ease-out;
    }
    &.hide {
      animation: hideModal 0.2s ease-out;
    }
  }
  &.show {
    animation: show 0.2s ease-out;
  }
  &.hide {
    animation: hide 0.2s ease-out;
  }
}
@keyframes show {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes hide {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes showModal {
  from {
    transform: translate(-50%, -50%) scale(0.5);
  }
  to {
    transform: translate(-50%, -50%) scale(1);
  }
}
@keyframes hideModal {
  from {
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    transform: translate(-50%, -50%) scale(0.5);
  }
}
</style>

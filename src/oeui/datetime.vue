<template>
  <oeui-area 
      ref="datePicker" 
      :title="title"
      :list="dateList"
      :row="3"
      :current="current"
      @change="confirmPicker"
  ></oeui-area>
</template>
<script>
import { ref, reactive, getCurrentInstance, watch } from 'vue';
import oeuiArea from './area.vue';
import { useStore } from 'vuex';
export default {
  props:{
    start:{
      type:Number,
      default:1970
    },
    title:{
      type:String,
      default:"请选择年月日"
    },
    end:{
      type:Number,
      default:new Date().getFullYear()
    },
    current:{
      type:Array
    }
  },
  components:{
    oeuiArea
  },
  setup(props,context) {
    const { proxy } = getCurrentInstance();
    const dateList = ref([]);
    const store = useStore();
    const isLeapYear = (year) => {//判断是否为闰年
      if (year / 4 == 0 && year / 100 != 0) {
        return true;
      } else if (year / 400 == 0) {
        return true;
      } else {
        return false;
      }
    };
    const getDayNum = (year, month) => {//获取这个月有多少天
      let nums;
      switch (month) {
        case 1:
        case 3:
        case 5:
        case 7:
        case 8:
        case 10:
        case 12:
          nums =  31;
          break;
        case 4:
        case 6:
        case 9:
        case 11:
          nums =  30;
          break;
        case 2:
          nums = isLeapYear(year) ? 29 : 28;
      }
      return nums
    };
    const initDate = (age1,age2)=>{
      // const {start,end} = reactive(props);
      let start = new Date().getFullYear() - age2;
      let end = new Date().getFullYear() - age1;
      let ind1 = 0;
      for(let i = start;i <= end; i++){//生成日期
        ind1++;
        let one = {i:ind1 ,value:i, text:i+'年',children:[]};
        let ind2 = 0;
        for(let j=1;j<=12;j++){
          ind2++;
          let two = {i:ind2,value:j, text:j+'月',children:[]};
          let ind3 = 0;
          for(let l=1;l<=getDayNum(i,j);l++){
            ind3++
            let three = {i:ind3,value:l, text:l+'日',children:[]};
            two.children.push(three);
          }
          one.children.push(two)
        }
        dateList.value.push(one)
      }
    }

    const open = ()=>{
      proxy.$refs.datePicker.open();
    }

    const confirmPicker = (e)=>{
      context.emit('confirm',e)
    }

    watch(()=>store.state.config,newValue=>{
      if(newValue.startage){
        initDate(newValue.startage,newValue.endage)
      }
    },{immediate:true,deep:true})
    return {
      dateList,
      getDayNum,
      isLeapYear,
      open,
      confirmPicker,
    };
  },
};
</script>
<style lang='scss' scoped>
</style>
<!-- @format -->

<template>
  <div class="slider">
    <div class="ruler" ref="ruler" :style="[{ background: backgroundColor }]" @click="rulerClick">
      <div class="box_bg"></div>
      <div ref="startBg" class="start_bg" :style="[{ background: activeColor, transition: style.bg }]"></div>
      <div ref="endBg" class="end_bg" :style="[{ background: activeColor, transition: style.bg }]"></div>
      <div ref="bar" class="bar startbar" :style="[{ background: blockColor, transition: style.bar1 }]" @touchstart.prevent="startTouchstart" @touchmove.prevent="startTouchmove" @touchend.prevent="touchFlag = false"></div>
      <div ref="endbar" class="bar endbar" :style="[{ background: blockColor, transition: style.bar2 }]" @touchstart.prevent="endTouchstart" @touchmove.prevent="endTouchmove" @touchend.prevent="touchFlag = false"></div>
    </div>
  </div>
</template>
<script>
  import { reactive, toRefs, getCurrentInstance, onMounted, watch, nextTick, ref } from 'vue'
  export default {
    props: {
      min: {
        type: Number,
        default: 0
      },
      max: {
        type: Number,
        default: 100
      },
      minValue: {
        type: Number,
        default: 0
      },
      maxValue: {
        type: Number,
        default: 100
      },
      activeColor: {
        type: String,
        default: ''
      },
      backgroundColor: {
        type: String,
        default: ''
      },
      blockColor: {
        type: String,
        default: ''
      }
    },
    setup(props, context) {
      const { proxy } = getCurrentInstance()
      const state = reactive({
        ruler: '', // 滑竿
        bar: '', // 左侧滑块
        endbar: '', // 右侧滑块
        startX: '', // 左侧滑块位置
        endX: '', // 右侧滑块位置
        step: '', // 滑竿在限定范围内可以分多少步
        intervalStart: 100,
        intervalEnd: 200,
        startStep: 100,
        endStep: 200,
        amountW: '' //  滑竿多长距离
      })
      const style = reactive({
        bg: '',
        bar1: '',
        bar2: ''
      })
      const { min, max } = reactive(props)
      const touchFlag = ref(false)
      state.intervalStart = min
      state.intervalEnd = max
      const initSlider = () => {
        state.ruler = proxy.$refs.ruler
        state.bar = proxy.$refs.bar
        state.endbar = proxy.$refs.endbar
        state.amountW = state.ruler.clientWidth - state.bar.offsetWidth // 滑竿多长距离
        state.step = state.amountW / (state.intervalEnd - state.intervalStart) // 总共多少步
        if (state.amountW == 0) return
        // 初始化样式
        state.bar.style.left = (state.startStep - state.intervalStart) * state.step + 'px'
        proxy.$refs.startBg.style.width = (state.startStep - state.intervalStart) * state.step + 'px'
        state.endbar.style.right = (state.intervalEnd - state.endStep) * state.step + 'px'
        proxy.$refs.endBg.style.width = (state.intervalEnd - state.endStep) * state.step + 'px'
      }
      const startTouchstart = e => {
        state.startX = e.touches[0].pageX // 开始滑动时滑块的位置
        style.bg = 'width 80ms ease'
        style.bar1 = 'left 80ms ease'
        if (state.step == 0) {
          initSlider()
        }
      }
      const startTouchmove = e => {
        // 滑动距离=当前滑块x距离-最开始滑块距离
        let slidedis = e.touches[0].pageX - state.ruler.offsetLeft
        touchFlag.value = true
        // 滑动距离小于0 或者大于滑竿的宽度，return掉
        if (slidedis < 0) {
          slidedis = 0
        } else if (slidedis > state.amountW) {
          return
        }
        let ste = Math.round(slidedis / state.step)
        if (ste + state.intervalStart >= state.endStep) {
          return
        }
        state.startStep = ste + state.intervalStart
        state.bar.style.left = ste * state.step + 'px'
        proxy.$refs.startBg.style.width = ste * state.step + 'px'
        context.emit('change', { value1: state.startStep, value2: state.endStep })
      }
      const endTouchstart = e => {
        state.endX = e.touches[0].pageX // 开始滑动时滑块的位置
        style.bg = 'width 80ms ease'
        style.bar2 = 'right 80ms ease'
        if (state.step == 0) {
          initSlider()
        }
      }
      const endTouchmove = e => {
        // 滑动距离=当前滑块x距离-最开始滑块距离
        let slidedis = e.touches[0].pageX - state.ruler.offsetLeft
        touchFlag.value = false
        if (slidedis < 0) {
          return
        } else if (slidedis > state.amountW) {
          slidedis = state.amountW
        }
        let ste = Math.round(slidedis / state.step)
        if (state.startStep >= ste + state.intervalStart) {
          return
        }
        state.endStep = ste + state.intervalStart
        state.endbar.style.right = (state.intervalEnd - state.endStep) * state.step + 'px'
        proxy.$refs.endBg.style.width = (state.intervalEnd - state.endStep) * state.step + 'px'
        context.emit('change', { value1: state.startStep, value2: state.endStep })
      }
      const rulerClick = e => {
        if (state.step == 0) {
          initSlider()
        }
        style.bg = 'width 200ms ease'
        style.bar1 = 'left 200ms ease'
        style.bar2 = 'right 200ms ease'
        let slidedis = e.pageX - state.ruler.offsetLeft
        let nums = Math.round(slidedis / state.step)
        let ste = nums + state.intervalStart
        if (ste > state.intervalEnd || ste < state.intervalStart) {
          return
        }
        // state.endStep - ste <= ste - state.startStep   相邻算法
        if ((nums > (state.intervalEnd - state.intervalStart) / 2 && ste > state.startStep) || (ste > state.endStep && ste < state.intervalEnd)) {
          state.endStep = ste
          state.endbar.style.right = (state.intervalEnd - state.endStep) * state.step + 'px'
          proxy.$refs.endBg.style.width = (state.intervalEnd - state.endStep) * state.step + 'px'
          context.emit('change', { value1: state.startStep, value2: state.endStep })
        } else {
          state.startStep = ste
          state.bar.style.left = nums * state.step + 'px'
          proxy.$refs.startBg.style.width = nums * state.step + 'px'
          context.emit('change', { value1: state.startStep, value2: state.endStep })
        }
      }
      nextTick(() => {
        initSlider()
      })
      watch(
        () => props.minValue,
        newMin => {
          //初始化小值
          if (newMin > max) return
          state.startStep = newMin < min ? min : newMin
          if (!touchFlag.value) {
            nextTick(() => {
              initSlider()
            })
          }
        },
        { immediate: true }
      )
      watch(
        () => props.maxValue,
        newMax => {
          //初始化大值
          if (newMax < min) return
          state.endStep = newMax > max ? max : newMax
          if (!touchFlag.value) {
            nextTick(() => {
              initSlider()
            })
          }
        },
        { immediate: true }
      )
      return {
        ...toRefs(state),
        style,
        startTouchstart,
        startTouchmove,
        endTouchstart,
        endTouchmove,
        rulerClick,
        initSlider
      }
    }
  }
</script>
<style lang="scss" scoped>
  .slider {
    margin: auto;
    width: 100%;
    .ruler {
      height: 0.16rem;
      position: relative;
      padding: 0 0.133rem;
      cursor: pointer;
      .box_bg {
        height: 100%;
        background: #efefef;
      }
      .bar {
        position: absolute;
        top: -0.2rem;
        height: 0.267rem;
        width: 0.267rem;
        border-radius: 100%;
        background: $color_main;
        border: 0.13333rem solid #fff;
        font-size: 0.3rem;
        line-height: 0.65rem;
        text-align: center;
        box-shadow: 0 0 0.213rem 0 rgb(216 216 216 / 95%);
        z-index: 10;
        cursor: pointer;
      }
      .start_bg {
        position: absolute;
        background: $color_main;
        opacity: 0.1;
        height: 100%;
        top: 0;
        left: 0.133rem;
      }
      .end_bg {
        position: absolute;
        background: $color_main;
        opacity: 0.1;
        height: 100%;
        top: 0;
        right: 0.133rem;
      }
      .startbar {
        left: 0;
      }
      .endbar {
        right: 0;
      }
    }
  }
</style>

<!-- @format -->

<template>
  <div class="oe_popup" v-show="display">
    <div class="mask" :class="visible ? 'show' : 'hide'" @click="close" v-if="maskClose"></div>
    <div class="mask" :class="visible ? 'show' : 'hide'" v-else></div>
    <div class="popup" :class="[mode, visible ? 'show' : 'hide', round ? 'br' : '']" :style="{ width: mode == 'top' || mode == 'bottom' ? '100%' : width, background: background, borderRadius: (round && mode == 'center') || mode == 'bottom' ? roundStyle : '', height }">
      <slot></slot>
    </div>
  </div>
</template>

<script>
  import { ref } from 'vue'
  export default {
    props: {
      width: {
        type: String,
        default: '80%'
      },
      mode: {
        type: String,
        default: 'right'
      },
      round: {
        type: Boolean,
        default: false
      },
      roundStyle: {
        type: String,
        default: '.267rem'
      },
      maskClose: {
        type: Boolean,
        default: true
      },
      background: {
        type: String,
        default: ''
      },
      top: {
        type: String,
        default: '0'
      },
      height: {
        type: String,
        default: 'auto'
      }
    },
    setup(props, context) {
      const visible = ref(false)
      const display = ref(false)
      const closeChange = () => {
        context.emit('close')
      }

      const open = callback => {
        visible.value = true
        display.value = true
        if (typeof callback == 'function') callback()
      }

      const close = callback => {
        visible.value = false
        setTimeout(() => {
          display.value = false
          closeChange()
          if (typeof callback == 'function') callback()
        }, 280)
      }

      return {
        visible,
        display,
        open,
        close
      }
    }
  }
</script>
<style lang="scss" scoped>
  .oe_popup {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
    .mask {
      background: rgba(0, 0, 0, 0.45);
      width: 100%;
      height: 100%;
      cursor: pointer;
      &.show {
        animation: show 0.3s ease;
      }
      &.hide {
        animation: hide 0.3s ease;
      }
    }
    .popup {
      position: absolute;
      background: #ffffff;
      &.left {
        left: 0;
        top: 0;
        bottom: 0;
        &.show {
          animation: leftShow 0.3s ease;
        }
        &.hide {
          animation: leftHide 0.3s ease;
        }
      }
      &.top {
        left: 0;
        top: 0;
        right: 0;
        min-height: 2.667rem;
        &.br {
          border-radius: 0 0 0.267rem 0.267rem;
        }
        &.show {
          animation: topShow 0.3s ease;
        }
        &.hide {
          animation: topHide 0.3s ease;
        }
      }
      &.right {
        right: 0;
        top: 0;
        bottom: 0;
        &.show {
          animation: rightShow 0.3s ease;
        }
        &.hide {
          animation: rightHide 0.3s ease;
        }
      }
      &.bottom {
        left: 0;
        bottom: 0;
        right: 0;
        min-height: 4rem;
        border-bottom-left-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        &.br {
          border-radius: 0.267rem 0.267rem 0 0;
        }
        &.show {
          animation: bottomShow 0.3s ease;
        }
        &.hide {
          animation: bottomHide 0.3s ease;
        }
      }
      &.center {
        min-height: 4rem;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        //transform: translateX(-50%);
        //margin-top: -50%;
        &.br {
          border-radius: 0.267rem;
        }
        &.show {
          animation: centerShow 0.3s ease;
        }
        &.hide {
          animation: centerHide 0.3s ease;
        }
      }
    }
    &.show {
      animation: show 0.3s ease;
    }
    &.hide {
      animation: hide 0.3s ease;
    }
  }

  @keyframes show {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  @keyframes hide {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }

  @keyframes leftShow {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0);
    }
  }
  @keyframes leftHide {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(-100%);
    }
  }
  @keyframes topShow {
    from {
      transform: translateY(-100%);
    }
    to {
      transform: translateY(0);
    }
  }
  @keyframes topHide {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(-100%);
    }
  }
  @keyframes rightShow {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0);
    }
  }
  @keyframes rightHide {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(100%);
    }
  }
  @keyframes bottomShow {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }
  @keyframes bottomHide {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(100%);
    }
  }
  @keyframes centerShow {
    from {
      transform: translate(-50%, -50%) scale(0.5);
      opacity: 0;
    }
    to {
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
    }
  }
  @keyframes centerHide {
    from {
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
    }
    to {
      transform: translate(-50%, -50%) scale(0.5);
      opacity: 0;
    }
  }
</style>

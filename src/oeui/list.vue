<!-- @format -->

<template>
  <div
    class="scroll"
    :style="{
      height: 'calc(100vh - ' + (top + bottom + minus) + company + ')',
      top: top + company,
      bottom: bottom + company
    }">
    <div class="oe_list" :style="style" @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd">
      <div class="tips" v-if="isPullDown">
        <div v-if="moveState < 2">
          <img class="pulldown" :class="moveState == 1 ? 'rot' : ''" src="./images/pulldown.png" />
          {{ moveState === 0 ? '下拉刷新' : '松开刷新' }}
        </div>
        <div v-else><img class="loading" src="./images/loading2.gif" />正在加载</div>
      </div>
      <div class="content oeScroll" ref="oeScroll" @scroll="handlerScroll">
        <div class="scroll_top_dw"></div>
        <slot></slot>
        <template v-if="bottomShow">
          <div class="load_tips" v-if="result == 'loading'"><img class="loading" src="./images/loading2.gif" />内容加载中...</div>
          <div class="no_data" v-else-if="result == 'no_data'">
            <img src="@/assets/images/no_result.png" style="width: 4.53rem" />
            <p class="text">暂时没有更多内容</p>
          </div>
          <!-- <div class="no_result" v-else-if="text && text == '1'"></div> -->
          <div class="no_result" v-else-if="isbottom">
            {{ text || '没有更多内容了' }}
          </div>
        </template>
        <slot name="other"></slot>
      </div>
    </div>
  </div>
</template>

<script>
  import { ref, reactive, toRefs, computed, watch, getCurrentInstance, onActivated } from 'vue'
  import { useRoute } from 'vue-router'
  import { useStore } from 'vuex'
  export default {
    props: {
      top: {
        type: Number,
        default: 0
      },
      bottom: {
        type: Number,
        default: 0
      },
      minus: {
        type: Number,
        default: 0
      },
      company: {
        type: String,
        default: 'px'
      },
      status: {
        type: String,
        default: 'loading'
      },
      type: {
        type: String,
        default: 'list'
      },
      text: {
        type: String,
        default: ''
      },
      isPullDown: {
        type: Boolean,
        default: true
      },
      isBottm: {
        type: Boolean,
        default: true
      }
    },
    setup(props, context) {
      const { proxy } = getCurrentInstance()
      const result = ref('loading')
      const store = useStore()
      const isbottom = ref(false)
      const state = reactive({
        startY: '', //保存touch时的Y坐标
        startX: '', //保存touch时的X坐标
        moveDistance: 0, //保存向下滑动的距离
        moveState: 0, //开始滑动到结束后状态的变化 0:下拉即可刷新 1:释放即可刷新 2:加载中
        duration: 0 //动画持续时间，0就是没有动画
      })
      const bottomShow = ref(props.isBottm)
      let touchStart = e => {
        if (!props.isPullDown) return false

        state.duration = 0 // 关闭动画
        state.moveDistance = 0 // 滑动距离归0
        state.startY = e.targetTouches[0].clientY // 获得开始Y坐标
        state.startX = e.targetTouches[0].clientX // 获得开始X坐标
        if (props.type == 'swiper') {
          store.commit('setListState', false)
        }
      }
      let touchMove = e => {
        if (!props.isPullDown) return false
        let scrollTop = proxy.$refs.oeScroll.scrollTop
        //首先判断有没有滚动条，如果有，下拉刷新就不能启用。
        if (scrollTop > 0) return
        let moveX = e.targetTouches[0].clientX - state.startX
        let move = e.targetTouches[0].clientY - state.startY
        if (props.type == 'swiper') {
          let listState = store.state.listState
          if (moveX > 3 && !listState) return
          if (moveX < -3 && !listState) return
          store.commit('setListState', true)
        }
        //判断手指滑动的距离，只有为正数才代表用户下拉了。
        if (move > 0) {
          //增加滑动阻力的感觉
          state.moveDistance = Math.pow(move, 0.8)
          if (state.moveDistance > 50) {
            //如果滑动距离大于50 释放即可刷新
            if (state.moveState === 1) return
            state.moveState = 1
          } else {
            //否则 恢复原样
            if (state.moveState === 0) return
            state.moveState = 0
          }
        } else if (props.type == 'swiper') {
          store.commit('setListState', false)
        }
      }
      let touchEnd = () => {
        if (!props.isPullDown) return false
        // 手指拿开，需要加上结束时的动画，这里为300ms
        state.duration = 300
        if (props.type == 'swiper') {
          store.commit('setListState', false)
        }
        if (state.moveDistance > 50) {
          state.moveState = 2
          state.moveDistance = 50
          if (props.isBottm) bottomShow.value = false
          context.emit('refresh', () => {
            //成功后的回调了，如果该函数被调用，那就意味着加载数据完成，所以状态要回到0，需要在父组件调用。
            state.moveState = 0
            if (props.isBottm) bottomShow.value = true
          })
        } else {
          state.moveDistance = 0
        }
      }
      let scrollTop = (offset = 0) => {
        if (offset > 0) {
          proxy.$refs.oeScroll.scrollTo(0, offset)
        } else {
          goAnchor()
        }
      }


      const goAnchor = () => {
        //锚点定位
        proxy.$refs.oeScroll.querySelector('.scroll_top_dw').scrollIntoView({
          behavior: 'smooth'
        })
      }
      const style = computed(() => {
        return {
          transition: `${state.duration}ms`,
          transform: `translate3d(0,${state.moveDistance}px, 0)`
        }
      })
      watch(
        () => state.moveState,
        newValue => {
          if (newValue === 0 && state.duration === 300) {
            state.moveDistance = 0
          }
        }
      )
      watch(
        () => props.status,
        newValue => {
          result.value = newValue
        },
        { immediate: true }
      )

      let scrollY = ref(0)
      let bottomFlag = false
      const handlerScroll = e => {

        if (props.type == 'swiper') {
          store.commit('setListState', true)
        }
        const dom = e.target
        let scrollTop = dom.scrollTop //滑入屏幕上方的高度
        let windowHeitht = dom.clientHeight //能看到的页面的高度
        let scrollHeight = dom.scrollHeight //监控的整个div的高度（包括现在看到的和上下隐藏起来看不到的）
        let total = scrollTop + windowHeitht
        context.emit('handleScroll', scrollTop)
        scrollY.value = dom.scrollTop
        if (total >= scrollHeight - 3 && !bottomFlag) {
          bottomFlag = true
          if (result.value == 'loading') {
            //下拉加载之后显示文字（没有更多内容了），第一回加载则不显示
            isbottom.value = true
          }
          context.emit('scrollBottom', res => {
            result.value = res
          })
        } else {
          bottomFlag = false
        }
      }
      //记录滚动位置
      const route = useRoute()
      onActivated(() => {
        //如果路由动作为返回则触发
        if (scrollY.value && (route.meta.transitionName == 'backoff' || route.meta.isListScroll)) {
          proxy.$refs.oeScroll.scrollTo(0, scrollY.value)
        }
      })

      return {
        ...toRefs(state),
        result,
        style,
        isbottom,
        bottomShow,
        touchStart,
        touchMove,
        touchEnd,
        handlerScroll,
        scrollTop
      }
    }
  }
</script>
<style lang="scss" scoped>
  @import './icon/iconfont.css';
  .scroll {
    position: relative;
    height: calc(100%);
  }
  .oe_list {
    position: relative;
    height: 100%;
    cursor: pointer;
    .tips {
      position: absolute;
      left: 0;
      top: -1.333rem;
      right: 0;
      line-height: 1.333rem;
      height: 1.333rem;
      font-size: 0.373rem;
      text-align: center;
      color: #787878;
      overflow: hidden;
      .loading {
        width: 0.48rem;
        vertical-align: middle;
        margin-bottom: 0.107rem;
        margin-right: 0.267rem;
      }
      .pulldown {
        width: 0.64rem;
        vertical-align: middle;
        transition: all 0.2s;
        margin-right: 0.053rem;
        &.rot {
          transform: rotate(180deg);
        }
      }
    }
    .content {
      font-family: PingFang SC, PingFang SC;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      overflow-y: scroll;
      box-sizing: border-box;
      user-select: none;
      -webkit-overflow-scrolling: touch;
      &::-webkit-scrollbar {
        display: none;
      }
      .load_tips {
        text-align: center;
        color: #787878;
        line-height: 1.067rem;
        font-size: 0.373rem;
        font-weight: normal;
        .loading {
          width: 0.48rem;
          vertical-align: middle;
          margin-bottom: 0.107rem;
          margin-right: 0.267rem;
        }
      }
      .no_data {
        color: #999999;
        font-size: 0.373rem;
        text-align: center;
        padding: .8rem 0 0.267rem;
        font-weight: normal;
        padding-bottom: 2.6667rem;
        .text {
          padding-top: 0.133rem;
          font-weight: normal;
        }
        img {
          width: 4.533rem;
        }
        .oeuifont {
          font-size: 1.067rem;
          line-height: 1.067rem;
          padding-bottom: 0.267rem;
          font-weight: normal;
        }
      }
      .no_result {
        color: #999999;
        text-align: center;
        font-weight: normal;
        font-size: 0.373rem;
        line-height: 1.067rem;
      }
    }
  }
</style>

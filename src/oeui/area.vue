<template>
  <oeui-popup ref="area" mode="bottom" :round="true">
    <div class="oe_area">
      <div class="flex flex_ac flex_jsb" style="padding: 0 0.4rem">
        <div class="title">{{ title }}</div>
        <span @click="confirm" class="btn">确定</span>
      </div>
      <div class="tab_list">
        <div ref="distOne" class="tab" :class="dist1 == 0 ? 'c9' : ''" @click="selectDist('distOne', 0)">{{ dist1Text }}</div>
        <div ref="distTwo" class="tab" :class="dist2 == 0 ? 'c9' : ''" @click="selectDist('distTwo', 1)" v-show="dist1 > 0">{{ dist2Text }}</div>
        <div ref="distThree" class="tab" :class="dist3 == 0 ? 'c9' : ''" @click="selectDist('distThree', 2)" v-show="dist2 > 0 && row > 2">{{ dist3Text }}</div>
        <div ref="distFour" class="tab" :class="dist4 == 0 ? 'c9' : ''" @click="selectDist('distFour', 3)" v-show="dist3 > 0 && row >= 3">{{ dist4Text }}</div>
        <span class="solid" :style="{ width: width + 'px', transform: 'translateX(' + left + 'px)' }"></span>
      </div>
      <div class="tab_pane" ref="pane">
        <div class="ripple" :style="{ transform: 'translateX(-' + ind * 25 + '%)' }">
          <template v-if="list.length > 0">
            <ul class="ullist" ref="ulOne">
              <li v-for="(item, i) in list" :key="i" @click="selectArea(item, i, 1)" :class="dist1 == item.value ? 'current' : ''">
                <span>{{ item.text }}</span>
                <i class="oeuifont oeui-checkbox"></i>
              </li>
            </ul>
            <ul class="ullist" ref="ulTwo">
              <li v-for="(item, i) in list[index1].children" :key="i" @click="selectArea(item, i, 2)" :class="dist2 == item.value ? 'current' : ''">
                <span>{{ item.text }}</span>
                <i class="oeuifont oeui-checkbox"></i>
              </li>
            </ul>
            <ul class="ullist" ref="ulThree">
              <li v-for="(item, i) in list[index1].children[index2].children" :key="i" @click="selectArea(item, i, 3)" :class="dist3 == item.value ? 'current' : ''">
                <span>{{ item.text }}</span>
                <i class="oeuifont oeui-checkbox"></i>
              </li>
            </ul>
            <ul class="ullist" ref="ulFour">
              <li v-for="(item, i) in list[index1]?.children[index2]?.children[index3]?.children" :key="i" @click="selectArea(item, i, 4)" :class="dist4 == item.value ? 'current' : ''">
                <span>{{ item.text }}</span>
                <i class="oeuifont oeui-checkbox"></i>
              </li>
            </ul>
          </template>
        </div>
      </div>
    </div>
  </oeui-popup>
</template>
<script>
import oeuiPopup from './popup.vue'
import { getCurrentInstance, ref, reactive, nextTick, watch } from 'vue'
export default {
  components: {
    oeuiPopup,
  },
  props: {
    list: {
      type: Array,
      required: true,
    },
    row: {
      type: Number,
      default: 3,
    },
    title: {
      type: String,
      default: '请选择居住地',
    },
    current: {
      type: Array,
    },
  },
  setup(props, context) {
    const { proxy } = getCurrentInstance()
    const width = ref('40')
    const left = ref('16')
    const ind = ref(0)
    const dist1Text = ref('请选择')
    const dist2Text = ref('请选择')
    const dist3Text = ref('请选择')
    const dist4Text = ref('请选择')
    const dist1 = ref('0')
    const dist2 = ref('0')
    const dist3 = ref('0')
    const dist4 = ref('0')
    const index1 = ref(0)
    const index2 = ref(0)
    const index3 = ref(0)
    const index4 = ref(0)
    const status = ref(true)
    const arrList = ref([])
    let { row, list, current } = reactive(props)
    arrList.value = list
    const open = () => {
      proxy.$refs.area.open()
      if (status.value) {
        setCurrent()
        status.value = false
      }
    }

    const closeArea = () => {
      proxy.$refs.area.close()
    }

    const changeArea = () => {
      let arr
      let obj1, obj2, obj3, obj4
      let list1 = arrList.value[index1.value] || ''
      obj1 = list1 ? { i: list1.i || index1.value + 1, value: list1.value || '', text: list1.text || '' } : {}
      obj2 = {}
      obj3 = {}
      obj4 = {}
      arr = [obj1]
      if (row >= 2) {
        let list2 = arrList.value[index1.value]?.children[index2.value] || ''
        obj2 = list2 ? { i: list2.i || index2.value + 1, value: list2.value || '', text: list2.text || '' } : {}
        arr.push(obj2)
      }
      if (row == 3) {
        let list3 = arrList.value[index1.value]?.children[index2.value].children[index3.value] || ''
        obj3 = list3 ? { i: list3.i || index3.value + 1, value: list3.value || '', text: list3.text || '' } : {}
        arr.push(obj3)
      }
      if (row >= 3) {
        let list4 = arrList.value[index1.value]?.children[index2.value]?.children[index3.value]?.children[index4.value] || ''
        obj4 = list4 ? { i: list4.i || index4.value + 1, value: list4.value || '', text: list4.text || '' } : {}
        arr.push(obj4)
      }
      context.emit('change', arr)
    }

    const setCurrent = async () => {
      if (current && current[0] > 0 && current[0] != '') {
        let index = arrList.value.map(item => item.value).indexOf(current[0])
        dist1.value = current[0]
        index1.value = index > 0 ? index : 0
        dist1Text.value = list[index1.value].text
      }
      if (current && current[1] > 0 && current[1] != '') {
        ind.value = 1
        if (arrList.value[index1.value]?.children.length) {
          let index = arrList.value[index1.value]?.children.map(item => item.value).indexOf(current[1])
          dist2.value = current[1]
          index2.value = index > 0 ? index : 0
          dist2Text.value = list[index1.value]?.children[index2.value].text
        }
      }
      if (current && current[2] > 0 && row > 2 && current[2] != '') {
        ind.value = 2
        if (arrList.value[index1.value]?.children[index2.value]?.children.length) {
          let index = arrList.value[index1.value]?.children[index2.value]?.children.map(item => item.value).indexOf(current[2])
          dist3.value = current[2]
          index3.value = index > 0 ? index : 0
          dist3Text.value = arrList.value[index1.value]?.children[index2.value]?.children[index3.value].text
        }
      }
      if (current && current[3] > 0 && row >= 3 && current[3] != '') {
        ind.value = 3
        if (arrList.value[index1.value]?.children[index2.value]?.children[index3.value]?.children.length) {
          let index = arrList.value[index1.value]?.children[index2.value]?.children[index3.value]?.children.map(item => item.value).indexOf(current[3])
          dist4.value = current[3]
          index4.value = index > 0 ? index : 0
          dist4Text.value = arrList.value[index1.value]?.children[index2.value]?.children[index3.value]?.children[index4.value].text
        }
      }
      if (current) {
        await nextTick(() => {
          scrollCurrent()
          animationArea()
        })
      }
    }

    const scrollCurrent = () => {
      let list = proxy.$refs.pane.querySelectorAll('.ullist')
      let top1 = list[0].querySelector('.current') ? list[0].querySelector('.current').offsetTop : ''
      let top2 = list[1].querySelector('.current') ? list[1].querySelector('.current').offsetTop : ''
      let top3 = list[2].querySelector('.current') ? list[2].querySelector('.current').offsetTop : ''
      let top4 = list[3].querySelector('.current') ? list[3].querySelector('.current').offsetTop : ''
      if (top1 >= 0) {
        proxy.$refs.ulOne.scrollTop = top1
      }
      if (top2 >= 0) {
        proxy.$refs.ulTwo.scrollTop = top2
      }
      if (top3 >= 0) {
        proxy.$refs.ulThree.scrollTop = top3
      }
      if (top4 >= 0) {
        proxy.$refs.ulFour.scrollTop = top4
      }
    }

    const selectDist = (e, i) => {
      width.value = proxy.$refs[e].offsetWidth
      left.value = proxy.$refs[e].offsetLeft
      ind.value = i
      if (i == 0) {
        dist2Text.value = '请选择'
        dist2.value = '0'
        index2.value = 0

        dist3Text.value = '请选择'
        dist3.value = '0'
        index3.value = 0

        dist4Text.value = '请选择'
        dist4.value = '0'
        index4.value = 0
      }
      if (i == 1) {
        dist3Text.value = '请选择'
        dist3.value = '0'
        index3.value = 0

        dist4Text.value = '请选择'
        dist4.value = '0'
        index4.value = 0
      }
      if (i == 2) {
        dist4Text.value = '请选择'
        dist4.value = '0'
        index4.value = 0
      }
    }

    const animationArea = () => {
      if (ind.value == 0) {
        width.value = proxy.$refs.distOne.offsetWidth
        left.value = proxy.$refs.distOne.offsetLeft
      } else if (ind.value == 1) {
        width.value = proxy.$refs.distTwo.offsetWidth
        left.value = proxy.$refs.distTwo.offsetLeft
      } else if (ind.value == 2) {
        width.value = proxy.$refs.distThree.offsetWidth
        left.value = proxy.$refs.distThree.offsetLeft
      } else if (ind.value == 3) {
        width.value = proxy.$refs.distFour.offsetWidth
        left.value = proxy.$refs.distFour.offsetLeft
      }
    }

    const selectArea = async (item, index, type) => {
      if (type == 1) {
        index1.value = index
        dist1.value = item.value
        dist1Text.value = item.text
        dist2.value = 0
        index2.value = 0
        dist2Text.value = '请选择'
        dist3.value = 0
        index3.value = 0
        dist3Text.value = '请选择'
        dist4.value = 0
        index4.value = 0
        dist4Text.value = '请选择'
        if (item.value == 0 || item.value == '') {
          await nextTick(() => {
            changeArea()
            closeArea()
          })
          return
        }
        ind.value = 1
        proxy.$refs.ulTwo.scrollTop = 0
      } else if (type == 2) {
        index2.value = index
        dist2.value = item.value
        dist2Text.value = item.text
        if (row == 2 || arrList.value[index1.value]?.children[index2.value]?.children.length == 0) {
          await nextTick(() => {
            changeArea()
            closeArea()
          })
          return
        }
        dist3.value = 0
        index3.value = 0
        dist3Text.value = '请选择'
        ind.value = 2
        proxy.$refs.ulThree.scrollTop = 0
      } else if (type == 3) {
        index3.value = index
        dist3.value = item.value
        dist3Text.value = item.text
        if (row >= 3 && arrList.value[index1.value]?.children[index2.value].children[index3.value]?.children.length == 0) {
          await nextTick(() => {
            changeArea()
            closeArea()
          })
          return
        }
        dist4.value = 0
        index4.value = 0
        dist4Text.value = '请选择'
        ind.value = 3
        proxy.$refs.ulFour.scrollTop = 0
      } else if (type == 4) {
        index4.value = index
        dist4.value = item.value
        dist4Text.value = item.text
        await nextTick(() => {
          changeArea()
          closeArea()
        })
      }
      await nextTick(() => {
        animationArea()
      })
    }

    const confirm = () => {
      let arr = []
      if (dist1.value > 0) {
        arr.push({
          i: index1.value || '',
          value: dist1.value || '',
          text: dist1Text.value != '请选择' ? dist1Text.value : '',
        })
      }
      if (dist2.value > 0) {
        arr.push({
          i: index2.value || '',
          value: dist2.value || '',
          text: dist2Text.value != '请选择' ? dist2Text.value : '',
        })
      }
      if (dist3.value > 0) {
        arr.push({
          i: index3.value || '',
          value: dist3.value || '',
          text: dist3Text.value != '请选择' ? dist3Text.value : '',
        })
      }
      if (dist4.value > 0) {
        arr.push({
          i: index3.value || '',
          value: dist4.value || '',
          text: dist3Text.value != '请选择' ? dist3Text.value : '',
        })
      }
      closeArea()
      context.emit('change', arr)
    }

    watch(
      () => props.list,
      newValue => {
        arrList.value = newValue
      },
      { deep: true, immediate: true },
    )

    watch(
      () => props.current,
      newValue => {
        if (newValue[0]) {
          current = newValue
          setCurrent()
        }
      },
      { deep: true, immediate: true },
    )
    return {
      open,
      closeArea,
      selectDist,
      selectArea,
      changeArea,
      setCurrent,
      scrollCurrent,
      animationArea,
      width,
      left,
      ind,
      dist1Text,
      dist2Text,
      dist3Text,
      dist4Text,
      dist1,
      dist2,
      dist3,
      dist4,
      index1,
      index2,
      index3,
      index4,
      confirm,
    }
  },
}
</script>
<style lang="scss" scoped>
@import './icon/iconfont.css';

.oe_area {
  position: relative;

  .title {
    position: relative;
    height: 1.28rem;
    line-height: 1.28rem;
    color: $color_3;
    font-size: 0.427rem;

    .oeuifont {
      position: absolute;
      right: 0;
      top: 0;
      color: $color_9;
      font-size: 0.533rem;
      padding: 0 0.4rem;
      cursor: pointer;
    }
  }
  .btn {
    background: $color_main;
    color: #fff;
    border-radius: 0.1333rem;
    padding: 0.08rem 0.2667rem;
    cursor: pointer;
  }

  .tab_list {
    position: relative;
    display: flex;
    line-height: 1.28rem;
    height: 1.28rem;
    padding: 0 0.16rem;
    z-index: 10;

    .tab {
      margin: 0 0.267rem;
      font-size: 0.373rem;
      cursor: pointer;

      &.c9 {
        color: $color_9;
      }
    }

    .solid {
      display: inline-block;
      position: absolute;
      width: 1.067rem;
      height: 0.08rem;
      background: $color_main;
      left: 0;
      bottom: 0;
      transform: translateX(0.427rem);
      transition: all 0.3s;
    }
  }

  .tab_pane {
    position: relative;
    height: 6.1867rem;
    overflow: hidden;
    padding-top: 0.267rem;

    .ripple {
      width: 400%;
      height: 100%;
      display: flex;
      transition: all 0.3s;
      transform: translateX(0);

      .ullist {
        flex: 1;
        overflow-y: auto;

        li {
          padding: 0.267rem 0.4rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: $color_3;
          font-size: 0.373rem;

          i {
            display: none;
          }

          &.current {
            color: $color_main;

            i {
              font-size: 0.48rem;
              display: block;
            }
          }
        }
      }
    }
  }
}
</style>

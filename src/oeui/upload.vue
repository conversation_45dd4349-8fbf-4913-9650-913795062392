<template>
  <div class="oe_upload">
    <div class="preview" v-for="(item,i) in listImg" :key="i">
      <img src="./images/meinv34.jpg">
      <span class="remove" @click="removeImg(i)">
        <i class="oeuifont oeui-guanbi1"></i>
      </span>
    </div>
    <div class="upload" @click="upload">
      <i class="oeuifont oeui-jia"></i>
      <input ref="uploadImg" type="file" accept="image/*" @change="changeUpload($event)" multiple="multiple" style="display:none;">
    </div>
  </div>
</template>
<script>
import { getCurrentInstance, reactive } from 'vue';
export default {
  setup() {
    const { proxy } = getCurrentInstance();
    let listImg = reactive([]);
    const upload = () =>{
      proxy.$refs.uploadImg.dispatchEvent(new MouseEvent('click'));
    }
    const changeUpload = (e)=>{
      let inputFile = Array.from(e.target.files);
      inputFile.forEach(v => {
        listImg.push(v)
      });
    }
    const removeImg = (index)=>{
      listImg.splice(index,1)
    }
    const getBase64 = (file)=>{
      return new Promise((resolve, reject) => {//转base64 方法
        let reader = new FileReader();
        let fileResult = "";
        reader.readAsDataURL(file);
        reader.onload = function() {
          fileResult = reader.result;
        };
        reader.onerror = function(error) {
          reject(error);
        };
        reader.onloadend = function() {
          resolve(fileResult);
        };
      });
    }
    return {
      listImg,
      upload,
      changeUpload,
      removeImg
    }
  }
};
</script>
<style lang='scss' scoped>
@import './icon/iconfont.css';
.oe_upload{
  position: relative;
  display:flex;
  flex-wrap: wrap;
  width:7.04rem;
  .preview{
    position: relative;
    width:2.133rem;
    height:2.133rem;
    margin:0 .213rem .213rem 0;
    cursor: pointer;
    img{
      width:100%;
      height:100%;
      object-fit:cover;
      border-radius:.133rem;
    }
    .remove{
      position:absolute;
      right:-.133rem;
      top:-.133rem;
      width:.48rem;
      height:.48rem;
      line-height:.48rem;
      color:#ffffff;
      background:rgba(0, 0, 0, 0.8);
      text-align: center;
      border-radius:50%;
      i{
        font-size:.373rem;
      }
    }
  }
  .upload{
    width:2.133rem;
    height:2.133rem;
    color:#dcdee0;
    background:#f7f8fa;
    border-radius:.133rem;
    cursor:pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin:0 .213rem .213rem 0;
    .oeuifont{
      font-size:.533rem;
    }
  }
}
</style>
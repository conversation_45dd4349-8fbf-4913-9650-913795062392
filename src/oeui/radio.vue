<template>
  <div class="oe_radio" :class="value == val ? 'checked': ''" @click="changeVal">
    <span class="text">{{text}}</span>
    <div class="icon"><i class="oeuifont oeui-checkbox"></i></div>
  </div>
</template>
<script>
export default {
  props:{
    text:{
      type:String,
      default:''
    },
    val:{
      default:'1'
    },
    value:null
  },
  setup(props,context) {
    const changeVal = ()=>{
      context.emit('input',props.val);
    }
    return {
      changeVal
    }
  }
};
</script>
<style lang='scss' scoped>
.oe_radio{
  position: relative;
  display: flex;
  line-height:.533rem;
  margin:0 .267rem;
  padding:.32rem 0;
  cursor:pointer;
  border-bottom:.027rem solid #f5f5f5;
  .icon{
    width:.533rem;
    height:.533rem;
    line-height:.533rem;
    border-radius:50%;
    color:#ffffff;
    background:#ffffff;
    text-align: center;
    border:.027rem solid #c8c9cc;
    box-sizing: border-box;
    transition: all .2s;
    i{
      font-size:.347rem;
    }
  }
  &.checked > .icon{
    background:$color_main;
    border:.027rem solid $color_main;
  }
  .text{
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size:.373rem;
    padding-right:.267rem;
  }
  &:last-child{
    border-bottom:none;
  }
  &:active{
    opacity:.8;
  }
}
</style>
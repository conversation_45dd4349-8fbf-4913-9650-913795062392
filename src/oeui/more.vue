<!-- @format -->

<template>
  <oeui-popup ref="more" mode="bottom" :round="true">
    <div class="oe_more">
      <!-- <span class="close oeuifont oeui-guanbi" @click="close"></span> -->
      <h3 class="title">{{ title }}</h3>
      <p class="sm">支持多项选择</p>
      <div class="list">
        <dl class="item" :class="arr.includes(item) ? 'current' : ''" v-for="(item, i) in list" :key="i" @click="selectValue(item)">
          <dt><i class="oeuifont oeui-duoxuankuangdagou"></i></dt>
          <dd class="text">{{ item.text }}</dd>
        </dl>
      </div>
      <div class="btn_box">
        <div class="btn" :class="arr.length == 0 ? 'no_click' : ''" @click="submit"><i>提交</i></div>
      </div>
    </div>
  </oeui-popup>
</template>
<script>
  import oeuiPopup from './popup.vue'
  import { getCurrentInstance, ref, reactive, watch } from 'vue'
  export default {
    components: {
      oeuiPopup
    },
    props: {
      list: {
        type: Array,
        required: true
      },
      title: {
        type: String,
        default: '学历要求'
      },
      current: {
        type: String
      }
    },
    setup(props, context) {
      const { proxy } = getCurrentInstance()
      const arr = ref([])
      let { current, list } = reactive(props)
      const setCurrent = () => {
        let curArr = current.split(',')
        arr.value = []
        curArr.forEach(v => {
          let index = list.findIndex(item => item.value == v)
          if (index >= 0) {
            arr.value.push(list[index])
          }
        })
      }
      if (current && list) {
        setCurrent()
      }
      const open = () => {
        proxy.$refs.more.open()
      }
      const close = () => {
        proxy.$refs.more.close()
      }
      const selectValue = item => {
        if (arr.value.includes(item)) {
          arr.value.splice(arr.value.indexOf(item), 1)
        } else {
          if (item.value == 0) {
            arr.value = [item]
          } else {
            if (arr.value.includes(list[0]) && list[0].value == 0) {
              arr.value.splice(arr.value.indexOf(list[0]), 1)
            }
            arr.value.push(item)
          }
        }
      }
      const submit = () => {
        if (arr.value.length == 0) {
          return
        }
        context.emit('confirm', arr.value)
        close()
      }

      watch(
        () => props.current,
        newValue => {
          if (newValue) {
            current = newValue
            setCurrent()
          }
        },
        { deep: true, immediate: true }
      )
      return {
        arr,
        open,
        close,
        selectValue,
        submit
      }
    }
  }
</script>
<style lang="scss" scoped>
  .oe_more {
    position: relative;
    .title {
      text-align: center;
      padding-top: 0.8rem;
      font-size: 0.48rem;
      color: #333333;
    }
    .sm {
      padding: 0.267rem 0 0.533rem;
      font-size: 0.373rem;
      color: #999999;
      text-align: center;
    }
    .close {
      position: absolute;
      left: 50%;
      bottom: -1.333rem;
      transform: translateX(-50%);
      width: 0.8rem;
      height: 0.8rem;
      line-height: 0.8rem;
      font-size: 0.427rem;
      border-radius: 50%;
      display: inline-block;
      color: #ffffff;
      border: 2px solid #ffffff;
      text-align: center;
      cursor: pointer;
    }
    .list {
      padding: 0 1.333rem;
      display: flex;
      flex-wrap: wrap;
      max-height: 5.867rem;
      overflow-y: scroll;
      .item {
        width: 47%;
        min-height: 1.04rem;
        border-radius: 1rem;
        border: 1px solid #e2e2e2;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        margin-bottom: 0.373rem;
        background: #ffffff;
        cursor: pointer;
        dt {
          margin-right: 0.133rem;
          width: 0.347rem;
          height: 0.347rem;
          line-height: 0.347rem;
          border: 1px solid #e2e2e2;
          text-align: center;
          margin-left: 0.267rem;
          border-radius: 0.08rem;
          color: #ffffff;
          .oeuifont {
            font-size: 0.32rem;
            font-weight: bold;
          }
        }
        .text {
          font-size: 0.373rem;
          text-align: center;
          line-height: 0.533rem;
          padding: 0.08rem 0.267rem 0.08rem 0;
        }
        &:nth-child(even) {
          margin-left: 6%;
        }
        &.current {
          background: $color_main;
          border: 1px solid $color_main;
          dt {
            background: #ffffff;
            color: $color_main;
          }
          .text {
            color: #ffffff;
          }
        }
      }
    }
    .btn_box {
      padding: 0.8rem 1.333rem;
      .btn {
        position: relative;
        margin: 0 auto;
        height: 1.28rem;
        line-height: 1.28rem;
        border-radius: 1.28rem;
        text-align: center;
        color: #ffffff;
        font-size: 0.427rem;
        background: $color_main;
        i {
          position: relative;
          z-index: 1;
        }
        &.no_click {
          opacity: 0.2;
        }
      }
    }
  }
</style>

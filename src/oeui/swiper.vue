<!-- @format -->

<template>
  <div ref="OESwiper" class="oe_swiper" @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd" :style="{ top: top, bottom: bottom }">
    <div class="oe_slide" :style="style">
      <slot></slot>
    </div>
  </div>
</template>
<script>
  import { reactive, toRefs, getCurrentInstance, watch, onMounted } from 'vue'
  import { useStore } from 'vuex'
  export default {
    props: {
      top: {
        type: String,
        default: '0'
      },
      bottom: {
        type: String,
        default: '0'
      },
      length: {
        type: Number,
        default: 1
      },
      current: {
        type: Number,
        default: 0
      },
      hasList: {
        type: Boolean,
        default: false
      },
      isTouch: {
        type: Boolean,
        default: true
      }
    },
    setup(props, context) {
      const { proxy } = getCurrentInstance()
      const OEUI = proxy.OEUI
      const store = useStore()
      const style = reactive({
        transform: 'translate3d(0px,0px,0px)',
        transition: 'transform .3s'
      })
      const state = reactive({
        bannerWidth: 0, //轮播图宽度
        startPoint: 0, //触摸开始的点的横坐标
        endPoint: 0, //触摸结束的点的横坐标
        moveLength: 0, //startPoint与endPoint的差值
        current: 0, //当前轮播图的索引
        lengths: 1
      })
      onMounted(() => {
        // let html1 = proxy.$refs.OESwiper.getElementsByClassName('oe_item')[0].innerHTML;
        // let html2 = proxy.$refs.OESwiper.getElementsByClassName('oe_item')[props.length-1].innerHTML;
        // let box = proxy.$refs.OESwiper.querySelector('.oe_slide').innerHTML
        // box = html2 + box + html1
        // proxy.$refs.OESwiper.querySelector('.oe_slide').innerHTML = box
        // 前后插入元素JS
      })
      const touchStart = e => {
        if (!props.isTouch) return
        state.startPoint = e.changedTouches[0].pageX
      }
      const touchMove = e => {
        if (!props.isTouch) return
        state.endPoint = e.changedTouches[0].pageX
        animations()
      }
      const touchEnd = () => {
        if (!props.isTouch) return
        jump()
        state.moveLength = 0
      }
      const jump = () => {
        style.transition = 'transform .4s'
        //滑动超过轮播图宽度的百分之25，则跳转下一张，否则不跳转
        if (state.moveLength > 0 && state.current !== state.lengths - 1) {
          if (state.moveLength > state.bannerwidth * 0.12) {
            state.current++
            context.emit('bindchange', state.current)
          }
          style.transform = `translate3d(${-state.current * state.bannerwidth}px,0px,0px)`
        } else if (state.moveLength < 0 && state.current !== 0) {
          if (-state.moveLength > state.bannerwidth * 0.12) {
            state.current--
            context.emit('bindchange', state.current)
          }
          style.transform = `translate3d(${-state.current * state.bannerwidth}px,0px,0px)`
        }
      }
      const animations = () => {
        if (state.startPoint === state.endPoint) {
          return
        }
        style.transition = 'none'
        state.moveLength = state.startPoint - state.endPoint
        //获取轮播图的宽度
        state.bannerwidth = proxy.$refs.OESwiper.offsetWidth
        //判断是否超出滑动范围，即第一页无法再往前一页滑动，最后一页无法再往后一页滑动
        if (state.moveLength > 0 && state.current != state.lengths - 1) {
          style.transform = `translate3d(${-state.moveLength - state.current * state.bannerwidth}px,0px,0px)`
        } else if (state.moveLength < 0 && state.current != 0) {
          style.transform = `translate3d(${-state.moveLength - state.current * state.bannerwidth}px,0px,0px)`
        }
      }

      watch(
        () => props.current,
        newValue => {
          if (newValue != state.current) {
            style.transform = `translate3d(${-newValue * proxy.$refs.OESwiper.offsetWidth}px,0px,0px)`
            state.current = newValue
          }
        }
      )
      watch(
        () => props.length,
        newValue => {
          if (newValue) {
            state.lengths = newValue
          }
        },
        { immediate: true, deep: true }
      )

      return {
        ...toRefs(state),
        style,
        touchStart,
        touchMove,
        touchEnd
      }
    }
  }
</script>
<style lang="scss" scoped>
  .oe_swiper {
    position: absolute;
    width: 100%;
    cursor: pointer;
    left: 0;
    right: 0;
    overflow: hidden;
    .oe_slide {
      position: relative;
      display: flex;
      height: 100%;
      cursor: pointer;
    }
  }
</style>

<template>
  <div
    ref="wrapper"
    :style="style"
    @touchstart="touchStart"
    @touchmove.prevent="touchMove"
    @touchend="touchEnd"
    @transitionend="transitionEnd"
  >
    <slot></slot>
  </div>
</template>

<script>
import { reactive, toRefs, getCurrentInstance, watch } from 'vue';
export default {
  props:{
    list:{
      type:Array
    },
    index:{
      type:Number,
      default:0
    }
  },
  setup(props,context) {
    const { proxy } = getCurrentInstance();
    const style = reactive({
      transform: 'translate3d(0px,0px,0px)',
      transition: 'transform .3s'
    });
    const state = reactive({
      activeIndex: 0, // 当前激活的索引
      startY: 0, // 开始距离
      startTime: 0,  // 开始时间
      endY: 0, // 结束距离
      endTime: 0,  // 结束时间
      prevY: 0, // 上一次移动的距离
      direction: 0, // 滑动方向
      maxY: 0,  // 滑动最大距离
      minY: 0, // 滑动最小距离
      optionHeight: 36, // 每一个选项的高度
      optionLength: 21  // 选项的长度
    });
    const { list } = reactive(props);
    state.optionLength = list.length;

    const touchStart = (e) =>{
      // 保存触碰开始的位置
      state.startY = e.touches[0].pageY;
      // 保存触碰开始的时间
      state.startTime = e.timeStamp;
    }

    const touchMove = (e) =>{
      // 保存当前移动的位置
      let height = proxy.$refs.wrapper.offsetHeight - 136;
      let moveY = e.changedTouches[0].pageY;
      // 保存当前移动的方向，往下拉的话，moveY - this.startY为正，往上拉的话为负
      state.direction = moveY - state.startY;
      // 设置拖拽移动
      let distance;
      style.transition = 'none';
      if(state.prevY + state.direction < - height){
        distance = - height;
      }else if(state.prevY + state.direction > state.optionHeight){
        distance = state.optionHeight;
      }else{
        distance = state.prevY + state.direction;
      }
      style.transition = 'none';
      style.transform = `translate3d(0px,${distance}px,0px)`;
    }

    const touchEnd = (e) =>{
      // 设置过渡动画
      style.transition = 'transform .3s';
      // 保存结束位置
      state.endY = e.changedTouches[0].pageY;
      // 保存结束时间
      state.endTime = e.timeStamp;
      // 保存上一次移动的距离
      state.prevY = style.transform.split(',')[1].slice(0, -2) * 1;
      // 计算当前移动到的位置索引
      let activeIndex = -Math.round(state.prevY / state.optionHeight);
      // 计算当前手指从触碰开始到结束移动的距离
      let distance = Math.abs(state.endY - state.startY);
      // 计算当前手指从触碰开始到结束的时间差
      let interval = state.endTime - state.startTime;
      // 根据方向不同来计算应该移动到对应的索引值上
      // 大于0 为向下拉
      if (state.direction > 0) {
        // 通过距离与时间差来计算最新的坐标索引
        activeIndex = activeIndex - Math.round(distance / interval) * 6;
      //  小于 0 为向上拉
      } else if (state.direction < 0) {
        // 通过距离与时间差来计算最新的坐标索引
        activeIndex = activeIndex + Math.round(distance / interval) * 6;
      }
      // 判断当前移动距离特别小，判定为触碰事件，而不是滑动
      if (distance <= 1) {
        // 通过当前点击的元素的offsetTop计算当前元素正确的索引值
        //activeIndex = Math.round((e.path[0].offsetTop - state.optionHeight * 2) / state.optionHeight);
      }
      // 对activeIndex值进行进一步处理，保证其不会超出选项范围
      activeIndex = activeIndex < 0 ? 0 : activeIndex > state.optionLength - 1 ? state.optionLength - 1 : activeIndex;

      // 执行判断并赋值索引
      state.activeIndex = activeIndex;
      context.emit('changeWheel',activeIndex);
      style.transform = `translate3d(0px,${-state.activeIndex * state.optionHeight}px,0px)`;
      transitionEnd();
    }

    const transitionEnd = () =>{
      // 保存上一次移动的距离
      state.prevY = -state.activeIndex * state.optionHeight;
    }

    watch(()=>props.index,(newValue)=>{
      if(newValue != state.activeIndex){
        style.transition = 'none';
        state.prevY = -newValue * state.optionHeight;
        style.transform = `translate3d(0px,${-newValue * state.optionHeight}px,0px)`;
        state.activeIndex = newValue;
      }
    },{deep:true,immediate: true})

    watch(()=>props.list,(newValue)=>{
      state.optionLength = newValue.length;
    },{deep:true,immediate: true})


    return {
      style,
      touchStart,
      touchMove,
      touchEnd,
      transitionEnd,
      ...toRefs(state)
    }
  }
}
</script>
<style lang='scss' scoped>
</style>
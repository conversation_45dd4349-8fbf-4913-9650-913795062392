<template>
    <div v-if="visible" class="oeui_toast" :class="[animations ? 'show' : 'hide', type]">
      {{ text }}
    </div>
</template>

<script>
import { defineComponent, reactive, ref, toRefs } from 'vue'

export default defineComponent({
  setup() {
    const state = reactive({
      text: '',
      time: '',
      type:''
    })

    const visible = ref(false)
    const animations = ref(true)
    let timer
    let animTime

    const close = () => {
      clearTimeout(timer)
      visible.value = false
      timer = null
    }

    const closeAnim = () => {
      clearTimeout(animTime)
      animations.value = false
      animTime = null
    }

    const open = () => {
      if (timer) clearTimeout(timer)
      if (animTime) clearTimeout(animTime)

      visible.value = true
      animations.value = true
      let times = state.time < 400 ? 400 : state.time;
      timer = setTimeout(close, times)
      animTime = setTimeout(closeAnim, times - 150);

      return close
    }

    return {
      ...toRefs(state),
      visible,
      animations,
      open,
      close
    }
  }
})
</script>

<style lang="scss" scoped>
.oeui_toast{
  position: fixed;
  left:50%;
  top:50%;
  transform:translate(-50%,-50%);
  color: #fff;
  word-break: break-all;
  background-color: rgba(0, 0, 0, .7);
  border-radius: .133rem;
  padding: .213rem .4rem;
  z-index:10000;
  &.show{
    animation: showToast .2s ease-out;
  }
  &.hide{
    animation: hideToast .2s ease-out;
  }
  &.top{
    top:10%;
  }
  &.bottom{
    top:90%;
  }
}
@keyframes showToast{
  from{
    opacity:0;
    transform: translate(-50%,-50%) scale(.5);
  }
  to{
    opacity:1;
    transform: translate(-50%,-50%) scale(1);
  }
}
@keyframes hideToast{
  from{
    opacity:1;
    transform: translate(-50%,-50%) scale(1);
  }
  to{
    opacity:0;
    transform: translate(-50%,-50%) scale(.5);
  }
}
</style>
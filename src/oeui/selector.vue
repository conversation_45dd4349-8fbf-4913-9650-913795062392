<template>
  <oeui-popup ref="selector" mode="bottom" :round="true">
    <div class="oe_selector">
      <!-- <span class="close oeuifont oeui-guanbi" @click="close"></span> -->
      <h3 class="title">{{title}}</h3>
      <p class="sm">请选择你的{{title}}</p>
      <div class="list">
        <dl class="item" :class="value == item.value ? 'current':''" v-for="(item,i) in list" :key="i" @click="selectValue(item)">
          <dd class="text">{{item.text}}</dd>
        </dl>
      </div>
      <div class="btn_box">
        <div class="btn" @click="submit"><i>{{btnText}}</i></div>
      </div>
    </div>
  </oeui-popup>
</template>

<script>
import oeuiPopup from './popup.vue'
import { getCurrentInstance, ref, reactive, watch } from 'vue';
export default {
  components:{
    oeuiPopup
  },
  props:{
    list:{
      type:Array,
      required: true
    },
    title:{
      type:String,
      default:'学历'
    },
    current:{
      type:Number
    },
    btnText:{
      type:String,
      default:'提交'
    }
  },
  setup(props,context) {
    const { proxy } = getCurrentInstance();
    const obj = ref([]);
    const value = ref(0);
    const { current, list } = reactive(props);
    if(current && list){
      list.forEach(v => {
        if(v.value == current){
          obj.value = v;
          value.value = current;
        }
      });
    }
    const open = ()=>{
      proxy.$refs.selector.open();
    }
    const close = ()=>{
      proxy.$refs.selector.close();
    }
    const selectValue = (item)=>{
      obj.value = item;
      value.value = item.value;
    }
    const submit = ()=>{
      context.emit('confirm',obj.value);
      close();
    }
    watch(()=>props.current,(newValue)=>{
      if(newValue){
        props.list.forEach(v => {
          if(v.value == newValue){
            obj.value = v;
            value.value = newValue;
          }
        });
      }
    },{deep:true,immediate: true});

    return {
      obj,
      value,
      open,
      close,
      selectValue,
      submit
    }
  }
}
</script>
<style lang='scss' scoped>
@import url('./icon/iconfont.css');
.oe_selector{
  position: relative;
  .title{
    text-align: center;
    padding-top:.8rem;
    font-size:.48rem;
    color:#333333;
  }
  .sm{
    padding-top:.267rem;
    font-size:.373rem;
    color:#999999;
    text-align: center;
  }
  .close {
    position: absolute;
    left: 50%;
    bottom: -1.333rem;
    transform: translateX(-50%);
    width: 0.8rem;
    height: 0.8rem;
    line-height: 0.8rem;
    font-size: 0.427rem;
    border-radius: 50%;
    display: inline-block;
    color: #ffffff;
    border: 2px solid #ffffff;
    text-align: center;
    cursor: pointer;
  }
  .list{
    padding:.533rem 1.333rem 0;
    display:flex;
    flex-wrap: wrap;
    max-height:5.867rem;
    overflow-y: scroll;
    .item{
      width:48%;
      min-height:40px;
      border-radius:1rem;
      border:1px solid #d3dfeb;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      margin-bottom:.32rem;
      background:#ffffff;
      cursor:pointer;
      .text{
        flex:1;
        font-size:.373rem;
        text-align: center;
        line-height:.533rem;
      }
      &:nth-child(even){
        margin-left:4%;
      }
      &.current{
        background:$color_main;
        border:1px solid $color_main;
        dt{
          background:#ffffff;
          color:$color_main;
        }
        .text{
          color:#FFF8EC;
        }
      }
    }
  }
  .btn_box{
    padding:.8rem 1.467rem .933rem;
    .btn{
      position: relative;
      margin:0 auto;
      height:1.333rem;
      line-height:1.333rem;
      border-radius:1.333rem;
      text-align: center;
      color:#ffffff;
      font-size:.427rem;
      background:$color_main;
      i{
        position: relative;
        z-index:1;
      }
    }
  }
}
</style>
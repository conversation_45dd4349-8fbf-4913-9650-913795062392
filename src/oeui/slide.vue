<!-- @format -->

<template>
  <div class="swiper-container" :style="{ height: height + 'rem' }">
    <div class="swiper-wrapper" :style="{ flexDirection: `${vertical ? 'column' : 'row'}`, height: height + 'rem' }">
      <div class="swiper-slide" :style="[{ transform: `${animate ? (vertical ? 'translateY(-100%)' : 'translateX(-100%)') : 'none'}` }, { transition: `${animate ? 'all 2s' : 'none'}` }, { height: height + 'rem' }, { lineHeight: height + 'rem' }]" v-for="(item, index) in list" :key="index">
        <p>{{ item?.title }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, defineProps, onMounted } from 'vue'
  const { proxy } = getCurrentInstance()

  const props = defineProps({
    duration: {
      //滚动时长
      type: Number,
      default: 4000
    },
    data: {
      type: Array,
      required: true
    },
    vertical: {
      //是否纵向
      type: Boolean,
      default: true
    },
    height: {
      type: Number,
      default: 5.3333
    }
  })

  const list = ref(props.data)
  const timer = ref(null)
  const animate = ref(false)

  onMounted(() => {
    timer.value = setInterval(scroll, props.duration)
  })

  const scroll = () => {
    animate.value = true
    setTimeout(() => {
      list.value.push(list.value.shift()) //删除数组的第一个元素并添加到数组的最后一项
      animate.value = false //滑动完成后取消过渡动画，实现无缝滚动
    }, props.duration / 2) //滑动item项必须要在每次轮播开始前,所以时间必须比duration短
  }
</script>

<style lang="scss" scoped>
  .swiper-container {
    width: 100%;
    overflow: hidden;
  }

  .swiper-wrapper {
    width: 90%;
    display: flex;
  }

  .swiper-slide {
    width: 100%;
    flex: 1;
    display: flex;
    align-items: center;
    p {
      max-width: 90%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      color: #666;
    }
  }
</style>

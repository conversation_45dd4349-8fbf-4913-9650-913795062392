import axios from '@/utils/axios';



export const getAuditUser = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'audituser',
    ...params
  });
}


export const getAuditUserDetail = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'audituser',
    a: 'detail',
    ...params
  });
}




export const auditPass = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'audituser',
    a: 'pass',
    ...params
  });
}


export const auditFail = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'audituser',
    a: 'fail',
    ...params
  });
}
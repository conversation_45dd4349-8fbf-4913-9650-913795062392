import axios from '@/utils/axios';

export const initIdrz = () => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'idrz',
    a: 'init'
  });
}

export const sendIdrz = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'idrz',
    a: 'submit',
    ...params
  });
}


export const resultIdrz = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'idresult',
    a: 'dorz',
    ...params
  });
}


export const initIdrz2 = () => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'staffrz',
    a: 'init'
  });
}

export const sendIdrz2 = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'staffrz',
    a: 'submit',
    ...params
  });
}

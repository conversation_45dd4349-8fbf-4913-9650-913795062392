import axios from '@/utils/axios';



export const getCpTj = () => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'my',
    a: 'tj'
  });
}


export const getTjWork = () => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'my',
    a: 'tjwork'
  });
}


export const checkUser = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'beuser',
    a: 'check',
    ...params
  });
}


export const openUser = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'beuser',
    a: 'be',
    ...params
  });
}




export const getKefu = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'kefu',
    ...params
  });
}

export const getKefuDetail = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'kefu',
    a: 'detail',
    ...params
  });
}

export const getKefuOne = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'kefu',
    a: 'one',
    ...params
  });
}


export const getMyLeader = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'my',
    a: 'myleader',
    ...params
  });
}


export const getMyPower = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'my',
    a: 'mylevel',
    ...params
  });
}


export const getPosterQrCode = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'poster',
    ...params
  });
}




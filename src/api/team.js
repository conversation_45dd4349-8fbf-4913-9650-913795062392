import axios from '@/utils/axios';

export const getTeamUnionContact = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'teamunion',
    a: 'contact',
    ...params
  });
}



export const getTeamList = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'team',
    ...params
  });
}


export const joinTeam = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'team',
    a: 'in',
    ...params
  });
}

export const quitTeam = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'team',
    a: 'out',
    ...params
  });
}



export const joinTeamList = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'teamjoin',
    s_type: '1',
    ...params
  });
}

export const quitTeamList = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'teamjoin',
    s_type: '2',
    ...params
  });
}


export const getTeamUnion = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'teamunion',
    ...params
  });
}

export const getTeamUser = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'teamuser',
    ...params
  });
}
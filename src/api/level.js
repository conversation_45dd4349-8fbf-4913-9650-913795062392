import axios from '@/utils/axios';

//推广
export const getLevel1 = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'level1',
    ...params
  });
}
export const getLevel1Detail = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'level1',
    a: 'detail',
    ...params
  });
}

export const upLevel1Grade = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'level1',
    a: 'submit',
    ...params
  });
}



//团长
export const getLevel2 = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'level2',
    ...params
  });
}
export const getLevel2Detail = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'level2',
    a: 'detail',
    ...params
  });
}

export const upLevel2Grade = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'level2',
    a: 'submit',
    ...params
  });
}


//服务
export const getLevel3 = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'level3',
    ...params
  });
}
export const getLevel3Detail = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'level3',
    a: 'detail',
    ...params
  });
}

export const upLevel3Grade = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'level3',
    a: 'submit',
    ...params
  });
}

//线下
export const getLevel4 = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'level4',
    ...params
  });
}
export const getLevel4Detail = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'level4',
    a: 'detail',
    ...params
  });
}
export const upLevel4Grade = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'level4',
    a: 'submit',
    ...params
  });
}

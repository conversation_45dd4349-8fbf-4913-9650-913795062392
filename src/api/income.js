
import axios from '@/utils/axios';

export const getTjincome = () => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'my',
    a: 'tjincome'
  });
}



export const getRwincome = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'rwincome',
    ...params
  });
}


export const getRwreg = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'rwreg',
    ...params
  });
}

export const getRwbuy = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'rwbuy',
    ...params
  });
}


/** @format */

import axios from '@/utils/axios'

export const initUnion1 = () => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'open1'
  })
}

export const openUnion1 = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'open1',
    a: 'submit',
    ...params
  })
}

export const checkOpen1 = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'open1',
    a: 'checkopen',
    paynum: params
  })
}

export const initUnion2 = () => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'open2'
  })
}

export const openUnion2 = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'open2',
    a: 'submit',
    ...params
  })
}

export const checkOpen2 = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'open2',
    a: 'checkopen',
    paynum: params
  })
}

export const initUnion3 = () => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'open3'
  })
}

export const openUnion3 = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'open3',
    a: 'submit',
    ...params
  })
}

export const checkOpen3 = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'open3',
    a: 'checkopen',
    paynum: params
  })
}

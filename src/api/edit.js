import axios from '@/utils/axios';

export const uploadImage = (params)=>{
  return axios.post('',{
    m: 'union',
    vuemod: 'vue',
    c: 'upload',
    a: 'image',
    ...params
  });
}

export const editAvatar = (params)=>{
  return axios.post('',{
    m: 'union',
    vuemod: 'vue',
    c: 'edit2',
    a: 'saveavatar',
    ...params
  });
}

export const editUsername = (params)=>{
  return axios.post('',{
    m: 'union',
    vuemod: 'vue',
    c: 'edit2',
    a: 'saveunionname',
    ...params
  });
}

export const editWeixin = (params)=>{
    return axios.post('',{
      m: 'union',
      vuemod: 'vue',
      c: 'edit2',
      a: 'saveweixin',
      ...params
    });
}

export const editArea = (params)=>{
  return axios.post('',{
    m: 'union',
    vuemod: 'vue',
    c: 'edit2',
    a: 'savedist',
    ...params
  });
}

export const editPassword = (params)=>{
  return axios.post('',{
    m: 'union',
    vuemod: 'vue',
    c: 'edit2',
    a: 'savepwd',
    ...params
  });
}

export const editLeaderName = (params)=>{
  return axios.post('',{
    m: 'union',
    vuemod: 'vue',
    c: 'edit2',
    a: 'saveleadername',
    ...params
  });
}


export const editWinxinCode = (params)=>{
  return axios.post('',{
    m: 'union',
    vuemod: 'vue',
    c: 'edit2',
    a: 'savewxcode',
    ...params
  });
}
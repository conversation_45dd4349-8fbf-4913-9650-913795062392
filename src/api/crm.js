import axios from '@/utils/axios';

export const getCrmmemberUser = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'crmmember',
    ...params
  });
}


export const getCrmmemberUserDetail = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'crmmember',
    a: 'detail',
    ...params
  });
}



export const addCrmmemberUser = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'crmmember',
    a: 'saveadd',
    ...params
  });
}

export const getCrmmemberUserInfo = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'crmmember',
    a: 'edit',
    ...params
  });
}

export const editCrmmemberUser = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'crmmember',
    a: 'saveedit',
    ...params
  });
}
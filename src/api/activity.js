import axios from '@/utils/axios';

export const getActivity = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psactivity',
    ...params
  });
}

export const getActivityDetail = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psactivity',
    a: 'detail',
    ...params
  });
}





export const getActivityQrcode = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psactivity',
    a: 'qrcode',
    ...params
  });
}


export const getMyActivity = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'myactivity',
    ...params
  });
}


export const getActivityBmlist = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'myactivity',
    a: 'bmlist',
    ...params
  });
}


import axios from '@/utils/axios'

export const getUser = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psuser',
    ...params,
  })
}

export const getUserDetail = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psuser',
    a: 'detail',
    ...params,
  })
}

//获取用户相亲卡信息
export const getUserCard = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'blindcard',
    ...params,
  })
}

//获取全部相亲卡模板
export const getUserAlltp = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'blindcard',
    a: 'alltp',
    ...params,
  })
}

//获取单条模板信息
export const getUserOnetp = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'blindcard',
    a: 'onetp',
    ...params,
  })
}

//获取推广二维码
export const getTgQrcode = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'blindcard',
    a: 'qrcode',
    ...params,
  })
}

export const getTgSlogan = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'word',
    a: 'randone',
    ...params,
  })
}
export const getTgSloganAll = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'word',
    ...params,
  })
}

export const getTgSLink = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'tgposter',
    a: 'link',
    ...params,
  })
}

export const saveage = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psuser',
    a: 'saveage',
    ...params,
  })
}

export const savedist = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psuser',
    a: 'savedist',
    ...params,
  })
}

export const savehometown = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psuser',
    a: 'savehometown',
    ...params,
  })
}

export const saveauto = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psuser',
    a: 'saveauto',
    ...params,
  })
}

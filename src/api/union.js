import axios from '@/utils/axios'

export const getMyunion = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'child',
    ...params,
  })
}

export const getMyuser = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'myuser',
    ...params,
  })
}

export const getTgSlogan = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'word',
    a: 'randone',
    ...params,
  })
}
export const getTgSloganAll = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'word',
    ...params,
  })
}

export const getTgPoster = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'tgposter',
    ...params,
  })
}

export const getTgPosterInfo = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'tgposter',
    a: 'get',
    ...params,
  })
}

export const getTgQrcode = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'tgposter',
    a: 'qrcode',
    ...params,
  })
}

export const saveTgPoster = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'tgposter',
    a: 'saveposter',
    ...params,
  })
}

export const getTgLink = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'tgposter',
    a: 'link',
    ...params,
  })
}

export const getUserContact = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'viewsingle',
    ...params,
  })
}

export const getUnionContact = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'child',
    a: 'viewunion',
    ...params,
  })
}

export const auditenter = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'myuser',
    a: 'auditenter',
    ...params,
  })
}

export const unbindenter = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'myuser',
    a: 'unbindenter',
    ...params,
  })
}


import axios from '@/utils/axios'
// 主动帮约
export const getAfterUser = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'afteruser',
    ...params
  });
}

export const getAfterUserDetail = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'afteruser',
    a: 'detail',
    ...params
  });
}



export const getAfterMatch = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'aftermatch',
    ...params
  });
}


export const getAfterMatchResult = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'aftermatch',
    a:'search',
    ...params
  });
}

export const saveAfterMatch = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'aftermatch',
    a:'savematch',
    ...params
  });
}

export const saveShareAfter = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'wxjsapi',
    a:'shareafter',
    ...params
  });
}
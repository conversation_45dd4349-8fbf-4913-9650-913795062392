import axios from '@/utils/axios';

export const getAfterUser = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'after',
    ...params
  });
}


export const afterStart = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'after',
    a: 'start',
    ...params
  });
}


export const afterEnd = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'after',
    a: 'end',
    ...params
  });
}



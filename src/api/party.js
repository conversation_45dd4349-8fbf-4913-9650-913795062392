import axios from '@/utils/axios';

export const getParty = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psparty',
    ...params
  });
}

export const getPartyDetail = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psparty',
    a: 'detail',
    ...params
  });
}





export const getPartyQrcode = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psparty',
    a: 'qrcode',
    ...params
  });
}


export const getMyParty = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'myparty',
    ...params
  });
}



export const getPartyBmlist = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'myparty',
    a: 'bmlist',
    ...params
  });
}



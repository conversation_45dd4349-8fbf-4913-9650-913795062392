import axios from '@/utils/axios'

export const getParty = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psparty',
    ...params,
  })
}

export const getPartyDetail = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psparty',
    a: 'detail',
    ...params,
  })
}

export const getPartyQrcode = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'psparty',
    a: 'qrcode',
    ...params,
  })
}

export const getMyParty = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'myparty',
    ...params,
  })
}

export const getPartyBmlist = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'myparty',
    a: 'bmlist',
    ...params,
  })
}

export const saveApply = params => {
  return axios.post('', {
    m: 'union',
    a: 'saveadd',
    vuemod: 'vue',
    c: 'party',
    ...params,
  })
}

export const editApply = params => {
  return axios.post('', {
    m: 'union',
    a: 'saveedit',
    vuemod: 'vue',
    c: 'party',
    ...params,
  })
}

export const getActivityApply = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'party',
    ...params,
  })
}

export const getActivityApplyDetail = params => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'party',
    a: 'detail',
    ...params,
  })
}

import axios from '@/utils/axios';

export const getMsg = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'msg',
    ...params
  });
}

export const getNewMsg = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'msg',
    a: 'new',
    ...params
  });
}


export const getPlaywall = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'playwall',
    ...params
  });
}
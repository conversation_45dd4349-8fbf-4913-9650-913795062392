import axios from '@/utils/axios';


export const getWithdraw = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'withdraw',
    ...params
  });
}

export const getWithdrawQrcode = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'withdraw',
    a: 'qrcode',
    ...params
  });
}


export const initWithdraw = () => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'withdraw',
    a: 'init'
  });
}


export const countFee = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'withdraw',
    a: 'countfee',
    ...params
  });
}


export const referWithdraw = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'withdraw',
    a: 'submit',
    ...params
  });
}

export const saveweixin = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'edit2',
    a: 'savewx2',
    ...params
  });
}



export const savebank = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'edit2',
    a: 'savebank',
    ...params
  });
}



export const savealipay = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'edit2',
    a: 'savealipay',
    ...params
  });
}





export const submitMobilerz = (params) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'mobilerz',
    a: 'submit',
    ...params
  });
}


export const getWeixinToken = () => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'wxjsapi',
    a: 'token',
  });
}


export const cancelWithdrawAjax = (id) => {
  return axios.post('', {
    m: 'union',
    vuemod: 'vue',
    c: 'withdraw',
    a: 'cancelwxpay',
    id
  });
}




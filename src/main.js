import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import 'lib-flexible/flexible';// H5 引入 npm i lib-flexible -D 安装
import OEUI from './oeui/js/oeui';
import axios from '@/utils/axios'
import lazy from '@/utils/lazy'

import './assets/iconfont/iconfont.css'

const app = createApp(App);

app.config.globalProperties.OEUI = OEUI;
app.config.globalProperties.http = axios;
app.use(lazy);
app.use(store);
app.use(router);
app.mount('#app');

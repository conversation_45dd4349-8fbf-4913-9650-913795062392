<!-- @format -->

<template>
  <div style="padding-bottom: 1.25rem" v-if="datalist.length">
    <party_item @share="shareParty" :item="item" v-for="item in datalist" :key="item.partyid" />
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, inject} from 'vue'
  import party_item from '@/views/party/components/party_item.vue'
  const { proxy } = getCurrentInstance()

  defineProps({
    datalist: {
      type: Array,
      required: true
    }
  })

  const share = inject('shareType')

  const shareParty = id => {
    share({ id, type: 'partyShare' })
  }
</script>

<style lang="scss" scoped></style>

<!-- @format -->

<template>
  <oeui-list @refresh="refresh" @handleScroll="pageScroll" :top="0" :bottom="1.6" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
    <div class="content_box">
      <div class="data_box flex flex_v">
        <div class="flex flex_ac title">
          <span class="">服务数据</span>
          <i @click="proxy.$refs.unionIntro_dialog.open()" class="iconfont icon-tishi pr" style="font-size: 0.48rem"></i>
        </div>
        <div class="flex flex_jsb flex_1 data" style="padding: 0 0.4267rem">
          <div class="flex_dc flex_v">
            <p>{{ data.after0_nums }}</p>
            <span class="fn_i">等待帮约</span>
          </div>
          <div class="flex_dc flex_v">
            <p>{{ data.after2_nums }}</p>
            <span class="fn_i">帮约成功</span>
          </div>
          <div class="flex_dc flex_v">
            <p>{{ data.after3_nums }}</p>
            <span class="fn_i">帮约失败</span>
          </div>
        </div>
        <div class="flex flex_jsb flex_1 data" style="padding: 0 0.4267rem">
          <div class="flex_dc flex_v">
            <p>{{ data.audituser_nums }}</p>
            <span class="fn_i">资料审核</span>
          </div>
          <div class="flex_dc flex_v">
            <p>{{ data.audituser1_nums }}</p>
            <span class="fn_i">资料已审</span>
          </div>
          <div class="flex_dc flex_v">
            <p>{{ data.audituser2_nums }}</p>
            <span class="fn_i">审核已过</span>
          </div>
        </div>
      </div>
      <div class="tips_box oh flex flex_ac" v-if="msglist.length">
        <i class="iconfont icon-tuiguangguangbo-01 flex_s pr" style="top: 0.0267rem"></i>
        <div class="flex_1" style="margin-left: 0.2667rem">
          <oeSlide :data="msglist" :duration="6000" :height="1.0667" />
        </div>
      </div>
      <div class="nav_box flex flex_ac flex_jsb">
        <div class="flex_1 flex_dc flex_v" @click="$router.push('/myaudit')">
          <span class="">
            <img src="@/assets/images/useraudit.png" alt="" />
          </span>
          <p>资料审核</p>
        </div>
        <div class="flex_1 flex_dc flex_v" @click="$router.push('/myafter')">
          <span class="">
            <img src="@/assets/images/after.png" alt="" />
          </span>
          <p>帮约申请</p>
        </div>
        <div class="flex_1 flex_dc flex_v" @click="$router.push('/afteruser')">
          <span class="">
            <img src="@/assets/images/aftertc.png" alt="" />
          </span>
          <p>主动推荐</p>
        </div>
        <!-- <div>
          <span class="">
            <img src="@/assets/images/viptc.png" alt="" />
          </span>
          <p>VIP套餐</p>
        </div> -->
        <!-- <div class="flex_1 flex_dc flex_v" @click="$router.push('/level3')">
          <span class="">
            <img src="@/assets/images/grade.png" alt="" />
          </span>
          <p>分成级别</p>
        </div> -->
      </div>
      <div class="column_box">
        <div class="nav flex">
          <span :class="view == 'audit' ? 'current' : ''" @click="changType('audit')">待审核资料</span>
          <span :class="view == 'after' ? 'current' : ''" @click="changType('after')">等待帮约</span>
          <span :class="view == 'again' ? 'current' : ''" @click="changType('again')">再次推荐</span>
        </div>
        <div style="margin-top: 0.2133rem">
          <home_audit :datalist="list" v-if="view == 'audit'" />
          <home_after :datalist="list" v-if="view == 'after'" />
          <home_again :datalist="list" v-if="view == 'again'" />
        </div>
      </div>
    </div>
  </oeui-list>

  <oe_popup ref="unionIntro_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">服务数据</p>
      <p class="tip">{{ config.union3_userintro || '您审核的用户资料以及完成帮约工作后各状态下的数据记录，完成服务即可获得相应的收益。' }}</p>
      <div @click="proxy.$refs.unionIntro_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <!--审核-->
  <oe_popup ref="audit_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="audit_dialog">
      <p class="title">资料审核</p>
      <p class="tip">请如实审核用户资料，通过用户填写的 信息是否真实，完善度是否达到要求。 该操作不可逆，请谨慎操作。</p>
      <div class="btn flex_dc">
        <span @click="failAuditEvent" class="fail">不通过</span>
        <span @click="sendAuditPass">通过</span>
      </div>
      <span @click="proxy.$refs.audit_dialog.close()" class="close iconfont icon-guanbi2"></span>
    </div>
  </oe_popup>

  <oe_popup ref="auditResult_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">不奖励原因</p>
      <p class="tip">{{ fail_result }}</p>
      <div @click="proxy.$refs.auditResult_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <oe_popup ref="auditFail_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="auditFail_dialog">
      <p class="title">审核不通过原因</p>
      <div class="content">
        <textarea v-model="fail_remark" maxlength="200" placeholder="请输入审核不通过的原因"></textarea>
        <span class="nums">{{ fail_remark.length }}/200</span>
      </div>
      <div class="btn flex_dc">
        <span @click="sendAuditFail(false)" class="fail">不填</span>
        <span @click="sendAuditFail(true)">确定</span>
      </div>
      <span
        @click="
          proxy.$refs.auditFail_dialog.close(() => {
            fail_remark = ''
          })
        "
        class="close iconfont icon-guanbi2"
      ></span>
    </div>
  </oe_popup>

  <!--帮约-->
  <!--开始帮约-->
  <oe_popup ref="after_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="audit_dialog">
      <p class="title">开始帮约</p>
      <p class="tip">请如实匹配双方情况，积极帮约用户完成帮约牵线目标。</p>
      <div class="btn flex_dc">
        <span @click="proxy.$refs.after_dialog.close()" class="fail" style="background: #f2f4f5; color: #666">稍后再说</span>
        <span @click="sendAfterStart">开始帮约</span>
      </div>
    </div>
  </oe_popup>

  <!--帮约失败-->
  <oe_popup ref="afterFail_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">不奖励原因</p>
      <p class="tip">{{ fail_result }}</p>
      <div @click="proxy.$refs.afterFail_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <!-- 修改帮约结果弹窗样式 -->
  <!--帮约结果-->
  <oe_popup ref="afterResult_dialog" width="100%">
    <div class="afterResult_dialog">
      <div class="nav flex_dc">
        <span @click="proxy.$refs.afterResult_dialog.close()" class="back flex_dc">
          <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
        </span>
        <div class="title flex_dc">帮约结果</div>
      </div>
      <div class="h44"></div>
      <div class="content">
        <p class="tip">请如实选择帮约结果且可上传佐证图片，平台会对帮约用户不定时进行抽检，如发现不实牵线，平台有权扣除相应奖励，并收回你的服务权限。</p>
        <div class="type">
          <p>选择帮约结果</p>
          <div class="radio">
            <span @click="changAfterType(2)">
              <i class="iconfont" :class="after_type == 2 ? 'icon-redio_checked' : 'icon-rediobtn_nor'"></i>
              帮约成功
            </span>
            <span @click="changAfterType(3)">
              <i class="iconfont" :class="after_type == 3 ? 'icon-redio_checked' : 'icon-rediobtn_nor'"></i>
              帮约失败
            </span>
          </div>
        </div>
        <div class="proof">
          <p>上传佐证（{{ after_list.length }}/9）</p>
          <div class="item flex">
            <span class="flex_s img" @click="deleteImg(item)" v-for="item in after_list" :key="item">
              <img v-lazy="item" alt="" />
              <i class="iconfont icon-guanbi1"></i>
            </span>
            <span @click="proxy.$refs.uploadBox.open(true)" v-show="after_list.length < 9" class="flex_s flex_dc iconfont icon-fabu-01"></span>
          </div>
        </div>
        <div class="remark" v-show="after_type == 3">
          <p>失败原因</p>
          <div class="box">
            <textarea v-model="fail_remark" maxlength="200" placeholder="请输入帮约失败的原因"></textarea>
            <span class="nums">{{ fail_remark.length }}/200</span>
          </div>
        </div>
        <div class="remark" v-show="after_type == 2">
          <p>备注</p>
          <div class="box">
            <textarea v-model="fail_remark" maxlength="200" placeholder="请输入帮约备注"></textarea>
            <span class="nums">{{ fail_remark.length }}/200</span>
          </div>
        </div>
        <div style="height: 2.6667rem"></div>
      </div>
      <div class="btn flex_dc">
        <span @click="sendAfterEnd()">确定</span>
      </div>
    </div>
  </oe_popup>

  <oe_popup ref="afterAgain_dialog" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title" style="font-weight: bold">不奖励原因</p>
      <p style="text-align: center; font-size: 0.3733rem; margin-top: 0.5333rem; padding: 0 0.5333rem">帮约失败后，可根据其择偶条件筛选更多异性用户，推荐给Ta！</p>
      <p style="text-align: center; font-size: 0.3733rem; color: #999; margin-top: 0.1333rem; padding: 0 0.5333rem">用户查看后可直接再次向您发起红娘帮约服务</p>

      <div class="flex_dc" style="margin-top: 0.6667rem; font-size: 0.3733rem">
        <span @click="closeAfterAgain" class="flex_1 flex_dc" style="background: #ebebeb; color: #666; padding: 0.2133rem 0; margin-right: 0.5333rem; border-radius: 1.0667rem">稍后再说</span>
        <span @click="goPageMatch" class="flex_1 flex_dc" style="background: #7d68fe; color: #fff; padding: 0.2133rem 0; border-radius: 1.0667rem">去筛选</span>
      </div>
    </div>
  </oe_popup>

  <upload-headimg ref="uploadBox" @callback="sendImg"></upload-headimg>
</template>

<script setup>
import { ref, getCurrentInstance, provide, computed } from 'vue'
import oeuiList from '@/oeui/list.vue'
import oeSlide from '@/oeui/slide.vue'
import oe_popup from '@/oeui/popup.vue'
import home_audit from './home_audit.vue'
import home_after from './home_after.vue'
import home_again from './home_again.vue'
import uploadHeadimg from '@/components/upload_headimg.vue'
import { useStore } from 'vuex'
import { getAuditUser, auditPass, auditFail } from '@/api/audit_user.js'
import { getAfterUser, afterStart, afterEnd } from '@/api/after.js'
import { uploadImage } from '@/api/edit.js'
import { getPlaywall } from '@/api/msg.js'
import { useRouter } from 'vue-router'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const config = computed(() => store.state.config)
const router = useRouter()

defineProps({
  data: {
    type: Object,
  },
})

const msglist = ref([])
const list = ref([])
const page = ref(1)
const pageCount = ref('0')
const listStatus = ref('loading')

//获取一组播报
const getBroadcast = () => {
  getPlaywall().then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data || []
      msglist.value = res.result.data
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}
getBroadcast()

const setListType = () => {
  if (view.value == 'audit') {
    return getAuditUser({ page: page.value, s_flag: 99 })
  } else if (view.value == 'after') {
    return getAfterUser({ page: page.value, s_flag: 98 })
  } else if (view.value == 'again') {
    return getAfterUser({ page: page.value, s_again: 1 })
  }
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  setListType().then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
  store.dispatch('getTj')
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

const view = ref('audit')
const changType = val => {
  view.value = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

//审核操作

const auditid = ref(null)
const fail_remark = ref('')
const is_send = ref(true)
const fail_result = ref('')
const AuditEvent = id => {
  auditid.value = id
  proxy.$refs.audit_dialog.open()
}
provide('selectAudit', AuditEvent)
const failAuditEvent = () => {
  proxy.$refs.audit_dialog.close(() => {
    proxy.$refs.auditFail_dialog.open()
  })
}
const sendAuditPass = () => {
  if (!is_send.value) return
  is_send.value = false
  auditPass({
    id: auditid.value,
  }).then(res => {
    if (res.ret == 1) {
      list.value = list.value.filter(v => v.userid != auditid.value)
      proxy.$refs.audit_dialog.close(() => {
        OEUI.toast({
          text: '操作成功',
        })
        auditid.value = null
        if (!list.value.length) {
          page.value = 1
          getList(true)
        }
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}
const sendAuditFail = flag => {
  if (flag && !fail_remark.value.length) {
    return OEUI.toast({
      text: '请填写审核不通过的原因',
    })
  }
  if (!is_send.value) return
  is_send.value = false
  auditFail({
    id: auditid.value,
    remark: fail_remark.value,
  }).then(res => {
    if (res.ret == 1) {
      list.value = list.value.filter(v => v.userid != auditid.value)
      proxy.$refs.auditFail_dialog.close(() => {
        OEUI.toast({
          text: '操作成功',
        })
        auditid.value = null
        fail_remark.value = ''
        if (!list.value.length) {
          page.value = 1
          getList(true)
        }
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}
const showFailAudit = val => {
  fail_result.value = val
  proxy.$refs.auditResult_dialog.open()
}
provide('showFailAudit', showFailAudit)

//帮约操作

const afterid = ref(null)
const after_type = ref(2)
const after_list = ref([])
const changAfterType = val => {
  after_type.value = val
}
const sendImg = obj => {
  if (!obj.src) return OEUI.toast({ text: '图片获取失败，请检查!' })
  try {
    uploadImage({
      base64img: obj.src,
      module: 'upload',
      thumb: 1,
    }).then(data => {
      if (data.ret == 1) {
        after_list.value.push(data.result.drawimg)
      } else {
        OEUI.toast({ text: '图片上传失败，请检查!' })
      }
    })
  } catch (error) {
    OEUI.toast({ text: '系统繁忙，请检查!' })
  }
}
const deleteImg = val => {
  after_list.value = after_list.value.filter(item => item != val)
}
const ininAfterList = () => {
  if (!after_list.value.length) return
  let data = {}
  after_list.value.forEach((v, i) => {
    data['link_img' + (i + 1)] = v
  })
  return data
}

const afterStartEvent = id => {
  afterid.value = id
  after_list.value = []
  proxy.$refs.after_dialog.open()
}
provide('afterStart', afterStartEvent)
const AfterEvent = id => {
  afterid.value = id
  proxy.$refs.afterResult_dialog.open()
}

provide('selectAfter', AfterEvent)

const sendAfterStart = () => {
  if (!is_send.value) return
  is_send.value = false
  afterStart({
    id: afterid.value,
  }).then(res => {
    if (res.ret == 1) {
      list.value.forEach(v => {
        if (v.afterid == afterid.value) {
          v.flag = 1
        }
      })
      proxy.$refs.after_dialog.close(() => {
        OEUI.toast({
          text: '操作成功',
        })
        afterid.value = null
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}
const sendAfterEnd = () => {
  if (!is_send.value) return
  is_send.value = false
  afterEnd({
    id: afterid.value,
    flag: after_type.value,
    remark: fail_remark.value,
    ...ininAfterList(),
  }).then(res => {
    if (res.ret == 1) {
      matchid.value = list.value.find(v => v.afterid == afterid.value).user1.userid
      list.value = list.value.filter(v => v.afterid != afterid.value)
      proxy.$refs.afterResult_dialog.close(() => {
        // if (after_type.value == 3) {
        //   proxy.$refs.afterAgain_dialog.open()
        //   return
        // }
        OEUI.toast({
          text: '操作成功',
        })
        after_type.value = 2
        afterid.value = null
        fail_remark.value = ''
        after_list.value = []
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}

// 帮约失败 start
const matchid = ref(null)
const closeAfterAgain = () => {
  proxy.$refs.afterAgain_dialog.close()
  matchid.value = null
}
const goPageMatch = () => {
  if (!matchid.value) return
  router.push('/match/result?id=' + matchid.value)
  closeAfterAgain()
}
// 帮约失败 end

const showFailAfter = val => {
  fail_result.value = val
  proxy.$refs.afterFail_dialog.open()
}
provide('showFailAfter', showFailAfter)

// 主动帮约

getList()
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}

.font_din {
  font-family: 'cpDin';
}

.content_box {
  // margin-top: 0.2667rem;
  padding: 0 0.4267rem;

  &::-webkit-scrollbar {
    display: none;
  }

  .data_box {
    border: 0.0267rem solid #fff;
    background: rgba(255, 255, 255, 0.32);
    border-radius: 0.32rem;
    padding: 0.32rem;
    box-sizing: border-box;

    .title span {
      font-size: 0.4267rem;
      color: #31293b;
      line-height: 0.5867rem;
      font-weight: 500;
      margin-right: 0.1333rem;
    }

    .title i {
      position: relative;
      font-size: 0.4267rem;
      color: #31293b;
      line-height: 0.5867rem;
      font-weight: 500;
    }

    .data {
      margin-top: 0.2667rem;

      div {
        p {
          font-size: 0.8533rem;
          font-weight: 700;
          color: #31293b;
          line-height: 1.0133rem;
          @extend .font_din;
        }

        span {
          font-size: 0.32rem;
          line-height: 0.4533rem;
          color: #999;
          margin-top: 0.2667rem;
        }
      }
    }
  }

  .tips_box {
    margin-top: 0.3733rem;
    background: #fff;
    height: 1.0667rem;
    border-radius: 0.32rem;
    padding: 0 0.32rem;
  }

  .nav_box {
    margin-top: 0.3733rem;

    div {
      span {
        display: block;
        width: 1.5467rem;
        height: 1.5467rem;
        background: #fff;
        border-radius: 0.64rem;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      p {
        font-size: 0.32rem;
        line-height: 0.4533rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        margin-top: 0.1067rem;
        color: #666;
        text-align: center;
      }
    }
  }

  .column_box {
    margin-top: 0.32rem;

    .nav {
      padding: 0.2667rem 0;

      &.sticky {
        position: fixed;
        top: 0;
        z-index: 100;
        width: 100vw;
        margin-left: -0.4267rem;
        padding-left: 0.4267rem;
        background: url('~@/assets/images/bg_main.png') no-repeat;
        background-size: cover;
        box-sizing: border-box;
        overflow: hidden;
      }

      span {
        margin-right: 0.64rem;
        font-size: 0.3733rem;
        line-height: 0.6133rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        position: relative;
        top: 0.1067rem;

        &.current {
          font-size: 0.48rem;
          line-height: 0.6667rem;
          font-weight: 500;
          color: #31293b;
          top: 0;
        }
      }
    }
  }
}

.audit_dialog {
  padding: 0.64rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;

  .title {
    font-size: 0.4267rem;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    text-align: center;
  }

  .tip {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.5867rem;
  }

  .btn {
    margin-top: 0.5333rem;

    span {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.4267rem;
      font-weight: normal;
      line-height: 0.5867rem;
      padding: 0.24rem 0;
      cursor: pointer;
      background: $color_main;
      border-radius: 0.5333rem;
      color: #fff;
    }

    .fail {
      margin-right: 0.5867rem;
      color: $color_main;
      background: rgba(125, 104, 254, 0.24);
    }
  }

  .close {
    position: absolute;
    right: 0.4rem;
    top: 0.2667rem;
    font-size: 0.48rem;
  }
}

.auditFail_dialog {
  padding: 0.4267rem 0.64rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;

  .title {
    font-size: 0.4267rem;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    text-align: center;
  }

  .content {
    margin-top: 0.32rem;
    background: #f2f4f5;
    height: 2.8rem;
    border-radius: 0.32rem;
    padding: 0.32rem;
    box-sizing: border-box;
    position: relative;

    textarea {
      resize: none;
      border: none;
      width: 100%;
      height: 100%;
      background: none;

      &::placeholder {
        font-size: 0.3733rem;
        font-weight: normal;
        color: #c5c3c7;
        line-height: 0.5333rem;
      }
    }

    .nums {
      position: absolute;
      font-size: 0.32rem;
      font-weight: normal;
      color: #c5c3c7;
      line-height: 0.4533rem;
      right: 0.4rem;
      bottom: 0.0533rem;
    }
  }

  .btn {
    margin-top: 0.4rem;
    padding: 0 0.2133rem;

    span {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.4267rem;
      font-weight: normal;
      line-height: 0.5867rem;
      padding: 0.24rem 0;
      cursor: pointer;
      background: $color_main;
      border-radius: 0.5333rem;
      color: #fff;
    }

    .fail {
      margin-right: 0.5867rem;
      color: $color_main;
      background: rgba(125, 104, 254, 0.24);
    }
  }

  .close {
    position: absolute;
    right: 0.4rem;
    top: 0.2667rem;
    font-size: 0.48rem;
  }
}

.afterResult_dialog {
  font-family: PingFang SC, PingFang SC;

  .nav {
    height: 1.1733rem;
    position: fixed;
    top: 0;
    width: 100vw;
    z-index: 100;
    font-family: PingFang SC-Regular, PingFang SC;

    span {
      width: 1.1733rem;
      height: 1.1733rem;

      i {
        font-size: 0.64rem;
        position: relative;
        top: 0.0267rem;
      }
    }

    .back {
      position: absolute;
      left: 0;
    }

    .title {
      font-size: 0.48rem;
      line-height: 0.64rem;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #2a2546;
    }
  }

  .content {
    padding: 0 0.64rem;
    height: calc(100vh - 1.1733rem);
    overflow-y: scroll;
    margin-bottom: 100px;

    .tip {
      margin-top: 0.4267rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5867rem;
    }

    .type {
      margin-top: 0.4267rem;

      p {
        font-size: 0.3733rem;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 0.5333rem;
      }

      .radio {
        margin-top: 0.2133rem;

        span {
          margin-right: 0.8533rem;
          font-size: 0.3733rem;
          font-weight: normal;
          color: #666666;
          line-height: 0.5333rem;

          i {
            position: relative;
            top: 0.0533rem;
            color: #999999;
            font-size: 0.48rem;
          }

          .icon-redio_checked {
            color: $color_main;
          }
        }
      }
    }

    .proof {
      margin-top: 0.4267rem;

      p {
        font-size: 0.3733rem;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 0.5333rem;
      }

      .item {
        margin-top: 0.2133rem;
        flex-wrap: wrap;

        span {
          width: 1.8667rem;
          height: 1.8667rem;
          background: #f2f4f5;
          margin-right: 0.5067rem;
          margin-bottom: 0.2667rem;
          border-radius: 0.3733rem;
          overflow: hidden;
          font-size: 0.96rem;
          color: #999;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .icon-guanbi1 {
            position: absolute;
            right: 0.0267rem;
            top: 0.0267rem;
            color: #ed1616;
            z-index: 100;
          }
        }

        & > span:nth-child(3n) {
          margin-right: 0;
        }
      }
    }

    .remark {
      margin-top: 0.4267rem;

      p {
        font-size: 0.3733rem;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 0.5333rem;
      }

      .box {
        margin-top: 0.2133rem;
        background: #f2f4f5;
        height: 2.8rem;
        border-radius: 0.32rem;
        padding: 0.32rem;
        box-sizing: border-box;
        position: relative;

        textarea {
          resize: none;
          border: none;
          width: 100%;
          height: 100%;
          background: none;

          &::placeholder {
            font-size: 0.3733rem;
            font-weight: normal;
            color: #c5c3c7;
            line-height: 0.5333rem;
          }
        }

        .nums {
          position: absolute;
          font-size: 0.32rem;
          font-weight: normal;
          color: #c5c3c7;
          line-height: 0.4533rem;
          right: 0.4rem;
          bottom: 0.0533rem;
        }
      }
    }
  }

  .btn {
    position: fixed;
    height: 2.6667rem;
    width: 100vw;
    bottom: 0;
    left: 0;
    margin-top: 0.4rem;

    span {
      width: 80%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.4267rem;
      font-weight: normal;
      line-height: 0.5867rem;
      padding: 0.24rem 0;
      cursor: pointer;
      background: $color_main;
      border-radius: 0.5333rem;
      color: #fff;
    }
  }
}
</style>

<!-- @format -->

<template>
  <div style="padding-bottom: 1.25rem" v-if="datalist.length">
    <user_item :item="item" v-for="item in datalist" :key="item.userid" />
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import user_item from '@/views/user/components/user_item.vue'
const { proxy } = getCurrentInstance()

defineProps({
  datalist: {
    type: Array,
    required: true,
  },
})
</script>

<style lang="scss" scoped></style>

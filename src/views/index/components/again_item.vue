<!-- @format -->

<template>
  <div class="item_box" @click="$router.push('/afteruser/detail?id=' + item.user1.userid)" v-if="item.user1.userid">
    <div class="data">
      <div class="head">
        <img v-if="item.user1.headimg" v-lazy="item.user1.headimg_m1_url" type="user" alt="" />
        <template v-else>
          <img v-if="item.user1.gender == 1" src="@/assets/images/gender_1.png" alt="" />
          <img v-else-if="item.user1.gender == 2" src="@/assets/images/gender_2.png" alt="" />
          <img v-else src="@/assets/images/gender_0.png" alt="" />
        </template>
      </div>
      <div class="info">
        <p class="job">
          {{ item.user1.age }}岁
          <template v-if="item.user1.job > 0">/{{ item.user1.job_t }}</template>
        </p>
        <p class="name">
          昵称: &nbsp;
          <em>{{ item.user1.username }}</em>
          &emsp; (编号:&nbsp;{{ item.user1.userid }})
        </p>
        <p class="base" v-if="item.user1.marry > 0 || item.user1.education > 0 || item.user1.height > 0">
          <template v-if="item.user1.marry > 0">{{ item.user1.marry_t }}</template>
          <template v-if="item.user1.marry > 0">·</template>
          <template v-if="item.user1.education > 0">{{ item.user1.education_t }}</template>
          <template v-if="item.user1.education > 0">·</template>
          <template v-if="item.user1.height > 0">{{ item.user1.height }}cm</template>
        </p>
        <div class="dist">
          <template>
            <span v-if="item.user1.dist1 > 0 && item.user1.list_dist_t">现居：{{ item.user1.list_dist_t }}</span>
          </template>
          <span style="text-align: right" v-if="item.user1.salary > 0">年收入：{{ item.user1.salary_t }}</span>
        </div>
      </div>
    </div>
    <div class="share" @click.stop="$router.push('/match/result?id=' + item.user1.userid)">
      <div class="show">
        <span>
          <img src="@/assets/images/after_match.png" alt="" />
        </span>
        <p>再次推荐</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const config = computed(() => store.state.config)
const emits = defineEmits(['audit', 'show'])
defineProps({
  item: {
    type: Object,
  },
})
</script>

<style lang="scss" scoped>
.item_box {
  cursor: pointer;
  background: #fff;
  border-radius: 0.32rem;
  box-sizing: border-box;
  margin-bottom: 0.32rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;
  overflow: hidden;

  .data {
    padding: 0.32rem;
    box-sizing: border-box;
    display: flex;
    align-content: center;
    border-bottom: 0.0267rem solid #f2f4f5;

    .head {
      width: 2.0533rem;
      height: 2.0533rem;
      border-radius: 0.2133rem;
      overflow: hidden;
      margin-right: 0.2133rem;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .job {
        font-size: 0.48rem;
        line-height: 0.6667rem;
        color: #3d3d3d;
        font-weight: 500;
      }

      .name {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
        display: flex;
        align-items: center;

        em {
          max-width: 2.6667rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }

      .base {
        margin-top: 0.2133rem;
        font-size: 0.3467rem;
        line-height: 0.48rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }

      .dist {
        width: 100%;
        margin-top: 0.2133rem;
        font-size: 0.3467rem;
        line-height: 0.48rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }

  .share {
    padding: 0.32rem;
    box-sizing: border-box;
    display: flex;
    align-content: center;
    justify-content: center;

    .show {
      color: #7d68fe;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      display: flex;
      align-items: center;
      margin-right: 0.64rem;
      flex-shrink: 0;

      span {
        width: 0.64rem;
        height: 0.64rem;
        margin-right: 0.1067rem;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      p {
        position: relative;
        top: 0.0267rem;
        font-weight: normal;
      }
    }
  }

  .tips {
    position: absolute;
    background: $color_main;
    color: #fff;
    right: -0.6133rem;
    top: -0.1067rem;
    padding: 0.4rem 0.5333rem 0.0533rem 0.5333rem;
    font-size: 0.2667rem;
    font-weight: normal;
    line-height: 0.3733rem;
    transform: rotate(45deg);

    &.success {
      background: #15ce7b;
    }

    &.audit {
      background: #ccc;
    }

    &.fail {
      background: #f40;
    }
  }
}
</style>

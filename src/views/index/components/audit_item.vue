<!-- @format -->

<template>
  <div class="item_box" @click="$router.push('/audituser/detail?id=' + item.userid)" v-if="item.userid">
    <div class="data">
      <div class="head">
        <img v-if="item.headimg" v-lazy="item.headimg_m1_url" type="user" alt="" />
        <template v-else>
          <img v-if="item.gender == 1" src="@/assets/images/gender_1.png" alt="" />
          <img v-else-if="item.gender == 2" src="@/assets/images/gender_2.png" alt="" />
          <img v-else src="@/assets/images/gender_0.png" alt="" />
        </template>
      </div>
      <div class="info">
        <p class="job">
          {{ item.age }}岁
          <template v-if="item.job > 0">/{{ item.job_t }}</template>
        </p>
        <p class="name">
          昵称: &nbsp;
          <em>{{ item.username }}</em>
          &emsp; (编号:&nbsp;{{ item.userid }})
        </p>
        <p class="base" v-if="item.marry > 0 || item.education > 0 || item.height > 0">
          <template v-if="item.marry > 0">{{ item.marry_t }}</template>
          <template v-if="item.marry > 0">·</template>
          <template v-if="item.education > 0">{{ item.education_t }}</template>
          <template v-if="item.education > 0">·</template>
          <template v-if="item.height > 0">{{ item.height }}cm</template>
        </p>
        <div class="dist">
          <span v-if="item.dist1 > 0 && item.list_dist_t">现居：{{ item.list_dist_t }}</span>
          <span style="text-align: right" v-if="item.salary > 0">年收入：{{ item.salary_t }}</span>
        </div>
      </div>
    </div>
    <div class="share">
      <div class="show">
        <span>
          <img src="@/assets/images/share_info.png" alt="" />
        </span>
        <p>查看用户信息</p>
      </div>
      <div class="audit">
        <p @click.stop="showRwremark(item.audit_rwremark)" class="fail" v-if="item.audit_rwflag == 2 && item.audit_rwremark">
          不奖励原因
          <i class="iconfont icon-tishi"></i>
        </p>
        <!-- <p class="pr" style="top: 0.0267rem" v-else-if="item.audit_rwflag == 1">
          <em>佣金：+¥{{ item.audit_rw }}</em>
        </p>
        <p class="pr" style="top: 0.0267rem" v-else-if="item.audit_rwflag == 0">佣金：¥{{ item.audit_rw }}</p> -->
        <p v-else></p>

        <span @click.stop="btn_audit(item.userid)" class="btn" v-if="item.flag == 0">审核</span>
        <span class="color_red" v-else-if="item.flag == 2">未通过</span>
        <span class="color_green" v-else>已通过</span>
      </div>
    </div>
    <div class="tips audit" v-if="item.audit_rwflag == 0">待奖励</div>
    <div class="tips success" v-else-if="item.audit_rwflag == 1">已奖励</div>
    <div class="tips fail" v-else-if="item.audit_rwflag == 2">不奖励</div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const config = computed(() => store.state.config)

const emits = defineEmits(['audit', 'show'])
defineProps({
  item: {
    type: Object,
  },
})

const btn_audit = id => {
  emits('audit', id)
}
const showRwremark = val => {
  emits('show', val)
}
</script>

<style lang="scss" scoped>
.item_box {
  cursor: pointer;
  background: #fff;
  border-radius: 0.32rem;
  box-sizing: border-box;
  margin-bottom: 0.32rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;
  overflow: hidden;
  .data {
    padding: 0.32rem;
    box-sizing: border-box;
    display: flex;
    align-content: center;
    border-bottom: 0.0267rem solid #f2f4f5;
    .head {
      width: 2.0533rem;
      height: 2.0533rem;
      border-radius: 0.2133rem;
      overflow: hidden;
      margin-right: 0.2133rem;
      flex-shrink: 0;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .job {
        font-size: 0.48rem;
        line-height: 0.6667rem;
        color: #3d3d3d;
        font-weight: 500;
      }
      .name {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
        display: flex;
        align-items: center;
        em {
          max-width: 2.6667rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .base {
        margin-top: 0.2133rem;
        font-size: 0.3467rem;
        line-height: 0.48rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }
      .dist {
        width: 100%;
        margin-top: 0.2133rem;
        font-size: 0.3467rem;
        line-height: 0.48rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        display: flex;
        align-items: center;
        justify-content: space-between;
        span {
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }
  .share {
    padding: 0.32rem;
    box-sizing: border-box;
    display: flex;
    align-content: center;
    .show {
      color: #7d68fe;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      display: flex;
      align-items: center;
      margin-right: 0.64rem;
      flex-shrink: 0;
      span {
        width: 0.64rem;
        height: 0.64rem;
        margin-right: 0.1067rem;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      p {
        position: relative;
        top: 0.0267rem;
        font-weight: normal;
      }
    }
    .audit {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      p {
        position: relative;
        top: 0.0267rem;
        font-size: 0.32rem;
        font-weight: normal;
        color: #666666;
        line-height: 0.4533rem;
        i {
          position: relative;
          top: 0.04rem;
        }
        em {
          font-weight: 500;
          color: $color_main;
        }
        &.fail {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
        }
      }
      .btn {
        background: $color_main;
        font-size: 0.3467rem;
        font-weight: normal;
        color: #ffffff;
        line-height: 0.48rem;
        padding: 0.1867rem 0.4267rem;
        border-radius: 0.4267rem;
        cursor: pointer;
      }
    }
  }
  .tips {
    position: absolute;
    background: $color_main;
    color: #fff;
    right: -0.6133rem;
    top: -0.1067rem;
    padding: 0.4rem 0.5333rem 0.0533rem 0.5333rem;
    font-size: 0.2667rem;
    font-weight: normal;
    line-height: 0.3733rem;
    transform: rotate(45deg);
    &.success {
      background: #15ce7b;
    }
    &.audit {
      background: #ccc;
    }
    &.fail {
      background: #f40;
    }
  }
}
</style>

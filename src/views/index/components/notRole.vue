<!-- @format -->

<template>
  <!--没身份-->
  <oe_popup ref="notRole_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <div class="tips_img">
        <img src="@/assets/images/chat_tips.png" alt="" />
      </div>
      <p class="title">选择角色</p>
      <p class="tip">尊敬的推广用户，请选择您要申请的角色，申请后可进入工作台开始您的工作。</p>
      <div @click="$router.replace('/active?flag=true')" class="event">去申请</div>
    </div>
  </oe_popup>
  <!--身份审核中-->
  <oe_popup ref="auditRole_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <div class="tips_img">
        <img src="@/assets/images/chat_tips.png" alt="" />
      </div>
      <p class="title">等待审核</p>
      <p class="tip">尊敬的推广用户,系统已收到您的申请信息,我们将尽快安排专人审核,请耐心等待审核结果!</p>
    </div>
  </oe_popup>
</template>

<script setup>
  import { getCurrentInstance } from 'vue'
  import oe_popup from '@/oeui/popup.vue'
  const { proxy } = getCurrentInstance()

  const open = val => {
    if (val == 0) {
      proxy.$refs.notRole_dialog.open()
    } else if (val == 2) {
      proxy.$refs.auditRole_dialog.open()
    }
  }

  defineExpose({
    open
  })
</script>

<style lang="scss" scoped>
  .intro_dialog {
    text-align: center;
    padding: 0.64rem 0.48rem;
    background: url('../../../assets/images/dailog_bg.png');
    background-size: cover;
    border-radius: 0.64rem;
    overflow: hidden;

    .tips_img {
      position: absolute;
      left: 50%;
      top: -1.7333rem;
      transform: translateX(-50%);
      width: 4.2667rem;
      height: 2.6133rem;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .title {
      margin-top: 0.4267rem;
      font-weight: 500;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      color: #3d3d3d;
      line-height: 0.5867rem;
    }
  }
</style>

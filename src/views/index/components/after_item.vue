<!-- @format -->

<template>
  <div class="item_box" v-if="item.afterid">
    <div class="time flex flex_ac">
      <i class="iconfont icon-shizhong"></i>
      <span>帮约时间：{{ getTime(item.addtime) }}</span>
    </div>
    <div class="data flex">
      <div class="flex_1 flex_s flex flex_ac flex_v head ws">
        <div class="photo" @click="$router.push('/user/detail?id=' + item.user1.userid)">
          <img v-if="item.user1.headimg" v-lazy="item.user1.headimg_m1_url" type="user" alt="" />
          <template v-else>
            <img v-if="item.user1.gender == 1" src="@/assets/images/gender_1.png" alt="" />
            <img v-else-if="item.user1.gender == 2" src="@/assets/images/gender_2.png" alt="" />
            <img v-else src="@/assets/images/gender_0.png" alt="" />
          </template>
        </div>
        <div class="flex_dc name">
          <span class="ws">{{ item.user1.username }}</span>
          <i class="iconfont icon-nanbiao-01" v-if="item.user1.gender == 1"></i>
          <i class="iconfont icon-nvbiao-01" v-else-if="item.user1.gender == 2"></i>
        </div>
        <p class="contact ws" v-if="item.user1.mobile">
          <span @click="callMobile(item.user1.mobile)" class="btn flex_dc" style="background: #7e6dfb; color: #fff">
            <i class="iconfont icon-dianhua"></i>
            拨打电话
          </span>
          <!--<i class="iconfont icon-dianhua3"></i>
          {{ filterMobile(item.user1.mobile) || '未填写' }}-->
        </p>
        <p class="contact ws" v-if="item.user1.weixin">
          <span @click="copy(iem.user1.weixin)" class="btn flex_dc" style="color: #09bb07; border: 1px solid #09bb07">
            <i class="iconfont icon-a-chakanweixinbai-01" style="font-size: 0.3733rem"></i>
            复制微信
          </span>
          <!--<i class="iconfont icon-weixin1"></i>
          {{ item.user1.weixin || '未填写' }}-->
        </p>
      </div>
      <div class="flex_1 flex_s flex_dc flex_v tip">
        <img src="@/assets/images/after_item.png" alt="" />
        <span class="contact">帮约</span>
        <i class="iconfont icon-youshuangjiantou" style="left: -0.08rem; color: #f897b6"></i>
        <i class="iconfont icon-youshuangjiantou"></i>
      </div>
      <div class="flex_1 flex_s flex flex_ac flex_v head ws">
        <div class="photo" @click="$router.push('/user/detail?id=' + +item.user2.userid)">
          <img v-if="item.user2.headimg" v-lazy="item.user2.headimg_m1_url" type="user" alt="" />
          <template v-else>
            <img v-if="item.user2.gender == 1" src="@/assets/images/gender_1.png" alt="" />
            <img v-else-if="item.user2.gender == 2" src="@/assets/images/gender_2.png" alt="" />
            <img v-else src="@/assets/images/gender_0.png" alt="" />
          </template>
        </div>
        <div class="flex_dc name">
          <span class="ws">{{ item.user2.username }}</span>
          <i class="iconfont icon-nanbiao-01" v-if="item.user2.gender == 1"></i>
          <i class="iconfont icon-nvbiao-01" v-else-if="item.user2.gender == 2"></i>
        </div>

        <!-- 属于自己的用户，显示用户电话号、微信 -->
        <template v-if="unionInfo.unionid === item.user2.be_afterunid">
          <p class="contact ws" v-if="item.user2.mobile">
            <span @click="callMobile(item.user2.mobile)" class="btn flex_dc" style="background: #7e6dfb; color: #fff">
              <i class="iconfont icon-dianhua"></i>
              拨打电话
            </span>
          </p>
          <p class="contact ws" v-if="item.user2.weixin">
            <span @click="copy(item.user2.weixin)" class="btn flex_dc" style="color: #09bb07; border: 1px solid #09bb07">
              <i class="iconfont icon-a-chakanweixinbai-01" style="font-size: 0.3733rem"></i>
              复制微信
            </span>
          </p>
        </template>
        <template v-else>
          <p class="contact ws" v-if="item.user2.union_mobile">
            <span @click="callMobile(item.user2.union_mobile)" class="btn flex_dc" style="background: #7e6dfb; color: #fff">
              <i class="iconfont icon-dianhua"></i>
              红娘电话
            </span>
          </p>
          <p class="contact ws" v-if="item.user2.union_weixin">
            <span @click="copy(item.user2.union_weixin)" class="btn flex_dc" style="color: #09bb07; border: 1px solid #09bb07">
              <i class="iconfont icon-a-chakanweixinbai-01" style="font-size: 0.3733rem"></i>
              红娘微信
            </span>
          </p>
        </template>
      </div>
    </div>
    <div class="share flex flex_ac flex_jsb">
      <span @click="showRwremark(item.settle_rwremark)" v-if="item.settle_rwflag == 2 && item.settle_rwremark">
        不奖励原因
        <i class="iconfont icon-tishi"></i>
      </span>
      <!-- <span style="color: #7d68fe" v-else-if="item.settle_rw > 0 && item.settle_rwflag == 1">佣金：+¥{{ item.settle_rw }}</span>
      <span v-else-if="item.settle_rw > 0">佣金：¥{{ item.settle_rw }}</span> -->
      <span v-else></span>
      <span class="btn flex_dc" @click="btn_after_start(item.afterid)" v-if="item.flag == 0">
        待帮约
        <i class="iconfont icon-youjiantou-01"></i>
      </span>
      <span class="btn flex_dc ing" @click="btn_after(item.afterid)" v-else-if="item.flag == 1">
        帮约中...
        <i class="iconfont icon-youjiantou-01"></i>
      </span>
      <span class="color_green" v-else-if="item.flag == 2">帮约成功</span>
      <span class="color_red" v-else-if="item.flag == 3">帮约失败</span>
      <span v-else></span>
    </div>
  </div>
</template>

<script setup>
import { getCurrentInstance, computed } from 'vue'
import { getTime, filterMobile } from '@/utils/hooks.js'
import { useStore } from 'vuex'
const { proxy } = getCurrentInstance()
const store = useStore()
const OEUI = proxy.OEUI
defineProps({
  item: {
    type: Object,
  },
})
const emits = defineEmits(['start', 'after', 'show'])

const unionInfo = computed(() => store.state.unionInfo)

const btn_after_start = id => {
  emits('start', id)
}

const btn_after = id => {
  emits('after', id)
}
const showRwremark = val => {
  emits('show', val)
}

const copy = data => {
  if (!data) return
  let elInput = document.createElement('input')
  elInput.value = data
  document.body.appendChild(elInput)
  elInput.select()
  document.execCommand('Copy')
  OEUI.toast({
    text: '复制成功',
  })
  elInput.remove()
}
const callMobile = data => {
  if (!data) return
  const phoneNumber = data
  const link = document.createElement('a')
  link.href = `tel:${phoneNumber}`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
</script>

<style lang="scss" scoped>
.item_box {
  cursor: pointer;
  background: #fff;
  border-radius: 0.32rem;
  box-sizing: border-box;
  margin-bottom: 0.32rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;
  overflow: hidden;
  padding: 0 0.4267rem;
  .time {
    margin-top: 0.32rem;
    font-size: 0.32rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #999999;
    line-height: 0.4533rem;
    i {
      margin-right: 0.1333rem;
    }
  }
  .data {
    margin-top: 0.2667rem;
    padding: 0.32rem 0;
    border-bottom: 0.0267rem solid #f2f4f5;
    .head {
      .photo {
        width: 1.76rem;
        height: 1.76rem;
        border-radius: 50%;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .name {
        margin-top: 0.2133rem;
        margin-bottom: 0.2133rem;
        span {
          font-size: 0.48rem;
          font-weight: 500;
          color: #3d3d3d;
          line-height: 0.6667rem;
          max-width: 1.76rem;
        }
        i {
          position: relative;
          top: 0.0267rem;
          margin-left: 0.1333rem;
        }
        .icon-nanbiao-01 {
          color: #0570f1;
        }
        .icon-nvbiao-01 {
          color: #fe6897;
        }
      }
      .contact {
        margin-top: 0.1333rem;
        font-size: 0.32rem;
        font-weight: normal;
        color: #666666;
        line-height: 0.4533rem;
        i {
          position: relative;
          top: 0.0267rem;
        }
        .btn {
          padding: 0 0.1333rem;
          height: 0.6133rem;
          box-sizing: border-box;
          border-radius: 0.08rem;
          i {
            font-size: 0.32rem;
            margin-right: 0.08rem;
          }
        }
      }
    }
    .tip {
      width: 2.2933rem;
      //margin: 0 0.2133rem;
      position: relative;
      top: -1.0667rem;
      img {
        width: 100%;
        object-fit: cover;
      }
      .contact {
        font-size: 0.32rem;
        font-weight: normal;
        color: #ffffff;
        line-height: 0.4533rem;
        position: absolute;
      }
      .icon-youshuangjiantou {
        position: absolute;
        color: #fe6897;
        right: -0.2133rem;
      }
    }
  }
  .share {
    height: 1.28rem;
    span {
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5333rem;
      i {
        position: relative;
        top: 0.04rem;
      }
    }
    .btn {
      background: $color_main;
      color: #fff;
      border-radius: 0.4267rem;
      padding: 0.16rem 0.4267rem;
      cursor: pointer;
      &.ing {
        background: #ffc052 !important;
      }
      i {
        position: relative;
        top: 0.04rem;
      }
    }
  }

  .tips {
    position: absolute;
    background: $color_main;
    color: #fff;
    right: -0.6133rem;
    top: -0.1067rem;
    padding: 0.4rem 0.5333rem 0.0533rem 0.5333rem;
    font-size: 0.2667rem;
    font-weight: normal;
    line-height: 0.3733rem;
    transform: rotate(45deg);
    &.success {
      background: #15ce7b;
    }
    &.audit {
      background: #ccc;
    }
    &.fail {
      background: #f40;
    }
  }
}
</style>

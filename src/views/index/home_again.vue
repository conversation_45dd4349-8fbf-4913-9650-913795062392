<!-- @format -->

<template>
  <div style="padding-bottom: 1.25rem" v-if="datalist.length">
    <div class="flex flex_ac tips">
      <i class="iconfont icon-tishi"></i>
      <p>没有帮约成功的Ta们，想让你推荐更多合适异性</p>
    </div>
    <again_item :item="item" v-for="item in datalist" :key="item.user1.userid" />
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, watch } from 'vue'
import again_item from './components/again_item.vue'
const { proxy } = getCurrentInstance()

const props = defineProps({
  datalist: {
    type: Array,
    required: true,
  },
})

const list = ref(props.datalist)
watch(
  () => props.datalist,
  newValue => {
    list.value = newValue
  },
  { deep: true, immediate: true }
)


</script>

<style lang="scss" scoped>
.tips{
  padding: .16rem .2667rem;
  font-size: .32rem;
  line-height: 1;
  border-radius: .5333rem;
  color: #B184FF;
  font-weight: normal;
  background: linear-gradient(180deg, rgba(220, 212, 254, 0.3) 0%, rgba(248, 219, 253, 0.3) 100%);
  border: .0267rem solid rgba(177, 132, 255, 0.3);
  i{
    margin-right: .1333rem;
  }
  margin-bottom: .32rem;
}
</style>

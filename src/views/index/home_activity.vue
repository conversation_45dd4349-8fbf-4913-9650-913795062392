<!-- @format -->

<template>
  <div style="padding-bottom: 1.25rem" v-if="datalist.length">
    <activity_item @share="shareActivity" :item="item" v-for="item in datalist" :key="item.actid" />
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, inject } from 'vue'
  import activity_item from '@/views/activity/components/activity_item.vue'
  const { proxy } = getCurrentInstance()
  defineProps({
    datalist: {
      type: Array,
      required: true
    }
  })
  const share = inject('shareType')

  const shareActivity = id => {
    share({ id, type: 'activityShare' })
  }
</script>

<style lang="scss" scoped></style>

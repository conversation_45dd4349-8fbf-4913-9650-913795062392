<!-- @format -->

<template>
  <div style="padding-bottom: 1.25rem" v-if="datalist.length">
    <audit_item @show="showEvent" @audit="auditEvent" :item="item" v-for="item in datalist" :key="item.userid" />
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, inject, watch } from 'vue'
  import audit_item from '@/views/index/components/audit_item.vue'
  const { proxy } = getCurrentInstance()

  const props = defineProps({
    datalist: {
      type: Array,
      required: true
    }
  })

  const list = ref(props.datalist)

  const event = inject('selectAudit')
  const auditEvent = id => {
    event(id)
  }
  const show = inject('showFailAudit')
  const showEvent = val => {
    show(val)
  }

  watch(
    () => props.datalist,
    newValue => {
      list.value = newValue
    },
    { deep: true, immediate: true }
  )
</script>

<style lang="scss" scoped></style>

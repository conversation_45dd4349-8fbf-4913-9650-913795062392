<!-- @format -->

<template>
  <oeui-list @refresh="refresh" :top="0" :bottom="1.6" @scrollBottom="scrollBottom" @handleScroll="pageScroll" company="rem" text="已加载完全部数据" :status="listStatus">
    <div class="content_box">
      <!-- <div class="item" v-if="unionInfo.unionid > 0">
        <oeui-swiper top="0" :length="roleLegth" :current="type" @bindchange="swiperChange">
          <oeui-item>
            <div class="data_box flex flex_v">
              <div class="flex flex_ac title">
                <span class="">拉新数据</span>
                <i @click="proxy.$refs.unionIntro_dialog.open()" class="iconfont icon-tishi pr" style="font-size: 0.48rem"></i>
              </div>
              <div class="flex flex_jsb flex_1 data" style="padding: 0 0.4267rem">
                <div class="flex_dc flex_v">
                  <p>{{ data.reguser_nums }}</p>
                  <span class="fn_i">注册人数</span>
                </div>
                <div class="flex_dc flex_v">
                  <p>{{ data.reguser1_nums }}</p>
                  <span class="fn_i">已奖励人数</span>
                </div>
                <div class="flex_dc flex_v">
                  <p>{{ Number(data.total_income) >= 10000 ? (Number(data.total_income) / 10000).toFixed(2) : data.total_income }}</p>
                  <span class="fn_i">总收益({{ Number(data.total_income) >= 10000 ? '万' : '元' }})</span>
                </div>
              </div>
            </div>
          </oeui-item>
          <oeui-item v-if="unionInfo.level2_flag == 1">
            <div class="data_box flex flex_v">
              <div class="flex flex_ac title">
                <span class="">团队数据</span>
                <i @click="proxy.$refs.teamIntro_dialog.open()" class="iconfont icon-tishi pr" style="font-size: 0.48rem"></i>
              </div>
              <div class="flex flex_jsb flex_1 data" style="padding: 0 0.4267rem">
                <div class="flex_dc flex_v">
                  <p>{{ data.leadunion_nums }}</p>
                  <span class="fn_i">团队成员</span>
                </div>
                <div class="flex_dc flex_v">
                  <p>{{ data.leaduser_nums }}</p>
                  <span class="fn_i">有效用户</span>
                </div>
                <div class="flex_dc flex_v">
                  <p>{{ data.leadpay_amount }}</p>
                  <span class="fn_i">团队业绩</span>
                </div>
              </div>
            </div>
          </oeui-item>
          <oeui-item v-if="unionInfo.level4_flag == 1">
            <div class="data_box flex flex_v">
              <div class="flex flex_ac title">
                <span class="">线下数据</span>
                <i @click="proxy.$refs.afterIntro_dialog.open()" class="iconfont icon-tishi pr" style="font-size: 0.48rem"></i>
              </div>
              <div class="flex flex_jsb flex_1 data" style="padding: 0 0.4267rem">
                <div class="flex_dc flex_v">
                  <p>{{ data.crmorder_nums }}</p>
                  <span class="fn_i">签单合同数</span>
                </div>
                <div class="flex_dc flex_v">
                  <p>{{ Number(data.crmfinance_amount) >= 10000 ? (Number(data.crmfinance_amount) / 10000).toFixed(2) : data.crmfinance_amount }}</p>
                  <span class="fn_i">累计收款({{ Number(data.crmfinance_amount) >= 10000 ? '万' : '元' }})</span>
                </div>
                <div class="flex_dc flex_v">
                  <p>{{ Number(data.crmfinance_income) >= 10000 ? (Number(data.crmfinance_income) / 10000).toFixed(2) : data.crmfinance_income }}</p>
                  <span class="fn_i">分成总金额({{ Number(data.crmfinance_income) >= 10000 ? '万' : '元' }})</span>
                </div>
              </div>
            </div>
          </oeui-item>
        </oeui-swiper>
        <div class="schedule flex_dc" v-if="roleLegth > 1">
          <span v-for="i in roleLegth" :key="i" :class="type == i - 1 ? 'current' : ''"></span>
        </div>
      </div>
      <div class="item_skelecton" v-else></div> -->
      <div class="content">
        <div class="tips_box oh flex flex_ac" v-if="msglist.length">
          <i class="iconfont icon-tuiguangguangbo-01 flex_s pr" style="top: 1px"></i>
          <div class="flex_1" style="margin-left: 0.2667rem"><oeSlide :data="msglist" :duration="6000" :height="1.0667" /></div>
        </div>
        <div class="nav_box flex flex_ac flex_jsb" v-if="unionInfo.unionid > 0">
          <div class="flex_1 flex_dc flex_v" @click="$router.push('/myuser')">
            <span class="">
              <img src="@/assets/images/d_user.png" alt="" />
            </span>
            <p>单位用户</p>
          </div>
          <div class="flex_1 flex_dc flex_v" @click="$router.push('/user')">
            <span class="">
              <img src="@/assets/images/user.png" alt="" />
            </span>
            <p>用户素材</p>
          </div>
          <div class="flex_1 flex_dc flex_v" v-if="config.nav_party == 1" @click="$router.push('/party')">
            <span class="">
              <img src="@/assets/images/party.png" alt="" />
            </span>
            <p>活动素材</p>
          </div>
          <div class="flex_1 flex_dc flex_v" v-if="config.nav_activity == 1" @click="$router.push('/activity')">
            <span class="">
              <img src="@/assets/images/activity.png" alt="" />
            </span>
            <p>互选素材</p>
          </div>
          <!-- <template v-if="config.union_partner == 2">
            <div v-if="unionInfo.level2_flag == 1" @click="$router.push('/teamunion')">
              <span class="">
                <img src="@/assets/images/team.png" alt="" />
              </span>
              <p>成员管理</p>
            </div>
            <template v-else>
              <div @click="joinTeam">
                <span class="">
                  <img src="@/assets/images/team.png" alt="" />
                </span>
                <p>加入团队</p>
              </div>
            </template>
          </template>
          <div @click="$router.push('/loveeval')" v-if="config.nav_loveeval == 1">
            <span class="">
              <img src="@/assets/images/loveeval.png" />
            </span>
            <p>测评素材</p>
          </div> -->
          <!-- <div v-if="unionInfo.level1_flag == 1" @click="$router.push('/level1')">
            <span class="">
              <img src="@/assets/images/grade.png" alt="" />
            </span>
            <p>分成级别</p>
          </div> -->
        </div>
        <div class="nav_box_skelecton flex flex_ac flex_el" v-else>
          <div v-for="item in 5" :key="item">
            <span></span>
            <p></p>
          </div>
        </div>
        <div
          class="share"
          @click="
            $router.push({
              path: '/poster',
              query: {
                type: 'user',
              },
            })
          "
          v-if="unionInfo.unionid > 0"
        >
          <div class="tit">
            <span class="iconfont icon-xinrenzhuce flex_dc"></span>
            <p>邀请单身、招募红娘</p>
          </div>
          <span class="btn">生成推广码</span>
        </div>
        <div class="share_item_skelecton" v-else></div>
        <div class="column_box">
          <div class="nav flex" ref="is_navSticky" :class="is_sticky ? 'sticky' : ''">
            <span :class="view == 'user' ? 'current' : ''" @click="changType('user')">优质用户</span>
            <span v-if="config.nav_party == 1" :class="view == 'party' ? 'current' : ''" @click="changType('party')">热门活动</span>
            <span v-if="config.nav_activity == 1" :class="view == 'activity' ? 'current' : ''" @click="changType('activity')">精彩互选</span>
          </div>
          <div style="height: 1.2rem" v-if="is_sticky"></div>
          <div style="margin-top: 0.2133rem">
            <template v-if="list.length">
              <home_user :datalist="list" v-if="view == 'user'" />
              <home_party :datalist="list" v-if="view == 'party'" />
              <home_activity :datalist="list" v-if="view == 'activity'" />
            </template>
          </div>
        </div>
      </div>
    </div>
  </oeui-list>

  <oe_popup ref="afterIntro_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">线下数据</p>
      <p class="tip">{{ config.union4_userintro || '线下合作红娘绑定的线下业务数据，绑定用户线下门店进行消费均计入线下数据。' }}</p>
      <div @click="proxy.$refs.afterIntro_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>
  <oe_popup ref="teamIntro_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">团队数据</p>
      <p class="tip">{{ config.union2_userintro || '团队内推广红娘绑定的拉新数据，拉新用户的个人资料通过审核后，以及后续在平台中的消费均计算入团队数据。' }}</p>
      <div @click="proxy.$refs.teamIntro_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>
  <oe_popup ref="unionIntro_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">拉新数据</p>
      <p class="tip">
        {{ config.union1_userintro || '红娘登录状态下分享/转发平台用户资料、活动、互选内容会包含您的专属参数，引流成功注册的会员均计入到您的名下，资料审核通过后即可获得分佣提成。' }}
      </p>
      <p class="tips">绑定用户在平台中使用金豆支付的所有业务将不参与推广分成</p>
      <div @click="proxy.$refs.unionIntro_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <!--团队信息-->
  <oe_popup ref="leader_dialog" width="8rem" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="leader_dialog">
      <p class="title">团队信息</p>
      <div class="data">
        <p class="tit">
          <i></i>
          业务数据
        </p>
        <div class="num flex_dc">
          <div class="item">
            <p class="earnings">{{ Number(leader.month_leadpay_amount) >= 10000 ? (Number(leader.month_leadpay_amount) / 10000).toFixed(2) : leader.month_leadpay_amount }}</p>
            <p>本月收益({{ Number(leader.month_leadpay_amount) >= 10000 ? '万' : '元' }})</p>
          </div>
          <div class="item">
            <p class="earnings">{{ Number(leader.leadpay_amount) >= 10000 ? (Number(leader.leadpay_amount) / 10000).toFixed(2) : leader.leadpay_amount }}</p>
            <p>累计收益({{ Number(leader.leadpay_amount) >= 10000 ? '万' : '元' }})</p>
          </div>
        </div>
      </div>
      <div class="info">
        <p class="tit">
          <i></i>
          团长信息
        </p>
        <div class="user" v-if="leader.unionid">
          <span class="head oh">
            <img v-lazy="leader.headimg_url" type="union" alt="" />
          </span>
          <div class="name">
            <div class="flex flex_ac">
              <p class="flex_s">{{ leader.unionname }}</p>
              <span class="group flex_s" :class="'group' + leader.level2" v-if="leader.unionid">
                <i>
                  <img :src="getPgroupUrl('pgroup', leader.level2)" alt="" />
                </i>
                <em>{{ leader.level2_name }}</em>
              </span>
            </div>
            <p class="id">编号：{{ leader.unionid }}</p>
          </div>
        </div>
      </div>
      <div class="contact">
        <div class="flex flex_ac flex_jsb">
          <p class="flex flex_ac">
            <i class="iconfont icon-dianhua2"></i>
            <span>{{ leader.mobile || '--' }}</span>
          </p>
          <span v-if="leader.mobile" class="btn" @click="callMobile(leader.mobile)">拨打</span>
        </div>
        <div class="flex flex_ac flex_jsb">
          <p class="flex flex_ac">
            <i class="iconfont icon-a-chakanweixinbai-01"></i>
            <span>{{ leader.weixin || '--' }}</span>
          </p>
          <span v-if="leader.weixin" class="btn" @click="copy(leader.weixin)">复制</span>
        </div>
      </div>
      <div class="event" @click="proxy.$refs.leader_dialog.close()">知道了</div>
      <div v-if="unionInfo.unionid != unionInfo.be_leaderid" @click="btnQuitTeam" class="out">申请退团</div>
    </div>
  </oe_popup>

  <oe_popup ref="successteam_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">提示</p>
      <p class="tip">您的退团申请已通过，已成功退出【{{ leaderName }}】 团队</p>
      <div @click="proxy.$refs.successteam_dialog.close()" class="event">确认</div>
    </div>
  </oe_popup>
  <oe_popup ref="auditteam_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">提示</p>
      <p class="tip">退团申请已提交,管理员通过审核后,将自动退出团队.</p>
      <div @click="proxy.$refs.auditteam_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>
  <oe_popup ref="failteam_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">提示</p>
      <p class="tip">{{ fail_msg }}</p>
      <div @click="proxy.$refs.failteam_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>
</template>

<script setup>
import { ref, getCurrentInstance, computed, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import oeSlide from '@/oeui/slide.vue'
import oeuiList from '@/oeui/list.vue'
import oeuiSwiper from '@/oeui/swiper.vue'
import oeuiItem from '@/oeui/item.vue'
import oe_popup from '@/oeui/popup.vue'
import home_user from './home_user.vue'
import home_party from './home_party.vue'
import home_activity from './home_activity.vue'

import { getUser } from '@/api/user.js'
import { getParty } from '@/api/party.js'
import { getActivity } from '@/api/activity.js'
import { getPlaywall } from '@/api/msg.js'
import { quitTeam } from '@/api/team.js'
import { getMyLeader } from '@/api/cp.js'
defineProps({
  data: {
    type: Object,
  },
})

const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()

const unionInfo = computed(() => store.state.unionInfo)
const config = computed(() => store.state.config)
const roleLegth = ref(1)

const is_sticky = ref(false)

let navtop = 0
const pageScroll = val => {
  if (unionInfo.value.level3_flag != 1) {
    if (proxy.$refs.is_navSticky.offsetTop) navtop = proxy.$refs.is_navSticky.offsetTop
    if (val >= navtop) {
      is_sticky.value = true
    } else {
      is_sticky.value = false
    }
  } else {
    is_sticky.value = false
  }
}

const leader = ref({})
let leaderName = ref('')
const getLeader = () => {
  getMyLeader().then(res => {
    if (res.ret == 1) {
      leaderName.value = res.result.data.leader_name
      leader.value = res.result.data
      nextTick(() => {
        proxy.$refs.leader_dialog.open()
      })
    } else {
      OEUI.toast(res.msg || '系统繁忙,请稍后再试!')
    }
  })
}
const getPgroupUrl = (type, val) => {
  return require(`@/assets/images/grade/${type}${val}.png`)
}
const copy = data => {
  let elInput = document.createElement('input')
  elInput.value = data
  document.body.appendChild(elInput)
  elInput.select()
  document.execCommand('Copy')
  OEUI.toast({
    text: '复制成功',
  })
  elInput.remove()
}
const callMobile = data => {
  if (!data) return
  const phoneNumber = data
  const link = document.createElement('a')
  link.href = `tel:${phoneNumber}`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const is_quit = ref(true)
const fail_msg = ref('')
const btnQuitTeam = () => {
  if (!is_quit.value) return
  is_quit.value = false

  OEUI.loading.show()
  quitTeam()
    .then(res => {
      OEUI.loading.hide()
      proxy.$refs.leader_dialog.close()
      store.dispatch('getUserInfo')
      if (res.ret == 1) {
        proxy.$refs.successteam_dialog.open()
      } else if (res.ret == 2) {
        proxy.$refs.auditteam_dialog.open()
      } else if (res.ret == 3) {
        proxy.$refs.failteam_dialog.open()
        fail_msg.value = res.msg
      } else {
        OEUI.toast(res.msg || '系统繁忙,请稍后再试!')
      }
      is_quit.value = true
    })
    .catch(() => {
      OEUI.loading.hide()
      is_quit.value = true
      OEUI.toast('系统繁忙,请稍后再试!')
    })
}

const initRole = data => {
  roleLegth.value = 1
  if (data.level2_flag == 1) {
    roleLegth.value += 1
  }
  if (data.level4_flag == 1) {
    roleLegth.value += 1
  }
}

const emits = defineEmits(['join', 'chang'])

const view = ref('user')

const msglist = ref([])
const list = ref([])
const page = ref(1)
const pageCount = ref('0')
const listStatus = ref('loading')

const type = ref(0)
const swiperChange = val => {
  type.value = val
}

//获取一组播报
const getBroadcast = () => {
  getPlaywall().then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data || []
      msglist.value = res.result.data
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}
getBroadcast()

const setListType = () => {
  if (view.value == 'user') {
    return getUser({ page: page.value })
  } else if (view.value == 'party') {
    return getParty({ page: page.value })
  } else if (view.value == 'activity') {
    return getActivity({ page: page.value })
  }
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  setListType().then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
  store.dispatch('getTj')
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

const changType = val => {
  view.value = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
  emits('chang', val)
}

const joinTeam = () => {
  if (unionInfo.value.be_leaderid != 0) {
    getLeader()
    return
  }
  emits('join')
}

getList()

watch(
  () => unionInfo.value,
  newInfo => {
    if (newInfo) {
      unionInfo.value = newInfo
      initRole(newInfo)
    }
  },
  { immediate: true, deep: true },
)
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}
.font_din {
  font-family: 'cpDin';
}
.content_box {
  // margin-top: 0.2667rem;
  &::-webkit-scrollbar {
    display: none;
  }
  .item {
    height: 3.6rem;
    position: relative;
    .schedule {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 0.2133rem;
      span {
        width: 0.1333rem;
        height: 0.1333rem;
        border-radius: 50%;
        background: #cccccc;
        margin: 0 0.0533rem;
        &.current {
          background: $color_main;
        }
      }
    }
    .data_box {
      cursor: pointer;
      margin: 0 0.4267rem;
      border: 0.0267rem solid #fff;
      height: 3.5467rem;
      background: rgba(255, 255, 255, 0.32);
      border-radius: 0.32rem;
      padding: 0.32rem;
      box-sizing: border-box;
      .title span {
        font-size: 0.4267rem;
        color: #31293b;
        line-height: 0.5867rem;
        font-weight: 500;
        margin-right: 0.1333rem;
      }
      .title i {
        position: relative;
        font-size: 0.4267rem;
        color: #31293b;
        line-height: 0.5867rem;
        font-weight: 500;
      }
      .data {
        margin-top: 0.2667rem;
        div {
          p {
            font-size: 0.8533rem;
            font-weight: 700;
            color: #31293b;
            line-height: 1.0133rem;
            @extend .font_din;
          }
          span {
            font-size: 0.32rem;
            line-height: 0.4533rem;
            color: #999;
            margin-top: 0.2667rem;
          }
        }
      }
    }
  }
  .item_skelecton {
    margin: 0 0.4267rem;
    height: 3.6rem;
    background: #f1f1f1;
    border-radius: 0.32rem;
  }
  .content {
    .tips_box {
      margin: 0 0.4267rem;
      margin-top: 0.3733rem;
      background: #fff;
      height: 1.0667rem;
      border-radius: 0.32rem;
      padding: 0 0.32rem;
    }
    .nav_box {
      //margin: 0 0.4267rem;
      margin-left: 0.4267rem;
      margin-top: 0.3733rem;
      overflow-x: scroll;
      &::-webkit-scrollbar {
        width: 0;
      }
      div {
        margin-right: 0.32rem;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        span {
          display: block;
          width: 1.5467rem;
          height: 1.5467rem;
          background: #fff;
          border-radius: 0.64rem;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        p {
          font-size: 0.32rem;
          line-height: 0.4533rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          margin-top: 0.2133rem;
          color: #666;
          text-align: center;
        }
      }
    }
    .nav_box_skelecton {
      margin-top: 0.3733rem;
      div {
        text-align: center;
        span {
          display: block;
          width: 1.5467rem;
          height: 1.5467rem;
          background: #fdfdfd;
          border-radius: 0.64rem;
        }
        p {
          display: block;
          height: 17px;
          width: 80%;
          background: #fdfdfd;
          margin: 0 auto;
          margin-top: 0.1067rem;
          border-radius: 0.1333rem;
        }
      }
    }
    .share {
      height: 1.92rem;
      cursor: pointer;
      margin: 0 0.4267rem;
      margin-top: 0.4rem;
      background: url('../../assets/images/share.png') no-repeat;
      background-size: cover;
      box-sizing: border-box;
      border-radius: 0.32rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 0.32rem;
      box-shadow: 0 0.0267rem 0 0 rgba(30, 1, 138, 0.12);
      .tit {
        display: flex;
        align-items: center;
        flex: 1;
        span {
          width: 0.8rem;
          height: 0.8rem;
          border-radius: 50%;
          background: linear-gradient(178deg, #7d9ffe 0%, #7d68fe 100%);
          margin-right: 0.24rem;
          flex-shrink: 0;
          color: #fff;
          font-size: 0.5867rem;
        }
        p {
          flex: 1;
          font-size: 0.4267rem;
          line-height: 0.5867rem;
          color: $color_main;
          font-weight: 500;
        }
      }

      .btn {
        flex-shrink: 0;
        background: $color_main;
        font-size: 0.3733rem;
        color: #fff;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        line-height: 0.5333rem;
        padding: 0.2133rem 0.4267rem;
        border-radius: 0.32rem;
      }
    }
    .share_item_skelecton {
      height: 1.92rem;
      margin: 0 0.4267rem;
      margin-top: 0.4rem;
      background: #f1f1f1;
    }
    .column_box {
      margin: 0 0.4267rem;
      margin-top: 0.32rem;
      .nav {
        padding: 0.2667rem 0 0.1067rem 0;
        &.sticky {
          position: fixed;
          top: 0;
          z-index: 100;
          width: 100vw;
          margin-left: -0.4267rem;
          padding-left: 0.4267rem;
          background: url('~@/assets/images/bg_main.png') no-repeat;
          background-size: cover;
          box-sizing: border-box;
          overflow: hidden;
        }
        span {
          margin-right: 0.64rem;
          font-size: 0.3733rem;
          line-height: 0.6133rem;
          color: #666;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          position: relative;
          top: 0.1067rem;
          &.current {
            font-size: 0.48rem;
            line-height: 0.8667rem;
            font-weight: 500;
            color: #31293b;
            top: 0;
          }
        }
      }
    }
  }
}
.leader_dialog {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.64rem;
  .title {
    text-align: center;
    font-size: 0.4267rem;
    font-weight: 500;
    color: #31293b;
    line-height: 0.5867rem;
  }
  .data {
    margin-top: 0.4267rem;
    .tit {
      display: flex;
      align-items: center;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      color: #31293b;
      font-weight: 600;
      i {
        position: relative;
        top: 0.0267rem;
        background: $color_main;
        width: 0.08rem;
        height: 0.2667rem;
        margin-right: 0.1333rem;
      }
    }
    .num {
      margin-top: 0.4267rem;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        width: 0.0267rem;
        height: 0.6667rem;
        background: #f2f4f5;
      }
      .item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        font-size: 0.32rem;
        line-height: 0.5867rem;
        font-weight: normal;
        color: #999;
        .earnings {
          @extend .font_din;
          color: $color_main;
          font-weight: 700;
          font-size: 0.5333rem;
          line-height: 0.5867rem;
        }
      }
    }
  }
  .info {
    margin-top: 0.64rem;
    position: relative;
    .tit {
      display: flex;
      align-items: center;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      color: #31293b;
      font-weight: 600;
      i {
        background: $color_main;
        width: 0.08rem;
        height: 0.2667rem;
        margin-right: 0.1333rem;
      }
    }
    .user {
      margin-top: 0.4267rem;
      display: flex;
      align-items: center;
      .head {
        width: 1.3867rem;
        height: 1.3867rem;
        border-radius: 0.32rem;
        flex-shrink: 0;
        margin-right: 0.32rem;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .name {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        color: #31293b;
        font-weight: normal;
        .id {
          font-size: 0.32rem;
          line-height: 0.4533rem;
          font-weight: normal;
          color: #999;
        }
        .group {
          margin-left: 0.2667rem;
        }
      }
    }
  }
  .contact {
    margin-top: 0.32rem;
    font-size: 0.3733rem;
    line-height: 0.5867rem;
    font-weight: normal;
    color: #31293b;
    > div {
      margin-bottom: 0.2133rem;
    }
    .iconfont {
      color: $color_main;
      margin-right: 0.1333rem;
    }
    .btn {
      color: #fff;
      font-size: 0.3467rem;
      line-height: 0.5867rem;
      background: $color_main;
      padding: 0.0267rem 0.3467rem;
      border-radius: 0.2133rem;
      cursor: pointer;
    }
  }

  .event {
    margin: 0 0.6133rem;
    margin-top: 0.64rem;
    cursor: pointer;
    text-align: center;
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-weight: normal;
    border-radius: 0.5333rem;
    padding: 0.24rem 0;
  }
  .out {
    cursor: pointer;
    text-align: center;
    font-size: 0.3467rem;
    color: #31293b;
    font-weight: normal;
    line-height: 0.5867rem;
    margin-top: 0.32rem;
  }
}
.intro_dialog {
  padding: 0.64rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;
  .title {
    font-size: 0.4267rem;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    text-align: center;
  }
  .tip {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.64rem;
  }
  .tit {
    font-size: 0.3733rem;
    font-weight: normal;
    color: #31293b;
    line-height: 0.5867rem;
    margin-top: 0.32rem;
  }
  .input {
    margin-top: 0.64rem;
    background: #f2f4f5;
    padding: 0.32rem;
    border-radius: 0.64rem;
    position: relative;
    .clear {
      position: absolute;
      right: 0.32rem;
      color: #999;
    }
  }
  .close {
    position: absolute;
    font-size: 0.5333rem;
    right: 0.4rem;
    top: 0.2667rem;
    cursor: pointer;
    z-index: 100;
  }
  .event {
    cursor: pointer;
    margin: 0 0.6133rem;
    margin-top: 0.5333rem;
    font-size: 0.4267rem;
    font-weight: normal;
    color: #ffffff;
    line-height: 0.5867rem;
    background: $color_main;
    text-align: center;
    padding: 0.24rem 0;
    border-radius: 0.5333rem;
  }
}
</style>

<!-- @format -->

<template>
  <oe_popup ref="teamDialog" mode="bottom" roundStyle=".64rem" round="true" height="93vh">
    <div class="teamDialog">
      <span @click="close" class="close iconfont icon-cha"></span>
      <div class="head">
        <p class="title">选择团队</p>
        <div class="search flex flex_ac flex_jsb">
          <div class="flex_1 flex flex_ac pr">
            <i class="iconfont icon-sousuo"></i>
            <input type="text" v-model="s_name" placeholder="请输入团队名称关键词" />
            <i v-if="s_name" @click="closeName" class="pa closename iconfont icon-guanbi"></i>
          </div>
          <span @click="searchName" class="btn_search">搜索</span>
        </div>
      </div>
      <oeui-list :bottom="5.3333" @refresh="refresh" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
        <div class="list">
          <div @click="changLeaderid(item.unionid, item.leader_name)" v-for="item in list" :key="item.unionid" class="item flex flex_ac flex_jsb">
            <div class="flex flex_ac flex_1 ws">
              <span>
                <img v-lazy="item.headimg_url" type="union" alt="" />
              </span>
              <p class="ws">{{ item.leader_name }}</p>
            </div>
            <i v-if="leaderid == item.unionid" class="iconfont icon-redio_checked flex_s"></i>
            <i v-else class="iconfont icon-rediobtn_nor flex_s"></i>
          </div>
        </div>
      </oeui-list>

      <div class="bottom_btn flex_dc">
        <span @click="handleJoinTeam">立即加入</span>
      </div>
    </div>
  </oe_popup>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import oe_popup from '@/oeui/popup.vue'
import oeuiList from '@/oeui/list.vue'
import { useStore } from 'vuex'
import { getTeamList, joinTeam } from '@/api/team.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()

const emits = defineEmits(['success', 'fail'])

const open = callback => {
  getList()
  proxy.$refs.teamDialog.open(() => {
    is_flag.value = true

    if (callback) callback()
  })
}
const close = callback => {
  proxy.$refs.teamDialog.close(() => {
    if (typeof callback == 'function') callback()
  })
}

const list = ref([])
const page = ref(1)
const pageCount = ref('0')
const listStatus = ref('loading')
const s_name = ref('')

const leaderid = ref(null)
const leader_name = ref('')

const changLeaderid = (val, name) => {
  leader_name.value = name
  leaderid.value = val
}

const is_flag = ref(true)
const handleJoinTeam = () => {
  if (!leaderid.value) {
    return OEUI.toast('请选择要加入的团队')
  }
  if (!is_flag.value) return
  is_flag.value = false
  OEUI.loading.show()
  joinTeam({ id: leaderid.value })
    .then(res => {
      OEUI.loading.hide()
      if (res.ret == 1) {
        store.dispatch('getUserInfo')
        proxy.$refs.teamDialog.close(() => {
          emits('success', leader_name.value)
        })
      } else if (res.ret == 2) {
        proxy.$refs.teamDialog.close(() => {
          emits('fail', res.msg)
        })
      } else {
        OEUI.toast(res.msg || '系统繁忙,请稍后再试!')
      }
      is_flag.value = true
    })
    .catch(() => {
      OEUI.loading.hide()
      is_flag.value = true
      OEUI.toast('系统繁忙,请稍后再试!')
    })
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getTeamList({
    page: page.value,
    s_name: s_name.value,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const searchName = () => {
  page.value = 1
  getList(true)
}

const closeName = () => {
  s_name.value = ''
  page.value = 1
  getList(true)
}

const refresh = done => {
  s_name.value = ''
  page.value = 1
  listStatus.value = 'loading'
  getList(true, done)
}
const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.teamDialog {
  position: relative;
  padding: 0.32rem 0.4267rem;
  box-sizing: border-box;
  font-family: PingFang SC, PingFang SC;

  .close {
    position: absolute;
    right: 0.4rem;
    top: 0.64rem;
    font-size: 0.48rem;
    z-index: 210;
  }
  .head {
    background: #fff;
    position: relative;
    z-index: 200;
    padding-bottom: 0.48rem;
    .title {
      font-size: 0.48rem;
      font-weight: 500;
      color: #31293b;
      line-height: 0.6667rem;
      padding: 0.2667rem 0;
    }
    .search {
      background: #f2f4f5;
      border-radius: 0.32rem;
      padding: 0.1067rem 0.2133rem;
      > div {
        i {
          font-size: 0.5333rem;
          color: #000;
          margin-right: 0.2133rem;
        }
        input {
          font-size: 0.3733rem;
          font-weight: normal;
          line-height: 0.5333rem;
          position: relative;
          top: 0.0267rem;
        }
        .closename {
          right: 0.2667rem;
          font-size: 0.3733rem;
          top: 0.0533rem;
        }
      }
      .btn_search {
        cursor: pointer;
        font-size: 0.3467rem;
        font-weight: normal;
        color: #ffffff;
        line-height: 0.48rem;
        background: $color_main;
        padding: 0.1867rem 0.4267rem;
        border-radius: 0.4267rem;
      }
    }
  }
  .list {
    padding-bottom: 1.3333rem;
    .item {
      background: #f2f4f5;
      padding: 0.32rem;
      border-radius: 0.32rem;
      margin-bottom: 0.32rem;
      div {
        margin-right: 0.5333rem;
      }
      span {
        width: 0.96rem;
        height: 0.96rem;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 0.32rem;
        flex-shrink: 0;
        background: #fff;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      p {
        font-size: 0.4267rem;
        font-weight: 500;
        color: #31293b;
        line-height: 0.5867rem;
      }
      i {
        font-size: 0.5333rem;
        position: relative;
        top: 0.0267rem;
      }
      .icon-redio_checked {
        color: $color_main;
      }
      .icon-rediobtn_nor {
        color: #c5c3c7;
      }
    }
  }
  .bottom_btn {
    position: fixed;
    height: 2.1333rem;
    width: 100%;
    background: linear-gradient(to top, #fff, #ffffff8f);
    bottom: 0;
    left: 0;
    span {
      cursor: pointer;
      font-size: 0.4267rem;
      font-weight: normal;
      color: #ffffff;
      line-height: 0.5867rem;
      background: $color_main;
      padding: 0.24rem 2.3467rem;
      border-radius: 0.5333rem;
    }
  }
}
</style>

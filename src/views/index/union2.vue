<!-- @format -->

<template>
  <oeui-list @refresh="refresh" :top="0" :bottom="0" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
    <div class="content_box">
      <div class="data_box flex flex_v">
        <div class="flex flex_ac flex_jsb title">
          <span class="">团队数据</span>
          <i @click="openTeamIntro" class="iconfont icon-tishi pr" style="font-size: 0.48rem"></i>
        </div>
        <div class="flex flex_jsb flex_1 data" style="padding: 0 0.4267rem">
          <div class="flex_dc flex_v">
            <p>99</p>
            <span class="fn_i">团队成员</span>
          </div>
          <div class="flex_dc flex_v">
            <p>99</p>
            <span class="fn_i">团队业绩</span>
          </div>
          <div class="flex_dc flex_v">
            <p>99</p>
            <span class="fn_i">有效用户</span>
          </div>
        </div>
      </div>
      <div class="tips_box oh flex flex_ac">
        <i class="iconfont icon-tuiguangguangbo-01 flex_s pr" style="top: 1px"></i>
        <div class="flex_1" style="margin-left: 0.2667rem"><oeSlide :data="msglist" :duration="6000" :height="1.0667" /></div>
      </div>
      <div class="nav_box flex flex_ac flex_jsb">
        <div @click="$router.push('/teamunion')">
          <span class="">
            <img src="@/assets/images/team.png" alt="" />
          </span>
          <p>成员管理</p>
        </div>
        <div @click="$router.push('/level2')">
          <span class="">
            <img src="@/assets/images/grade.png" alt="" />
          </span>
          <p>团队级别</p>
        </div>
        <div @click="$router.push('/user')">
          <span class="">
            <img src="@/assets/images/user.png" alt="" />
          </span>
          <p>用户素材</p>
        </div>
        <div @click="$router.push('/party')">
          <span class="">
            <img src="@/assets/images/party.png" alt="" />
          </span>
          <p>全部活动</p>
        </div>
        <div @click="$router.push('/activity')">
          <span class="">
            <img src="@/assets/images/activity.png" alt="" />
          </span>
          <p>全部互选</p>
        </div>
      </div>
      <div class="share">
        <div class="flex flex_ac flex_jsb title">
          <span class="fz16" style="font-weight: 500; line-height: 0.5867rem; color: #31293b">资源获取</span>
          <span class="flex flex_ac icon fn_i">
            <i class="iconfont icon-changyonggoupiaorenbianji" style="margin-right: 0.08rem"></i>
            手动录入
          </span>
        </div>
        <p class="tips fn_i">任何人通过您的邀请方式进入平台注册即计入您名下</p>
        <div class="btn flex_dc">
          <span class="flex_1" @click="openUnionLink" style="margin-right: 0.64rem">我的专属邀请链接</span>
          <span @click="$router.push('/poster')" class="flex_1">我的专属邀请海报</span>
        </div>
      </div>
      <div class="column_box">
        <div class="nav flex">
          <span :class="view == 'user' ? 'current' : ''" @click="changType('user')">优质用户</span>
          <span :class="view == 'party' ? 'current' : ''" @click="changType('party')">热门活动</span>
          <span :class="view == 'activity' ? 'current' : ''" @click="changType('activity')">精彩互选</span>
        </div>
        <div style="margin-top: 0.2133rem">
          <home_user @card="openCrad" :datalist="list" v-if="view == 'user'" />
          <home_party :datalist="list" v-if="view == 'party'" />
          <home_activity :datalist="list" v-if="view == 'activity'" />
        </div>
      </div>
    </div>
  </oeui-list>

  <oe_popup ref="teamIntro_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">团队数据</p>
      <p class="tip">团队内推广红娘绑定的拉新数据，拉新用户的个人资料通过审核后，以及后续在平台中的消费均计算入团队数据</p>
      <div @click="proxy.$refs.teamIntro_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import oeSlide from '@/oeui/slide.vue'
import oeuiList from '@/oeui/list.vue'
import oe_popup from '@/oeui/popup.vue'
import home_user from './home_user.vue'
import home_party from './home_party.vue'
import home_activity from './home_activity.vue'

import { getUser } from '@/api/user.js'
import { getParty } from '@/api/party.js'
import { getActivity } from '@/api/activity.js'

const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const emits = defineEmits(['join', 'card', 'link'])

const view = ref('user')

const msglist = ref(['***************', 'faoifdsfusfjosdhfosd', 'dasduoaisdjasbhncdfjaisdh', 'fifjudspifjdsjdsof', 'daodasoidasoidhjasdhasju'])
const list = ref([])
const page = ref(1)
const pageCount = ref('0')
const listStatus = ref('loading')

const openCrad = id => {
  emits('card', id)
}

const setListType = () => {
  if (view.value == 'user') {
    return getUser({ page: page.value })
  } else if (view.value == 'party') {
    return getParty({ page: page.value })
  } else if (view.value == 'activity') {
    return getActivity({ page: page.value })
  }
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  setListType().then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

const changType = val => {
  view.value = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const openTeamIntro = () => {
  proxy.$refs.teamIntro_dialog.open()
}

const joinTeam = () => {
  emits('join')
}
const openUnionLink = () => {
  emits('link')
}
getList()
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}
.font_din {
  font-family: 'cpDin';
}
.content_box {
  margin-top: 0.1067rem;
  padding: 0 0.4267rem;
  .data_box {
    border: 0.0267rem solid #fff;
    height: 3.5467rem;
    background: rgba(255, 255, 255, 0.32);
    border-radius: 0.32rem;
    padding: 0.32rem;
    box-sizing: border-box;
    .title span {
      font-size: 0.4267rem;
      color: #31293b;
      line-height: 0.5867rem;
      font-weight: 500;
    }
    .title i {
      position: relative;
      font-size: 0.4267rem;
      color: #31293b;
      line-height: 0.5867rem;
      font-weight: 500;
    }
    .data {
      margin-top: 0.2667rem;
      div {
        p {
          font-size: 0.8533rem;
          font-weight: 700;
          color: #31293b;
          line-height: 1.0133rem;
          @extend .font_din;
        }
        span {
          font-size: 0.32rem;
          line-height: 0.4533rem;
          color: #999;
          margin-top: 0.2667rem;
        }
      }
    }
  }
  .tips_box {
    margin-top: 0.3733rem;
    background: #fff;
    height: 1.0667rem;
    border-radius: 0.32rem;
    padding: 0 0.32rem;
  }
  .nav_box {
    margin-top: 0.3733rem;
    div {
      span {
        display: block;
        width: 1.5467rem;
        height: 1.5467rem;
        background: #fff;
        border-radius: 0.64rem;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      p {
        font-size: 0.32rem;
        line-height: 0.4533rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        margin-top: 0.1067rem;
        color: #666;
        text-align: center;
      }
    }
  }
  .share {
    margin-top: 0.4rem;
    height: 3.68rem;
    background: url('../../assets/images/share.png') no-repeat;
    background-size: contain;
    padding: 0.32rem;
    box-sizing: border-box;
    .title {
      .icon {
        color: #7d68fe;
        font-size: 0.3467rem;
        line-height: 0.48rem;
      }
    }
    .tips {
      text-align: center;
      font-size: 0.3467rem;
      line-height: 0.5333rem;
      color: #666;
      margin-top: 0.4267rem;
    }
    .btn {
      margin-top: 0.5867rem;

      span {
        background: #7d68fe;
        text-align: center;
        color: #fff;
        border-radius: 0.4267rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        padding: 0.16rem 0;
        cursor: pointer;
      }
    }
  }
  .column_box {
    margin-top: 0.32rem;
    .nav {
      padding: 0.2667rem 0;
      span {
        margin-right: 0.64rem;
        font-size: 0.3733rem;
        line-height: 0.6133rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        position: relative;
        top: 0.1067rem;
        &.current {
          font-size: 0.48rem;
          line-height: 0.6667rem;
          font-weight: 500;
          color: #31293b;
          top: 0;
        }
      }
    }
  }
}
</style>

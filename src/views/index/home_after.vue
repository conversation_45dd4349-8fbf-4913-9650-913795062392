<!-- @format -->

<template>
  <div style="padding-bottom: 1.25rem" v-if="datalist.length">
    <after_item @start="startAfter" @show="showEvent" @after="afterEvent" :item="item" v-for="item in datalist" :key="item.afterid" />
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, inject, watch } from 'vue'
  import after_item from '@/views/index/components/after_item.vue'
  const { proxy } = getCurrentInstance()

  const props = defineProps({
    datalist: {
      type: Array,
      required: true
    }
  })

  const list = ref(props.datalist)

  const start = inject('afterStart')
  const startAfter = id => {
    start(id)
  }

  const event = inject('selectAfter')
  const afterEvent = id => {
    event(id)
  }
  const show = inject('showFailAfter')
  const showEvent = val => {
    show(val)
  }

  watch(
    () => props.datalist,
    newValue => {
      list.value = newValue
    },
    { deep: true, immediate: true }
  )
</script>

<style lang="scss" scoped></style>

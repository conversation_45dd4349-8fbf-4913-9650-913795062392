<!-- @format -->

<template>
  <div class="main">
    <div class="nav flex_dc" :class="unionInfo.enter_name ? 'jsb' : ''" v-if="unionInfo.level3_flag == 1">
      <!-- 企业名称 -->
      <div class="enterprise_row flex flex_ac" v-if="unionInfo.enter_name">
        <div class="head flex_s">
          <img v-lazy="unionInfo.headimg_url" :key="unionInfo.headimg_url" alt="" type="union" />
        </div>
        <div class="enterprise_name ws">{{ unionInfo.enter_name }}</div>
      </div>
      <div class="flex_dc switch">
        <span @click="changRole('union')" class="fn_i" :class="view == 'union' ? 'current' : ''">推广</span>
        <span @click="changRole('after')" class="fn_i" :class="view == 'after' ? 'current' : ''">服务</span>
      </div>
    </div>

    <div style="height: 0.32rem" v-if="unionInfo.level3_flag != 1 && !unionInfo.enter_name"></div>

    <!-- 企业名称 -->
    <div class="nav flex_dc jsb" v-if="unionInfo.level3_flag != 1 && unionInfo.enter_name">
      <div class="enterprise_row flex flex_ac" v-if="unionInfo.enter_name">
        <div class="head flex_s">
          <img v-lazy="unionInfo.headimg_url" :key="unionInfo.headimg_url" alt="" type="union" />
        </div>
        <div class="enterprise_name ws">{{ unionInfo.enter_name }}</div>
      </div>
    </div>
    <component :data="tj_data" @chang="selectChildType" @link="openLink" @join="joinTeam" :is="active"></component>
  </div>
  <tabbars />

  <oe_popup ref="teamfail_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="teamfail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">加入团队失败</p>
      <p class="tips">您已加入过团队，不能再加入，如需变更团队，请先退出当前团队</p>
      <div @click="proxy.$refs.teamfail_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <oe_popup ref="teamsuccess_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="teamsuccess_dialog">
      <div class="tips_img">
        <img src="@/assets/images/chat_tips.png" alt="" />
      </div>
      <p class="tips">恭喜你，成功加入团队 【{{ leader_name }}】</p>
      <div @click="proxy.$refs.teamsuccess_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <oe_popup ref="linkDialog" mode="bottom" roundStyle=".64rem" round="true">
    <div class="linkDialog">
      <div class="tit flex flex_ac flex_jsb">
        <span>获取文案及链接</span>
        <i @click="showLinkIntro" class="iconfont icon-tishi"></i>
      </div>
      <p class="slogan">{{ slogan }}</p>
      <div class="refresh" @click="getSlogan(true)">
        <i class="iconfont icon-shuaxin1" :class="is_refresh ? 'refresh_anime' : ''"></i>
        <span>换一个</span>
      </div>
      <p class="tips">上方文案可修改，复制后会自动附上链接</p>
      <div class="btn flex_dc">
        <span @click="copyLink(slogan)">复制口令</span>
      </div>
    </div>
  </oe_popup>
  <oe_popup ref="linkIntro" mode="bottom" roundStyle=".64rem" round="true">
    <div class="linkIntro">
      <div class="title flex flex_ac" @click="proxy.$refs.linkIntro.close()">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
        <p>文案及链接说明</p>
      </div>
      <p class="tips">复制出来的口令可发给微信朋友，或者微信群</p>
      <p class="tit">格式如下：</p>
      <div class="data">
        <!-- 关注公众号 👉#{{ config.wxpublic_name }} -->
        <!-- <br /> -->
        <br />
        我们是一个专业的婚恋交友平台，已经帮助大量的单身找到合适的对象，如果您的单身圈子有限，让我们来帮你~
        <br />
        -----------------------------------
        <br />
        ☎ 服务热线: {{ config.sitetel }}
        <br />
        <br />
        点击下方链接进一步了解
        <br />
        {{ config.siteurl }}
        <br />
      </div>
    </div>
  </oe_popup>

  <Team ref="Teampage" @success="joinTeamSuccess" @fail="joinTeamFail" />
  <party_share ref="partyShare" v-if="child_type == 'party'" />
  <activity_share ref="activityShare" v-if="child_type == 'activity'" />
  <notRole ref="notRoleBox" />
</template>
<script>
export default {
  name: 'Home',
}
</script>

<script setup>
import { ref, getCurrentInstance, computed, watch, nextTick, onActivated } from 'vue'
import { useStore } from 'vuex'
import tabbars from '@/components/tabbars.vue'
import Team from './team.vue'
import union1 from './union1.vue'
import union2 from './union2.vue'
import union3 from './union3.vue'
import oe_popup from '@/oeui/popup.vue'
import party_share from '@/views/party/components/party_share.vue'
import activity_share from '@/views/activity/components/activity_share.vue'
import notRole from '@/views/index/components/notRole.vue'
import { isWeiXin, getWechatShare } from '@/utils/jssdk.js'
import { useRouter } from 'vue-router'

const { proxy } = getCurrentInstance()

const router = useRouter()
const store = useStore()
const config = computed(() => store.state.config)
const unionInfo = computed(() => store.state.unionInfo)
const tj_data = computed(() => store.state.tj_data)

const active = ref(union1)
const view = ref('union')
const child_type = ref('user')

onActivated(() => {
  if (isWeiXin()) getWechatShare('index')
})

const selectChildType = val => {
  child_type.value = val
}

const changRole = val => {
  view.value = val
  if (val == 'union') active.value = union1
  if (val == 'team') active.value = union2
  if (val == 'after') active.value = union3
}

const joinTeam = () => {
  proxy.$refs.Teampage.open()
}

//加入团队失败
const joinTeamFail = val => {
  proxy.$refs.Teampage.close(() => {
    proxy.$refs.teamfail_dialog.open()
  })
}
//加入团队成功
const leader_name = ref('')
const joinTeamSuccess = val => {
  leader_name.value = val
  proxy.$refs.teamsuccess_dialog.open()
}

watch(
  () => unionInfo.value,
  newInfo => {
    if (newInfo) {
      if (newInfo.enter_flag == 2) {
        router.replace({
          path: '/unitApplyResult',
        })
      }

      if (newInfo.level1_flag != 1) {
        nextTick(() => {
          proxy.$refs.notRoleBox.open(newInfo.level1_flag)
        })
      }
      unionInfo.value = newInfo
    }
  },
  { immediate: true, deep: true },
)
watch(
  () => config.value,
  newInfo => {
    if (newInfo) {
      config.value = newInfo
    }
  },
  { immediate: true, deep: true },
)
watch(
  () => tj_data.value,
  newData => {
    if (newData) {
      tj_data.value = newData
    }
  },
  { immediate: true, deep: true },
)
</script>

<style lang="scss" scoped>
.main {
  position: absolute;
  //width: 100vw;
  min-height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: hidden;
  box-sizing: border-box;

  & > .nav.jsb {
    justify-content: space-between;
    margin: 0 0.4267rem;

    .enterprise_row {
      flex: 1;
    }

    .switch {
      background: rgba(255, 255, 255, 0.32);
      border: 0.0267rem solid #ffffff;
      font-size: 0.32rem;
      line-height: 0.5333rem;
      color: #666;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      padding: 0.0267rem;
      box-sizing: border-box;
      border-radius: 0.5333rem;

      span {
        padding: 0.0533rem 0.32rem;

        &.current {
          border-radius: 0.5333rem;
          color: #fff;
          background: $color_main;
        }
      }
    }
  }

  & > .nav {
    top: 0;
    position: sticky;
    z-index: 500;
    height: 1.6rem;
    background: url('~@/assets/images/bg_main.png');
    background-size: cover;

    .enterprise_row {
      margin-right: 0.2133rem;

      .head img {
        width: 0.8533rem;
        height: 0.8533rem;
        border-radius: 50%;
        box-shadow: 0px 4px 10px 0px rgba(107, 68, 223, 0.16);
      }

      .enterprise_name {
        font-size: 0.48rem;
        font-weight: 500;
        color: #000;
        margin-left: 0.2133rem;
      }
    }

    & > .switch {
      background: rgba(255, 255, 255, 0.32);
      border: 0.0267rem solid #ffffff;
      font-size: 0.4267rem;
      line-height: 0.5867rem;
      color: #666;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      padding: 0.08rem;
      box-sizing: border-box;
      border-radius: 0.5333rem;
    }

    span {
      padding: 0.1067rem 0.7467rem;

      &.current {
        border-radius: 0.5333rem;
        color: #fff;
        background: $color_main;
      }
    }
  }
}

.teamfail_dialog {
  border-radius: 0.64rem 0.64rem 0 0;
  padding: 0.64rem 0.48rem;
  text-align: center;

  .tips_img {
    position: absolute;
    left: 50%;
    top: -2rem;
    transform: translateX(-50%);
    width: 3.2rem;
    height: 2.88rem;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .title {
    margin-top: 0.6933rem;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
  }

  .tips {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    color: #666666;
  }

  .event {
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin: 0 0.7733rem;
    margin-top: 0.64rem;
    padding: 0.24rem 0;
  }
}

.teamsuccess_dialog {
  text-align: center;
  padding: 0.64rem 1.2533rem;
  background: url('../../assets/images/dailog_bg.png');
  background-size: cover;
  border-radius: 0.64rem;
  overflow: hidden;

  .tips_img {
    position: absolute;
    left: 50%;
    top: -1.7333rem;
    transform: translateX(-50%);
    width: 4.2667rem;
    height: 2.6133rem;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .tips {
    margin-top: 0.64rem;
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
  }

  .event {
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin-top: 0.64rem;
    padding: 0.24rem 0;
  }
}

.linkDialog {
  font-family: PingFang SC, PingFang SC;
  padding: 0.5867rem 0.4267rem 0.64rem 0.4267rem;

  .tit {
    span {
      font-size: 0.48rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      color: #31293b;
      line-height: 0.6667rem;
    }

    i {
      cursor: pointer;
      color: #000;
      font-size: 0.48rem;
    }
  }

  .slogan {
    margin-top: 0.3467rem;
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #31293b;
    line-height: 0.64rem;
    background: #f2f4f5;
    padding: 0.4267rem;
    border-radius: 0.32rem;
  }

  .refresh {
    transform: all 0.3s;
    color: #0570f1;

    i {
      margin-right: 0.1333rem;
    }

    margin-top: 0.32rem;
    display: flex;
    align-items: center;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #0570f1;
    line-height: 0.64rem;
  }

  .data {
    margin-top: 0.5333rem;
    background: #f2f4f5;
    border-radius: 0.32rem;
    padding: 0.4267rem;
    margin-bottom: 0.4267rem;
    display: flex;
    flex-wrap: wrap;

    span {
      width: 50%;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #31293b;
      line-height: 0.64rem;
    }
  }

  .tips {
    font-size: 0.32rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #666666;
    line-height: 0.64rem;
    margin-top: 0.64rem;
  }

  .btn {
    margin-top: 0.64rem;

    span {
      cursor: pointer;
      font-size: 0.4267rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      color: #ffffff;
      line-height: 0.5867rem;
      background: $color_main;
      padding: 0.24rem 2.3467rem;
      border-radius: 0.5333rem;
    }
  }
}

.linkIntro {
  padding: 0.5867rem 0.4267rem 0.64rem 0.4267rem;

  .title {
    font-size: 0.48rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #31293b;
    line-height: 0.6667rem;

    i {
      font-weight: 600;
      font-size: 0.48rem;
      margin-right: 0.0533rem;
    }
  }

  .tips {
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #666666;
    line-height: 0.64rem;
    padding-left: 0.5333rem;
  }

  .tit {
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #999999;
    line-height: 0.64rem;
    margin: 0.4267rem;
  }

  .data {
    margin-top: 0.5333rem;
    background: #f2f4f5;
    border-radius: 0.32rem;
    padding: 0.4267rem;
    margin-bottom: 0.4267rem;
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #31293b;
    line-height: 0.64rem;
  }
}

.refresh_anime {
  transform: rotate(100deg);
}
</style>

<!-- @format -->

<template>
  <topBg />
  <div class="oe_login">
    <div class="back" @click="back">
      <i class="iconfont icon-zuo"></i>
    </div>
    <div style="height: 4.1333rem"></div>
    <div class="content_box">
      <p class="title">红娘推广中心</p>
      <p class="tips">帐号密码登录</p>
      <div class="oe_input">
        <input type="text" placeholder="请输入手机号或用户名" maxlength="11" v-model="state.mobile" />
        <span class="iconfont icon-guanbi" v-if="state.mobile" @click="clearInput('mobile')"></span>
      </div>
      <div class="oe_input">
        <input style="padding-top: 0.2133rem" type="password" placeholder="请输入密码" v-model="state.password" v-if="!inputStatus" />
        <input type="text" placeholder="请输入密码" v-model="state.password" v-else />
        <span class="oeuifont oeui-yanjing" :class="inputStatus ? 'current' : ''" v-if="state.password" @click="toggleInput"></span>
      </div>
      <div class="btn_box">
        <div class="btn" @click="sendLogin" :class="state.tcp && state.mobile && state.password ? 'current' : ''">登录</div>
      </div>
      <div class="more">
        <p @click="goPage('/reg')" class="more">验证码登录</p>
      </div>
      <div class="weixin flex_dc" v-if="isWeiXin()">
        <p style="background: linear-gradient(90deg, rgba(216, 216, 216, 0) 0%, #d8d8d8 50%, rgba(216, 216, 216, 1) 100%)"></p>
        <i @click="goPage('/reg/wechat')" class="iconfont icon-weixin"></i>
        <p></p>
      </div>
      <div class="oe_tcp" v-if="hideshow">
        <span class="oeuifont oeui-checkbox" :class="state.tcp ? 'current' : ''" @click="selectTcp"></span>
        <span @click="selectTcp">已阅读同意</span>
        <i @click="goRegTcp('meipo_xieyi')">《兼职红娘注册协议》</i>
        <i @click="goRegTcp('policy')">《个人信息保护政策》</i>
      </div>
    </div>

    <oeui-popup ref="tcpTips" mode="center" width="80%" :round="true" :maskClose="false">
      <div class="tcp_tips">
        <h3 class="title">{{ xieyi_title }}</h3>
        <div class="content" v-html="state.htmlTcp"></div>
        <div class="btn" @click="tcpAgree">同意并遵守协议</div>
      </div>
    </oeui-popup>

    <oeui-popup ref="consentXieyiEl" mode="center" width="80%" :round="true" :maskClose="false">
      <div class="tcp_tips flex_dc flex_v" style="padding: 0.5333rem 0.8rem">
        <h3 class="fz20">温馨提示</h3>
        <p style="margin-top: 0.4rem; text-align: center; color: #666">请阅读并同意《兼职红娘注册协议》和《个人信息保护政策》</p>
        <div class="flex_dc fz14" style="margin-top: 0.5333rem; width: 100%">
          <span @click="closeXieyi" class="flex_dc flex_1" style="margin-right: 0.4rem; height: 1.0667rem; border-radius: 0.5333rem; border: 0.0267rem solid #ebebeb; box-sizing: border-box; cursor: pointer">取消</span>
          <span @click="consentXieyi" class="flex_dc flex_1 bg_main" style="height: 1.0667rem; color: #fff; border-radius: 0.5333rem; cursor: pointer">同意并继续</span>
        </div>
      </div>
    </oeui-popup>
  </div>
</template>

<script setup>
import { getCurrentInstance, reactive, ref, onMounted, computed, nextTick, onUnmounted } from 'vue'
import topBg from '@/components/top_bg'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { setHtmlExp } from '../../utils/main'
import { loginPassword, getXieyi } from '@/api/login.js'
import oeuiPopup from '../../oeui/popup.vue'
import { isWeiXin } from '@/utils/jssdk.js'
const { proxy } = getCurrentInstance()

const OEUI = proxy.OEUI
const route = useRoute()
const router = useRouter()
const store = useStore()
const config = computed(() => store.state.config)
const state = reactive({
  mobile: '',
  password: '',
  tcp: false,
  codeSecond: 60,
  codeStatus: true,
  htmlTcp: '',
})
const inputStatus = ref(false)

const hideshow = ref(true)
const goPage = val => {
  hideshow.value = true
  router.replace(val)
}
const docmHeight = ref(document.documentElement.clientHeight || document.body.clientHeight)

const hanleOnresize = () => {
  let val = document.documentElement.clientHeight || document.body.clientHeight
  if (docmHeight.value > val) {
    //隐藏
    hideshow.value = false
  } else {
    //显示
    hideshow.value = true
  }
}
window.addEventListener('resize', hanleOnresize)
onUnmounted(() => {
  window.removeEventListener('resize', hanleOnresize)
})

onMounted(() => {
  let status = sessionStorage.getItem('regTcpAgree')
  if (!status) {
    //getTcpHtml('meipo_xieyi')
  } else {
    state.tcp = true
  }
})
let sms = sessionStorage.getItem('smsCheck')
if (sms == 0) {
  router.replace({ path: '/reg' })
}

const clearInput = name => {
  //清除input值
  state[name] = ''
}
const selectTcp = () => {
  //勾选协议
  state.tcp = state.tcp ? false : true
}

const toggleInput = () => {
  //切换密码显示
  inputStatus.value = !inputStatus.value
}

const closeXieyi = () => {
  proxy.$refs.consentXieyiEl.close()
}
const consentXieyi = () => {
  sessionStorage.setItem('regTcpAgree', '1')
  state.tcp = true
  proxy.$refs.consentXieyiEl.close()
}

const sendLogin = () => {
  if (state.mobile == '') {
    OEUI.toast({
      text: '请填写手机号或用户名',
    })
    return
  }
  if (state.password == '') {
    OEUI.toast({
      text: '请填写密码',
    })
    return
  }
  if (state.tcp == 0) {
    //getTcpHtml()
    proxy.$refs.consentXieyiEl.open()
    return
  }
  OEUI.loading.show('登录中...')
  loginPassword({
    loginname: state.mobile,
    password: state.password,
    weixinid: sessionStorage.getItem('openid') || '',
    tg_unionid: sessionStorage.getItem('tg_unionid') || '',
    tg_uid: sessionStorage.getItem('tg_uid') || '',
  }).then(res => {
    OEUI.loading.hide()
    if (res.ret == 1) {
      store.dispatch('setUserInfo', res.result)
      let info = res.result.info
      if (info.level1_flag == 1 || info.level2_flag == 1 || info.level3_flag == 1 || info.level4_flag == 1) {
        router.replace({ path: '/home' })
      } else {
        router.replace({
          path: '/active',
          query: {
            flag: 1,
          },
        })
      }
      state.mobile = ''
      state.password = ''
    } else {
      OEUI.toast({
        text: res.msg || '登录失败，请检查',
      })
    }
  })
}
const goRegTcp = name => {
  getTcpHtml(name)
}
const xieyi_title = ref('兼职红娘注册协议')
const getTcpHtml = (type = 'meipo_xieyi') => {
  xieyi_title.value = type == 'meipo_xieyi' ? '兼职红娘注册协议' : '个人信息保护政策'
  getXieyi({
    idmark: type,
  }).then(res => {
    if (res.ret == 1) {
      state.htmlTcp = setHtmlExp(res.result.content)
      nextTick(() => {
        proxy.$refs.tcpTips.open()
      })
    }
  })
}
const tcpAgree = () => {
  sessionStorage.setItem('regTcpAgree', '1')
  state.tcp = true
  proxy.$refs.tcpTips.close()
  if (state.mobile && state.code) {
    sendLogin()
  }
}
const back = () => {
  hideshow.value = true
  if (window.history.length <= 1) {
    router.replace({ path: '/' })
  } else {
    router.back()
  }
}
</script>

<style lang="scss" scoped>
@import url('~@/oeui/icon/iconfont.css');
@import url('~@/assets/iconfont/iconfont.css');
.bg_main {
  background: $color_main;
}
.oe_login {
  position: relative;
  min-height: 100%;
  box-sizing: border-box;
  .back {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    position: absolute;
    width: 1.3333rem;
    height: 1.3333rem;
    color: #fff;
    left: 0;
    i {
      font-size: 0.5333rem;
    }
  }

  .content_box {
    padding: 0 0.64rem;
    height: calc(100vh - 4.1333rem);
    position: relative;
    z-index: 1;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 28px 28px 0px 0px;
  }

  .title {
    font-size: 0.8533rem;
    padding-top: 0.7467rem;
    color: $color_main;
    line-height: 1.0133rem;
    font-weight: 600;
  }
  .tips {
    font-family: PingFang SC, PingFang SC;
    margin-top: 0.4rem;
    color: #666;
    font-weight: normal;
    font-size: 0.3467rem;
    margin-bottom: 0.9067rem;
  }

  .oe_input {
    background: #fff;
    position: relative;
    height: 1.28rem;
    line-height: 1.28rem;
    margin-top: 0.4267rem;
    border-radius: 0.64rem;

    input {
      width: 100%;
      height: 1.0667rem;
      box-sizing: border-box;
      padding: 0 0.5333rem;
      font-size: 0.4267rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;

      &::placeholder {
        color: #c3c3c5;
      }
    }

    .oeuifont {
      color: #c9cdd4;
      position: absolute;
      right: 0;
      padding-left: 0.267rem;
      padding-right: 0.4rem;
      font-size: 0.56rem;
      line-height: 1.333rem;
      cursor: pointer;

      &.oeui-yanjing {
        font-size: 0.613rem;

        &.current {
          color: $color_main;
        }
      }
    }
    .iconfont {
      color: #c9cdd4;
      position: absolute;
      right: 0;
      padding-left: 0.267rem;
      padding-right: 0.4rem;
      font-size: 0.56rem;
      line-height: 1.333rem;
      cursor: pointer;
      &.current {
        color: $color_main;
      }
    }

    .getcode {
      position: absolute;
      color: $color_main;
      font-size: 0.4267rem;
      cursor: pointer;
      right: 0.5333rem;

      &.current {
        color: #999999;
      }
    }
  }

  .btn_box {
    margin-top: 0.8533rem;

    .btn {
      width: 100%;
      text-align: center;
      background-color: $color_main;
      font-size: 0.427rem;
      line-height: 0.5867rem;
      border-radius: 1rem;
      color: #ffffff;
      padding: 0.2933rem 0;
      box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      cursor: pointer;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;
      opacity: 0.5;

      &.current {
        opacity: 1;
      }
    }
  }

  .oe_tcp {
    position: fixed;
    left: 0;
    width: 100%;
    bottom: 0.5333rem;
    font-size: 0.32rem;
    line-height: 0.4533rem;
    display: flex;
    justify-content: center;
    align-content: center;
    color: #999999;
    cursor: pointer;

    span.oeuifont {
      width: 0.48rem;
      height: 0.48rem;
      border-radius: 50%;
      box-sizing: border-box;
      border: 1px solid #e9e9e9;
      line-height: 0.48rem;
      font-size: 0.32rem;
      text-align: center;
      color: #ffffff;
      margin-right: 0.16rem;
      background: #ffffff;

      &.current {
        background: $color_main;
      }
    }

    i {
      color: $color_main;
    }
  }

  .more {
    margin-top: 0.4267rem;
    text-align: center;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: normal;
    cursor: pointer;
    color: $color_main;
    i {
      color: $color_main;
    }
  }
  .weixin {
    margin-top: 1.3333rem;
    p {
      flex: 1;
      height: 0.0267rem;
      background: linear-gradient(90deg, rgba(216, 216, 216, 1) 0%, #d8d8d8 50%, rgba(216, 216, 216, 0) 100%);
    }
    i {
      flex-shrink: 0;
      font-size: 0.64rem;
      color: #28c445;
      margin: 0 0.5333rem;
    }
  }
}

.tcp_tips {
  .title {
    text-align: center;
    font-size: 0.48rem;
    font-weight: 600;
    padding-top: 0.67rem;
    padding-bottom: 0;
    color: #333333;
  }

  .content {
    font-size: 0.373rem;
    color: #333333;
    padding: 0.533rem 0.533rem 0;
    max-height: 7rem;
    overflow-x: hidden;
    overflow-y: scroll;

    span {
      display: inline-block;
    }
  }

  .btn {
    margin: 0.533rem auto 0.667rem;
    width: 80%;
    height: 1.2rem;
    line-height: 1.2rem;
    color: #ffffff;
    background: $color_main;
    border-radius: 1rem;
    text-align: center;
    font-size: 0.4rem;
    cursor: pointer;
  }
}
</style>

<!-- @format -->

<template>
  <div class="result_box">
    <div class="res_row">
      <div class="no_res flex flex_dc">
        <img src="@/assets/images/pic_res.png" alt="" />
      </div>
      <div class="no_text">
        <p class="title flex flex_dc">申请已提交，等待审核</p>
        <div class="tips flex flex_v flex_dc">
          <p>我们已经收到您的企业申请</p>
          <p>平台管理员将尽快审核</p>
        </div>
      </div>

      <div class="kf_box flex flex_dc" @click="openKefu">
        <span>联系客服</span>
      </div>
    </div>

    <oe_popup ref="focus_tencent" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
      <div class="focus_tencent">
        <p class="title">联系客服</p>

        <div class="qr">
          <img :src="kefuInfo.wxcode_url" alt="" />
        </div>
        <p class="tips" style="color: #2a2546; font-size: 0.4267rem">{{ kefuInfo.title }}</p>
        <p class="tips flex flex_ac flex_jsb">
          <span class="flex flex_ac">
            <span class="tips_title">手机号：</span>
            <span class="tips_content">{{ kefuInfo.mobile }}</span>
          </span>
          <span class="c_btn flex flex_dc" v-if="kefuInfo.mobile" @click="callMobile(kefuInfo.mobile)">拨打</span>
        </p>
        <p class="tips flex flex_ac flex_jsb">
          <span class="flex flex_ac">
            <span class="tips_title">微信号：</span>
            <span class="tips_content">{{ kefuInfo.weixin }}</span>
          </span>
          <span class="c_btn flex flex_dc" v-if="kefuInfo.weixin" @click="copys(kefuInfo.weixin)">复制</span>
        </p>
        <p class="tap" v-html="kefuInfo.tips"></p>
        <span @click="closeKefu" class="close iconfont icon-guanbi"></span>
      </div>
    </oe_popup>
  </div>
</template>

<script>
import { getCurrentInstance, reactive, toRefs, onMounted, computed, watch } from 'vue'
import { useStore } from 'vuex'
import oe_popup from '@/oeui/popup.vue'
import { getKefu } from '@/api/cp.js'
import { isWeiXin } from '@/utils/jssdk.js'
import { useRouter } from 'vue-router'
export default {
  components: {
    oe_popup,
  },
  setup() {
    const { proxy } = getCurrentInstance()
    const OEUI = proxy.OEUI
    const store = useStore()
    const router = useRouter()
    const config = computed(() => store.state.config)
    const unionInfo = computed(() => store.state.unionInfo)
    const state = reactive({
      mobile: '',
      enterName: '',
      unionName: '',
      code: '',
      tcp: false,
      codeSecond: 60,
      codeStatus: true,
      htmlTcp: '',
      enterNameMessage: '', // 单位名称检测消息
      kefuInfo: {},
    })
    onMounted(() => {
      document.querySelector('.top_box').style.display = 'none'
      getKefuInfo()
    })

    watch(
      () => unionInfo.value,
      newInfo => {
        if (newInfo) {
          if (newInfo.enter_flag == 1) {
            router.replace({
              path: '/home',
            })
          }
          unionInfo.value = newInfo
        }
      },
      { immediate: true, deep: true },
    )

    const getKefuInfo = () => {
      getKefu().then(res => {
        if (res.ret == 1) {
          state.kefuInfo = res.result.data[0]
        }
      })
    }

    const openKefu = () => {
      proxy.$refs.focus_tencent.open()
    }

    const closeKefu = () => {
      proxy.$refs.focus_tencent.close()
    }

    const copys = data => {
      let elInput = document.createElement('input')
      elInput.value = data
      document.body.appendChild(elInput)
      elInput.select()
      document.execCommand('Copy')
      OEUI.toast({
        text: '复制成功',
      })
      elInput.remove()
    }

    const callMobile = data => {
      if (!data) return
      const phoneNumber = data
      const link = document.createElement('a')
      link.href = `tel:${phoneNumber}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

    return {
      config,
      ...toRefs(state),
      isWeiXin,
      copys,
      callMobile,
      getKefuInfo,
      openKefu,
      closeKefu,
    }
  },
}
</script>
<style lang="scss" scoped>
.result_box {
  width: 100vw;
  height: 100vh;
  background-color: #fff;

  .no_res {
    img {
      width: 4.6667rem;
      margin-top: 2.6667rem;
      margin-bottom: 0.5333rem;
    }
  }

  .no_text {
    .title {
      color: #1c2023;
      font-size: 0.48rem;
      margin-bottom: 0.8rem;
    }

    .tips {
      color: #606972;
      font-size: 0.3733rem;
    }
  }
}

.kf_box {
  position: fixed;
  left: 0;
  width: 100%;
  bottom: 0.5333rem;
  font-size: 0.32rem;
  line-height: 0.4533rem;
  display: flex;
  justify-content: center;
  align-content: center;
  cursor: pointer;

  img {
    width: 0.48rem;
    height: 0.48rem;
  }
  span {
    color: #3d3d3d;
    font-size: 0.3733rem;
    margin-left: 0.1067rem;
  }
}
</style>
<style lang="scss" scoped>
.focus_tencent {
  border-radius: 0.64rem 0.64rem 0 0;
  padding: 0.64rem 0.8533rem 0.8533rem 0.8533rem;
  .title {
    text-align: center;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    padding-top: 0;
  }
  .tips {
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    color: #666;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    text-align: center;
    margin-top: 0.4267rem;

    .tips_title {
      display: block;
      width: 100rpx;
    }

    .c_btn {
      width: 1.28rem;
      height: 0.64rem;
      border-radius: 0.16rem;
      border: 0.0267rem solid #7d68fe;
      background-color: rgba($color: #7d68fe, $alpha: 0.08);
      color: #7d68fe;
    }
  }
  .qr {
    box-sizing: border-box;
    width: 3.7333rem;
    height: 3.7333rem;
    background: fff;
    margin: 0.4267rem auto;
    border: 0.16rem solid #fff;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .tap {
    text-align: center;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    color: #666;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
  }
  .close {
    position: absolute;
    font-size: 0.7467rem;
    color: #fff;
    left: 50%;
    transform: translateX(-50%);
    bottom: -1.3333rem;
  }
}
</style>

<!-- @format -->

<template>
  <div class="oe_wechat">
    <div class="icon">
      <p class="wechat iconfont icon-weixin"></p>
      <p class="text">&#12288;微信登录中...</p>
      <p class="loading">
        <i class="iconfont icon-loading"></i>
      </p>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, ref } from 'vue'
  import { useStore } from 'vuex'
  import { useRoute, useRouter } from 'vue-router'
  import { checkWxauth, wxauthCallback } from '@/api/login.js'
  const { proxy } = getCurrentInstance()
  const OEUI = proxy.OEUI
  const route = useRoute()
  const router = useRouter()
  const store = useStore()
  const getCheckLogin = () => {
    //检查是否需要微信登录
    let url = sessionStorage.getItem('forwardUrl')
    checkWxauth({
      forward: url || ''
    })
      .then(res => {
        if (res.ret == 1) {
          window.location.replace(res.result)
        } else {
          goLogin()
        }
      })
      .catch(() => {
        goLogin()
      })
  }
  const wechatLogin = code => {
    //微信登录
    let url = sessionStorage.getItem('forwardUrl')
    wxauthCallback({
      code: code,
      forward: url || ''
    })
      .then(res => {
        if (res.ret == 1) {
          store.dispatch('setUserInfo', res.result).then(() => {
            router.replace('/home')
          })
        } else if (res.ret == 2) {
          goLogin(res.result)
        } else {
          OEUI.toast({
            text: res.msg || '登录失败，请检查'
          })
        }
      })
      .catch(() => {
        goLogin()
      })
  }
  if (route.query.code) {
    wechatLogin(route.query.code)
  } else {
    getCheckLogin()
  }
  const goLogin = openid => {
    OEUI.toast({
      text: '微信登录失败！'
    })
    setTimeout(() => {
      router.replace({ path: '/reg', query: { openid: openid || '' } })
    }, 500)
  }
</script>
<style lang="scss" scoped>
  //@import url('../../assets/iconfont/iconfont.css');
  .oe_wechat {
    position: fixed;
    min-height: 100%;
    top: 0;
    width: 100vw;
    .icon {
      position: absolute;
      left: 50%;
      top: 45%;
      transform: translate(-50%, -50%);
      .wechat {
        font-size: 1rem;
        color: #07c160;
        text-align: center;
      }
      .text {
        padding-top: 0.533rem;
        font-size: 0.373rem;
        color: #333333;
        text-align: center;
      }
      .loading {
        color: #6a6da9;
        padding-top: 0.4rem;
        text-align: center;
        i {
          font-size: 0.8rem;
          animation: loading 1.5s linear infinite;
        }
      }
      .url {
        padding: 0.533rem;
      }
    }
  }
  @keyframes loading {
    to {
      transform: rotate(0);
    }
    from {
      transform: rotate(-360deg);
    }
  }
</style>

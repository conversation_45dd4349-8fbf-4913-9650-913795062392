<!-- @format -->

<template>
  <topBg />
  <div class="oe_login">
    <div class="back" @click="back">
      <i class="iconfont icon-zuo"></i>
    </div>
    <div style="height: 4.1333rem"></div>
    <div class="content_box">
      <p class="title">申请单位帐号</p>
      <p class="tips">未注册手机号验证后自动注册</p>
      <div class="oe_input">
        <input type="text" placeholder="手机号码" maxlength="11" v-model="mobile" @keyup="numberInput('mobile')" />
        <span class="iconfont icon-guanbi" v-if="mobile" @click="clearInput('mobile')"></span>
      </div>
      <div class="oe_input">
        <input type="text" placeholder="验证码" maxlength="4" v-model="code" />
        <span class="getcode" @click="getCode" v-if="codeStatus">获取验证码</span>
        <span class="getcode current" v-else>{{ codeSecond }}秒重新发送</span>
      </div>
      <div class="oe_input">
        <input type="text" placeholder="单位名称" v-model="enterName" @blur="checkEnterNameDuplicate" />
        <span class="iconfont icon-guanbi" v-if="enterName" @click="clearInput('enterName')"></span>
      </div>
      <span v-if="enterNameMessage" style="color: #ff6666; font-size: 0.32rem; margin-top: 0.2rem; padding-left: 0.5333rem">
        {{ enterNameMessage }}
      </span>
      <div class="oe_input">
        <input type="text" placeholder="联络人" v-model="unionName" />
        <span class="iconfont icon-guanbi" v-if="unionName" @click="clearInput('unionName')"></span>
      </div>
      <div class="btn_box">
        <div class="btn" @click="sendLogin" :class="tcp && mobile && code && enterName && unionName && !enterNameMessage ? 'current' : ''">注册</div>
      </div>

      <div class="kf_box flex flex_dc" @click="openKefu">
        <img src="@/assets/images/kf.png" alt="" />
        <span>联系客服</span>
      </div>

      <div class="weixin flex_dc" v-if="isWeiXin()">
        <p style="background: linear-gradient(90deg, rgba(216, 216, 216, 0) 0%, #d8d8d8 50%, rgba(216, 216, 216, 1) 100%)"></p>
        <i @click="goPage('/reg/wechat')" class="iconfont icon-weixin"></i>
        <p></p>
      </div>
      <div class="oe_tcp" v-if="hideshow.flag">
        <span class="oeuifont oeui-checkbox" :class="tcp ? 'current' : ''" @click="selectTcp"></span>
        <span @click="selectTcp">已阅读同意</span>
        <i @click="goRegTcp('meipo_xieyi')">《兼职红娘注册协议》</i>
        <i @click="goRegTcp('policy')">《个人信息保护政策》</i>
      </div>
    </div>

    <ver-fication ref="oeVerFicat" @callback="smsCallback"></ver-fication>

    <oeui-popup ref="tcpTips" mode="center" width="80%" :round="true" :maskClose="false">
      <div class="tcp_tips">
        <h3 class="title">{{ xieyi_title }}</h3>
        <div class="content" v-html="htmlTcp"></div>
        <div class="btn" @click="tcpAgree">同意并遵守协议</div>
      </div>
    </oeui-popup>

    <oeui-popup ref="consentXieyiEl" mode="center" width="80%" :round="true" :maskClose="false">
      <div class="tcp_tips flex_dc flex_v" style="padding: 0.5333rem 0.8rem">
        <h3 class="fz20">温馨提示</h3>
        <p style="margin-top: 0.4rem; text-align: center; color: #666">请阅读并同意《兼职红娘注册协议》和《个人信息保护政策》</p>
        <div class="flex_dc fz14" style="margin-top: 0.5333rem; width: 100%">
          <span @click="closeXieyi" class="flex_dc flex_1" style="margin-right: 0.4rem; height: 1.0667rem; border-radius: 0.5333rem; border: 0.0267rem solid #ebebeb; box-sizing: border-box; cursor: pointer">取消</span>
          <span @click="consentXieyi" class="flex_dc flex_1 bg_main" style="height: 1.0667rem; color: #fff; border-radius: 0.5333rem; cursor: pointer">同意并继续</span>
        </div>
      </div>
    </oeui-popup>

    <oe_popup ref="focus_tencent" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
      <div class="focus_tencent">
        <p class="title">联系客服</p>

        <div class="qr">
          <img :src="kefuInfo.wxcode_url" alt="" />
        </div>
        <p class="tips" style="color: #2a2546; font-size: 0.4267rem">{{ kefuInfo.title }}</p>
        <p class="tips flex flex_ac flex_jsb">
          <span class="flex flex_ac">
            <span class="tips_title">手机号：</span>
            <span class="tips_content">{{ kefuInfo.mobile }}</span>
          </span>
          <span class="c_btn flex flex_dc" v-if="kefuInfo.mobile" @click="callMobile(kefuInfo.mobile)">拨打</span>
        </p>
        <p class="tips flex flex_ac flex_jsb">
          <span class="flex flex_ac">
            <span class="tips_title">微信号：</span>
            <span class="tips_content">{{ kefuInfo.weixin }}</span>
          </span>
          <span class="c_btn flex flex_dc" v-if="kefuInfo.weixin" @click="copys(kefuInfo.weixin)">复制</span>
        </p>
        <p class="tap" v-html="kefuInfo.tips"></p>
        <span @click="closeKefu" class="close iconfont icon-guanbi"></span>
      </div>
    </oe_popup>
  </div>
</template>

<script>
import { getCurrentInstance, reactive, nextTick, toRefs, onMounted, computed, ref, onUnmounted, watch } from 'vue'
import topBg from '@/components/top_bg'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import verFication from '@/components/verification.vue'
import oe_popup from '@/oeui/popup.vue'
import { setHtmlExp } from '@/utils/main'
import { regenteRprise, codeSend, getXieyi, checkEntername } from '@/api/login.js'
import { getKefu } from '@/api/cp.js'
import oeuiPopup from '@/oeui/popup.vue'
import { isWeiXin } from '@/utils/jssdk.js'
export default {
  components: {
    verFication,
    oeuiPopup,
    topBg,
    oe_popup,
  },
  setup() {
    const { proxy } = getCurrentInstance()
    const OEUI = proxy.OEUI
    const router = useRouter()
    const store = useStore()
    const config = computed(() => store.state.config)
    const state = reactive({
      mobile: '',
      enterName: '',
      unionName: '',
      code: '',
      tcp: false,
      codeSecond: 60,
      codeStatus: true,
      htmlTcp: '',
      enterNameMessage: '', // 单位名称检测消息
      kefuInfo: {},
    })
    onMounted(() => {
      let status = sessionStorage.getItem('regTcpAgree')
      if (!status) {
        //getTcpHtml('meipo_xieyi')
      } else {
        state.tcp = true
      }

      getKefuInfo()
    })
    let sms = sessionStorage.getItem('smsCheck')
    if (sms == 0) {
      router.replace({ path: '/reg' })
    }
    const numberInput = name => {
      //只可输入数字
      state[name] = state[name].replace(/[^\d]/g, '')
    }
    const clearInput = name => {
      //清除input值
      state[name] = ''
      // 如果清除的是单位名称，同时清除错误消息
      if (name === 'enterName') {
        state.enterNameMessage = ''
      }
    }

    const checkEnterNameDuplicate = () => {
      if (!state.enterName.trim()) {
        state.enterNameMessage = ''
        return
      }

      checkEntername({
        enter_name: state.enterName.trim(),
      })
        .then(res => {
          if (res.ret == 1) {
            state.enterNameMessage = ''
          } else {
            state.enterNameMessage = '该单位已存在，不能重复申请，有疑问请联系平台客服'
          }
        })
        .catch(() => {
          state.enterNameMessage = '网络异常，请稍后再试'
        })
    }

    const getKefuInfo = () => {
      getKefu().then(res => {
        if (res.ret == 1) {
          state.kefuInfo = res.result.data[0]
        }
      })
    }

    const openKefu = () => {
      proxy.$refs.focus_tencent.open()
    }

    const closeKefu = () => {
      proxy.$refs.focus_tencent.close()
    }

    const selectTcp = () => {
      //勾选协议
      state.tcp = state.tcp ? false : true
    }

    const smsCallback = code => {
      //防刷回调
      sendCode(code)
    }
    const getCode = () => {
      //判断是否开启了防刷机制
      if (state.mobile == '') {
        OEUI.toast({
          text: '请填写手机号',
        })
        return
      }
      if (config.value.sms_check_imgcode2 == 1) {
        proxy.$refs.oeVerFicat.start()
      } else {
        sendCode()
      }
    }
    let time = null
    const sendCode = code => {
      //发送验证码
      OEUI.loading.show()
      codeSend({
        mobile: state.mobile,
        brushcode: code || '',
        type: '',
      }).then(res => {
        OEUI.loading.hide()
        if (res.ret == 1) {
          OEUI.toast({ text: '发送验证码成功' })
          state.codeStatus = false
          if (code) {
            proxy.$refs.oeVerFicat.success()
          }
          if (time != null) return
          time = setInterval(() => {
            state.codeSecond--
            if (state.codeSecond == 0) {
              clearInterval(time)
              time = null
              state.codeStatus = true
              state.codeSecond = 60
            }
          }, 1000)
        } else {
          if (code) {
            proxy.$refs.oeVerFicat.error()
          }
          OEUI.toast({
            text: res.msg || '系统繁忙，请稍后再试',
          })
        }
      })
    }

    const closeXieyi = () => {
      proxy.$refs.consentXieyiEl.close()
    }
    const consentXieyi = () => {
      sessionStorage.setItem('regTcpAgree', '1')
      state.tcp = true
      proxy.$refs.consentXieyiEl.close()
    }

    const sendLogin = () => {
      if (state.mobile == '') {
        OEUI.toast({
          text: '请填写手机号',
        })
        return
      }
      if (state.code == '') {
        OEUI.toast({
          text: '请填写验证码',
        })
        return
      }
      if (state.enterName.trim() == '') {
        OEUI.toast({
          text: '请填写单位名称',
        })
        return
      }
      if (state.enterNameMessage) {
        OEUI.toast({
          text: '单位名称存在问题，请检查',
        })
        return
      }
      if (state.unionName.trim() == '') {
        OEUI.toast({
          text: '请填写联络人',
        })
        return
      }
      if (state.tcp == 0) {
        proxy.$refs.consentXieyiEl.open()
        return
      }
      OEUI.loading.show('注册中...')
      regenteRprise({
        mobile: state.mobile,
        mobilecode: state.code,
        enter_name: state.enterName.trim(),
        unionname: state.unionName.trim(),
        weixinid: sessionStorage.getItem('openid') || '',
        tg_unionid: sessionStorage.getItem('tg_unionid') || '',
        tg_uid: sessionStorage.getItem('tg_uid') || '',
      }).then(res => {
        OEUI.loading.hide()
        if (res.ret == 1) {
          store.dispatch('setUserInfo', res.result)
          router.replace({
            path: '/unitApplyResult',
            query: {
              flag: 1,
            },
          })
          state.mobile = ''
          state.code = ''
          state.enterName = ''
          state.unionName = ''
          state.enterNameMessage = ''
        } else {
          OEUI.toast({
            text: res.msg || '注册失败，请检查',
          })
        }
      })
    }
    const goRegTcp = name => {
      getTcpHtml(name)
    }

    const copys = data => {
      let elInput = document.createElement('input')
      elInput.value = data
      document.body.appendChild(elInput)
      elInput.select()
      document.execCommand('Copy')
      OEUI.toast({
        text: '复制成功',
      })
      elInput.remove()
    }

    const callMobile = data => {
      if (!data) return
      const phoneNumber = data
      const link = document.createElement('a')
      link.href = `tel:${phoneNumber}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

    const xieyi_title = ref('兼职红娘注册协议')
    const getTcpHtml = (type = 'meipo_xieyi') => {
      xieyi_title.value = type == 'meipo_xieyi' ? '兼职红娘注册协议' : '个人信息保护政策'
      getXieyi({
        idmark: type,
      }).then(res => {
        if (res.ret == 1) {
          state.htmlTcp = setHtmlExp(res.result.content)
          nextTick(() => {
            proxy.$refs.tcpTips.open()
          })
        }
      })
    }
    const tcpAgree = () => {
      sessionStorage.setItem('regTcpAgree', '1')
      state.tcp = true
      proxy.$refs.tcpTips.close()
      if (state.mobile && state.code) {
        sendLogin()
      }
    }

    let hideshow = reactive({
      flag: true,
    })
    const back = () => {
      hideshow.flag = true
      if (window.history.length <= 1) {
        router.replace({ path: '/' })
      } else {
        router.back()
      }
    }

    const goPage = val => {
      hideshow.flag = true
      router.replace(val)
    }
    const docmHeight = ref(document.documentElement.clientHeight || document.body.clientHeight)

    const hanleOnresize = () => {
      let val = document.documentElement.clientHeight || document.body.clientHeight
      if (docmHeight.value > val) {
        //隐藏
        hideshow.flag = false
      } else {
        //显示
        hideshow.flag = true
      }
    }
    window.addEventListener('resize', hanleOnresize)
    onUnmounted(() => {
      window.removeEventListener('resize', hanleOnresize)
    })

    // 监听单位名称输入，清除错误消息
    watch(
      () => state.enterName,
      (newVal, oldVal) => {
        if (newVal !== oldVal && state.enterNameMessage) {
          state.enterNameMessage = ''
        }
      },
    )

    watch(
      () => config.value,
      newData => {
        if (newData) {
          config.value = newData
        }
      },
      { immediate: true, deep: true },
    )

    return {
      config,
      ...toRefs(state),
      xieyi_title,
      numberInput,
      clearInput,
      checkEnterNameDuplicate,
      selectTcp,
      getCode,
      smsCallback,
      sendLogin,
      consentXieyi,
      closeXieyi,
      goRegTcp,
      tcpAgree,
      back,
      goPage,
      hideshow,
      isWeiXin,
      copys,
      callMobile,
      getKefuInfo,
      openKefu,
      closeKefu,
    }
  },
}
</script>
<style lang="scss" scoped>
@import url('~@/oeui/icon/iconfont.css');
@import url('~@/assets/iconfont/iconfont.css');

.bg_main {
  background: $color_main;
}
.oe_login {
  position: relative;
  min-height: 100%;
  box-sizing: border-box;
  .back {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    position: absolute;
    width: 1.3333rem;
    height: 1.3333rem;
    color: #fff;
    left: 0;
    i {
      font-size: 0.5333rem;
    }
  }

  .content_box {
    padding: 0 0.64rem;
    height: calc(100vh - 4.1333rem);
    position: relative;
    z-index: 1;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 28px 28px 0px 0px;
  }

  .title {
    font-size: 0.8533rem;
    padding-top: 0.7467rem;
    color: $color_main;
    line-height: 1.0133rem;
    font-weight: 600;
  }
  .tips {
    font-family: PingFang SC, PingFang SC;
    color: #666;
    font-weight: normal;
    font-size: 0.3467rem;
  }

  .oe_input {
    background: #fff;
    position: relative;
    height: 1.28rem;
    line-height: 1.28rem;
    margin-top: 0.4267rem;
    border-radius: 0.64rem;

    input {
      width: 100%;
      height: 1.0667rem;
      box-sizing: border-box;
      padding: 0 0.5333rem;
      font-size: 0.4267rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;

      &::placeholder {
        color: #c3c3c5;
      }
    }

    .iconfont {
      color: #c9cdd4;
      position: absolute;
      right: 0;
      padding-left: 0.267rem;
      padding-right: 0.4rem;
      font-size: 0.56rem;
      line-height: 1.333rem;
      cursor: pointer;

      &.icon-yanjing {
        font-size: 0.613rem;

        &.current {
          color: $color_main;
        }
      }
    }

    .getcode {
      position: absolute;
      color: $color_main;
      font-size: 0.4267rem;
      cursor: pointer;
      right: 0.5333rem;
      font-weight: normal;
      font-family: PingFang SC, PingFang SC;

      &.current {
        color: #999999;
      }
    }
  }

  .btn_box {
    margin-top: 0.8533rem;

    .btn {
      width: 100%;
      text-align: center;
      background-color: $color_main;
      font-size: 0.427rem;
      line-height: 0.5867rem;
      border-radius: 1rem;
      color: #ffffff;
      padding: 0.2933rem 0;
      box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      cursor: pointer;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;
      opacity: 0.5;

      &.current {
        opacity: 1;
      }
    }
  }

  .oe_tcp {
    position: fixed;
    left: 0;
    width: 100%;
    bottom: 0.5333rem;
    font-size: 0.32rem;
    line-height: 0.4533rem;
    display: flex;
    justify-content: center;
    align-content: center;
    color: #999999;
    cursor: pointer;

    span.oeuifont {
      width: 0.48rem;
      height: 0.48rem;
      border-radius: 50%;
      box-sizing: border-box;
      border: 1px solid #e9e9e9;
      line-height: 0.48rem;
      font-size: 0.32rem;
      text-align: center;
      color: #ffffff;
      margin-right: 0.16rem;
      background: #ffffff;

      &.current {
        background: $color_main;
      }
    }

    i {
      color: $color_main;
    }
  }

  .more {
    margin-top: 0.4267rem;
    text-align: center;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: normal;
    cursor: pointer;
    color: $color_main;
    i {
      color: $color_main;
    }
  }
  .weixin {
    margin-top: 1.3333rem;
    p {
      flex: 1;
      height: 0.0267rem;
      background: linear-gradient(90deg, rgba(216, 216, 216, 1) 0%, #d8d8d8 50%, rgba(216, 216, 216, 0) 100%);
    }
    i {
      flex-shrink: 0;
      font-size: 0.64rem;
      color: #28c445;
      margin: 0 0.5333rem;
    }
  }

  .kf_box {
    margin-top: 0.64rem;
    img {
      width: 0.48rem;
      height: 0.48rem;
    }
    span {
      color: #999;
      font-size: 0.3733rem;
      margin-left: 0.1067rem;
    }
  }
}

.tcp_tips {
  .title {
    text-align: center;
    font-size: 0.48rem;
    font-weight: 600;
    padding-top: 0.67rem;
    padding-bottom: 0;
    color: #333333;
  }

  .content {
    font-size: 0.373rem;
    color: #333333;
    padding: 0.533rem 0.533rem 0;
    max-height: 7rem;
    overflow-x: hidden;
    overflow-y: scroll;

    span {
      display: inline-block;
    }
  }

  .btn {
    margin: 0.533rem auto 0.667rem;
    width: 80%;
    height: 1.2rem;
    line-height: 1.2rem;
    color: #ffffff;
    background: $color_main;
    border-radius: 1rem;
    text-align: center;
    font-size: 0.4rem;
    cursor: pointer;
  }
}
</style>
<style lang="scss" scoped>
.focus_tencent {
  border-radius: 0.64rem 0.64rem 0 0;
  padding: 0.64rem 0.8533rem 0.8533rem 0.8533rem;
  .title {
    text-align: center;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    padding-top: 0;
  }
  .tips {
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    color: #666;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    text-align: center;
    margin-top: 0.4267rem;

    .tips_title {
      display: block;
      width: 100rpx;
    }

    .c_btn {
      width: 1.28rem;
      height: 0.64rem;
      border-radius: 0.16rem;
      border: 0.0267rem solid #7d68fe;
      background-color: rgba($color: #7d68fe, $alpha: 0.08);
      color: #7d68fe;
    }
  }
  .qr {
    box-sizing: border-box;
    width: 3.7333rem;
    height: 3.7333rem;
    background: fff;
    margin: 0.4267rem auto;
    border: 0.16rem solid #fff;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .tap {
    text-align: center;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    color: #666;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
  }
  .close {
    position: absolute;
    font-size: 0.7467rem;
    color: #fff;
    left: 50%;
    transform: translateX(-50%);
    bottom: -1.3333rem;
  }
}
</style>

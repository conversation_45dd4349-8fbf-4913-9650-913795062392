<template>
  <div class="oe_login apply">
    <div class="back" @click="back">
      <i class="iconfont icon-zuo"></i>
    </div>
    <div style="height: 2rem"></div>
    <div class="content_box">
      <!-- 标题输入框 -->
      <div class="field_item">
        <div class="field_label">
          标题
          <span class="required">*</span>
        </div>
        <div class="oe_input">
          <input type="text" placeholder="请输入" v-model="formData.title" maxlength="30" />
          <span class="iconfont icon-guanbi" v-if="formData.title" @click="clearInput('title')"></span>
        </div>
      </div>

      <!-- 活动地址输入框 -->
      <div class="field_item">
        <div class="field_label">
          活动地址
          <span class="required">*</span>
        </div>
        <div class="oe_input">
          <input type="text" placeholder="填写地址" v-model="formData.address" />
          <span class="iconfont icon-guanbi" v-if="formData.address" @click="clearInput('address')"></span>
        </div>
      </div>

      <!-- 活动时间输入框 -->
      <div class="field_item">
        <div class="field_label">
          活动时间
          <span class="required">*</span>
        </div>
        <div class="oe_input flex flex_ac" @click="openCalendar">
          <input type="text" placeholder="请选择活动时间" v-model="formData.starttime_text" readonly />
          <span class="iconfont icon-youjiantou-01"></span>
        </div>
      </div>

      <datetime ref="datetimePicker" title="请选择活动时间" :current="selectedDateTime" @confirm="onDateTimeConfirm" @cancel="onDateTimeCancel" />

      <!-- 活动内容多行输入框 -->
      <div class="field_item">
        <div class="field_label">
          大概描述一下活动内容
          <span class="required">*</span>
        </div>
        <div class="textarea_box">
          <textarea v-model="formData.content" placeholder="请输入" maxlength="1000"></textarea>
          <span class="nums">{{ formData.content.length }}/1000</span>
        </div>
      </div>

      <!-- 发布按钮 -->
      <div class="btn_box">
        <div class="btn" @click="submitForm" :class="isFormValid ? 'current' : ''">发布</div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, computed, getCurrentInstance, ref } from 'vue'
import { useRouter } from 'vue-router'
import datetime from '@/oeui/datetime-picker.vue'
import { saveApply, editApply } from '@/api/party.js'

export default {
  name: 'ActivityApply',
  components: {
    datetime,
  },
  setup() {
    const { proxy } = getCurrentInstance()
    const OEUI = proxy.OEUI
    const router = useRouter()

    // 基础选择器的选中时间
    const selectedDateTime = ref(new Date())

    // 表单数据
    const formData = reactive({
      title: '',
      address: '',
      starttime: '',
      starttime_text: '',
      content: '',
    })

    // 计算表单是否有效（所有字段都必填）
    const isFormValid = computed(() => {
      return formData.title.trim() && formData.address.trim() && formData.starttime && formData.content.trim()
    })

    // 清空输入框
    const clearInput = field => {
      formData[field] = ''
    }

    // 返回上一页
    const back = () => {
      router.back()
    }

    // 打开日历
    const openCalendar = () => {
      proxy.$refs.datetimePicker.open()
    }

    // 提交表单
    const submitForm = () => {
      // 验证必填字段
      if (!formData.title.trim()) {
        return OEUI.toast({
          text: '请输入标题',
        })
      }

      if (!formData.address.trim()) {
        return OEUI.toast({
          text: '请填写地址',
        })
      }

      if (!formData.starttime) {
        return OEUI.toast({
          text: '请设置时间',
        })
      }

      if (!formData.content.trim()) {
        return OEUI.toast({
          text: '请填写活动内容描述',
        })
      }

      // 输出表单数据（接口由您自己补充）
      console.log('提交的表单数据：', {
        title: formData.title.trim(),
        address: formData.address.trim(),
        starttime: formData.starttime,
        content: formData.content.trim(),
      })

      saveApply(formData).then(res => {
        if (res.ret == 1) {
          OEUI.toast({ text: '发布成功' })
          //   router.back()
        } else {
          OEUI.toast({ text: res.msg || '发布失败' })
        }
      })
    }

    // 格式化日期时间显示
    const formatDateTime = date => {
      if (!date) return ''

      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hour = date.getHours().toString().padStart(2, '0')
      const minute = date.getMinutes().toString().padStart(2, '0')

      return `${year}-${month}-${day} ${hour}:${minute}`
    }

    // 基础选择器确认回调
    const onDateTimeConfirm = date => {
      selectedDateTime.value = date
      formData.starttime = Math.round(date.getTime() / 1000)
      formData.starttime_text = formatDateTime(date)
      console.log('选择的日期时间:', formatDateTime(date))
    }

    // 基础选择器取消回调
    const onDateTimeCancel = () => {
      console.log('取消选择日期时间')
    }

    return {
      selectedDateTime,
      formData,
      isFormValid,
      clearInput,
      back,
      submitForm,
      openCalendar,
      onDateTimeConfirm,
      onDateTimeCancel,
    }
  },
}
</script>

<style lang="scss" scoped>
.oe_login {
  .back {
    position: absolute;
    left: 0.32rem;
    top: 0.64rem;
    width: 1.1733rem;
    height: 1.1733rem;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    cursor: pointer;

    i {
      font-size: 0.64rem;
      color: #333;
    }
  }

  .content_box {
    padding: 0 0.64rem;

    .title {
      font-size: 0.64rem;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 0.8533rem;
      text-align: center;
      margin-bottom: 0.2667rem;
    }

    .tips {
      font-size: 0.3733rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;
      color: #666;
      line-height: 0.5333rem;
      text-align: center;
      margin-bottom: 0.8533rem;
    }
  }

  .field_item {
    margin-top: 0.4267rem;

    .field_label {
      font-size: 0.4267rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;
      color: #333;
      line-height: 0.5867rem;
      margin-bottom: 0.2667rem;

      .required {
        color: #ff6666;
      }
    }
  }

  .oe_input {
    background: #fff;
    position: relative;
    height: 1.12rem;
    line-height: 1.12rem;
    border-radius: 0.16rem;

    input {
      width: 100%;
      height: 1.12rem;
      box-sizing: border-box;
      padding: 0 0.5333rem;
      font-size: 0.4267rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;

      &::placeholder {
        color: #999;
      }
    }

    .iconfont {
      color: #c9cdd4;
      position: absolute;
      right: 0;
      padding-left: 0.267rem;
      padding-right: 0.4rem;
      font-size: 0.56rem;
      line-height: 1.333rem;
      cursor: pointer;
    }
  }

  .textarea_box {
    background: #fff;
    border-radius: 0.16rem;
    padding: 0.32rem;
    box-sizing: border-box;
    position: relative;
    height: 3.2rem;

    textarea {
      resize: none;
      border: none;
      width: 100%;
      height: 100%;
      background: none;
      font-size: 0.4267rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;

      &::placeholder {
        color: #999;
      }
    }

    .nums {
      position: absolute;
      font-size: 0.32rem;
      font-weight: normal;
      color: #c5c3c7;
      line-height: 0.4533rem;
      right: 0.4rem;
      bottom: 0.32rem;
    }
  }

  .btn_box {
    margin-top: 0.8533rem;

    .btn {
      width: 100%;
      text-align: center;
      background-color: #7d68fe;
      font-size: 0.427rem;
      line-height: 0.5867rem;
      border-radius: 1rem;
      color: #ffffff;
      padding: 0.2933rem 0;
      box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      cursor: pointer;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;
      opacity: 0.5;

      &.current {
        background-color: #7d68fe;
        color: #ffffff;
        opacity: 1;
      }
    }
  }

  // 日历弹窗样式
  .calendar_box {
    padding: 0;
    height: 500px;
    overflow: hidden;

    .calendar_header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.4rem 0.5333rem;
      border-bottom: 1px solid #f0f0f0;
      background: #fff;

      .calendar_title {
        font-size: 0.4267rem;
        font-weight: 500;
        color: #333;
      }

      .calendar_close {
        font-size: 0.64rem;
        color: #999;
        cursor: pointer;
        width: 0.8rem;
        height: 0.8rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .calendar_wrapper {
      height: calc(500px - 60px);
      overflow: hidden;

      // 重写日历组件样式以适配弹窗
      :deep(.hash-calendar) {
        position: relative !important;
        z-index: 1 !important;
        background: #fff !important;

        .calendar_body {
          height: auto !important;
        }

        .calendar_group {
          height: auto !important;
        }

        .calendar_group_li {
          height: auto !important;
        }
      }
    }
  }
}
</style>

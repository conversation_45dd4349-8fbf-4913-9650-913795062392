<template>
  <div class="oe_login">
    <div class="back" @click="back">
      <i class="iconfont icon-zuo"></i>
    </div>
    <div style="height: 4.1333rem"></div>
    <div class="content_box">
      <p class="title">活动申请发布</p>
      <p class="tips">请填写完整的活动信息</p>

      <!-- 标题输入框 -->
      <div class="oe_input">
        <input type="text" placeholder="请输入活动标题" v-model="formData.title" maxlength="50" />
        <span class="iconfont icon-guanbi" v-if="formData.title" @click="clearInput('title')"></span>
      </div>

      <!-- 活动地址输入框 -->
      <div class="oe_input">
        <input type="text" placeholder="请输入活动地址" v-model="formData.address" maxlength="100" />
        <span class="iconfont icon-guanbi" v-if="formData.address" @click="clearInput('address')"></span>
      </div>

      <!-- 活动时间输入框 -->
      <div class="oe_input">
        <input type="text" placeholder="请输入活动时间" v-model="formData.starttime" maxlength="50" />
        <span class="iconfont icon-guanbi" v-if="formData.starttime" @click="clearInput('starttime')"></span>
      </div>

      <!-- 活动内容多行输入框 -->
      <div class="content_input">
        <p class="label">活动内容</p>
        <div class="textarea_box">
          <textarea v-model="formData.content" placeholder="请输入活动内容描述" maxlength="500"></textarea>
          <span class="nums">{{ formData.content.length }}/500</span>
        </div>
      </div>

      <!-- 发布按钮 -->
      <div class="btn_box">
        <div class="btn" @click="submitForm" :class="isFormValid ? 'current' : ''">发布</div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, computed, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'ActivityApply',
  setup() {
    const { proxy } = getCurrentInstance()
    const OEUI = proxy.OEUI
    const router = useRouter()

    // 表单数据
    const formData = reactive({
      title: '',
      address: '',
      starttime: '',
      content: '',
    })

    // 计算表单是否有效（所有字段都必填）
    const isFormValid = computed(() => {
      return formData.title.trim() && formData.address.trim() && formData.starttime.trim() && formData.content.trim()
    })

    // 清空输入框
    const clearInput = field => {
      formData[field] = ''
    }

    // 返回上一页
    const back = () => {
      router.back()
    }

    // 提交表单
    const submitForm = () => {
      // 验证必填字段
      if (!formData.title.trim()) {
        return OEUI.toast({
          text: '请填写活动标题',
        })
      }

      if (!formData.address.trim()) {
        return OEUI.toast({
          text: '请填写活动地址',
        })
      }

      if (!formData.starttime.trim()) {
        return OEUI.toast({
          text: '请填写活动时间',
        })
      }

      if (!formData.content.trim()) {
        return OEUI.toast({
          text: '请填写活动内容',
        })
      }

      // 输出表单数据（接口由您自己补充）
      console.log('提交的表单数据：', {
        title: formData.title.trim(),
        address: formData.address.trim(),
        starttime: formData.starttime.trim(),
        content: formData.content.trim(),
      })

      // 这里可以调用您的接口
      // 示例：
      // submitActivity(formData).then(res => {
      //   if (res.ret == 1) {
      //     OEUI.toast({ text: '发布成功' })
      //     router.back()
      //   } else {
      //     OEUI.toast({ text: res.msg || '发布失败' })
      //   }
      // })

      OEUI.toast({
        text: '表单数据已输出到控制台，请查看并补充接口',
      })
    }

    return {
      formData,
      isFormValid,
      clearInput,
      back,
      submitForm,
    }
  },
}
</script>

<style lang="scss" scoped>
.oe_login {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(180deg, #7d68fe 0%, #9c8cff 100%);

  .back {
    position: absolute;
    left: 0.4267rem;
    top: 1.2rem;
    width: 1.1733rem;
    height: 1.1733rem;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    cursor: pointer;

    i {
      font-size: 0.64rem;
      color: #fff;
    }
  }

  .content_box {
    padding: 0 0.8533rem;

    .title {
      font-size: 0.64rem;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 0.8533rem;
      text-align: center;
      margin-bottom: 0.2667rem;
    }

    .tips {
      font-size: 0.3733rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;
      color: rgba(255, 255, 255, 0.8);
      line-height: 0.5333rem;
      text-align: center;
      margin-bottom: 0.8533rem;
    }
  }

  .oe_input {
    background: #fff;
    position: relative;
    height: 1.28rem;
    line-height: 1.28rem;
    margin-top: 0.4267rem;
    border-radius: 0.64rem;

    input {
      width: 100%;
      height: 1.0667rem;
      box-sizing: border-box;
      padding: 0 0.5333rem;
      font-size: 0.4267rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;

      &::placeholder {
        color: #c3c3c5;
      }
    }

    .iconfont {
      color: #c9cdd4;
      position: absolute;
      right: 0;
      padding-left: 0.267rem;
      padding-right: 0.4rem;
      font-size: 0.56rem;
      line-height: 1.333rem;
      cursor: pointer;
    }
  }

  .content_input {
    margin-top: 0.4267rem;

    .label {
      font-size: 0.4267rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;
      color: #ffffff;
      line-height: 0.5867rem;
      margin-bottom: 0.2667rem;
    }

    .textarea_box {
      background: #fff;
      border-radius: 0.32rem;
      padding: 0.32rem;
      box-sizing: border-box;
      position: relative;
      height: 3.2rem;

      textarea {
        resize: none;
        border: none;
        width: 100%;
        height: 100%;
        background: none;
        font-size: 0.4267rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: normal;

        &::placeholder {
          color: #c3c3c5;
        }
      }

      .nums {
        position: absolute;
        font-size: 0.32rem;
        font-weight: normal;
        color: #c5c3c7;
        line-height: 0.4533rem;
        right: 0.4rem;
        bottom: 0.32rem;
      }
    }
  }

  .btn_box {
    margin-top: 0.8533rem;

    .btn {
      width: 100%;
      text-align: center;
      background-color: rgba(255, 255, 255, 0.3);
      font-size: 0.427rem;
      line-height: 0.5867rem;
      border-radius: 1rem;
      color: #ffffff;
      padding: 0.2933rem 0;
      box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      cursor: pointer;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: normal;
      opacity: 0.5;

      &.current {
        opacity: 1;
        background-color: #ffffff;
        color: #7d68fe;
      }
    }
  }
}
</style>

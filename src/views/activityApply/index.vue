<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">活动管理</div>
    </div>

    <!-- 列表容器 -->
    <oeui-list :top="1.1733" @refresh="refresh" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <!-- 提示信息 -->
      <div class="tips_box">
        <p class="tips_text">在这里申请发布活动，工作人员收到后会第一时间联系并协助您发布确定活动内容，完成活动发布。</p>
      </div>

      <!-- 活动列表 -->
      <div class="list">
        <template v-if="list.length">
          <activity_item v-for="item in list" :key="item.id" :item="item" @edit="editActivity" />
        </template>
        <template v-else-if="listStatus != 'no_data'">
          <div class="list_skelecton flex flex_ac" v-for="item in 3" :key="item">
            <div class="head"></div>
            <div class="flex_1">
              <p></p>
              <div class="flex flex_ac">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </template>
      </div>
    </oeui-list>
  </div>
</template>

<script>
import { ref, getCurrentInstance, onActivated, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import oeuiList from '@/oeui/list.vue'
import activity_item from './components/activity_item.vue'
import { getActivityApply } from '@/api/party.js'

export default {
  name: 'ActivityApplyIndex',
  components: {
    oeuiList,
    activity_item,
  },
  setup() {
    const { proxy } = getCurrentInstance()
    const OEUI = proxy.OEUI
    const router = useRouter()
    const route = useRoute()

    // 列表数据
    const list = ref([])
    const page = ref(1)
    const pageCount = ref('0')
    const listStatus = ref('loading')

    // 获取列表数据
    const getList = (flag, callback) => {
      if (page.value == 0) return
      getActivityApply({
        page: page.value,
      }).then(res => {
        if (res.ret == 1) {
          res.result.data = res.result.data ? res.result.data : []
          if (flag) list.value = res.result.data
          else list.value = [...list.value, ...res.result.data]
          pageCount.value = res.result.total
          page.value = res.result.nextpage
          if (res.result.pagecount == 0) {
            listStatus.value = 'no_data'
          } else if (res.result.nextpage == 0) {
            listStatus.value = 'no_result'
          } else {
            listStatus.value = 'loading'
          }
          if (callback) callback()
        } else {
          OEUI.toast({
            text: res.msg || '系统繁忙，请稍后再试',
          })
        }
      })
    }

    // 刷新
    const refresh = done => {
      page.value = 1
      list.value = []
      listStatus.value = 'loading'
      getList(true, done)
    }

    // 加载更多
    const scrollBottom = done => {
      page.value++
      getList(false, done)
    }

    // 跳转到发布申请页面
    const goApply = () => {
      router.push('/activity/apply')
    }

    // 编辑活动
    const editActivity = item => {
      router.push(`/activity/apply?id=${item.id}`)
    }

    // 监听路由查询参数变化，自动刷新列表
    watch(
      () => route.query.refresh,
      newVal => {
        if (newVal) {
          // 重置分页并刷新数据
          page.value = 1
          list.value = []
          listStatus.value = 'loading'
          getList(true)

          // 清除URL中的刷新参数，避免重复刷新
          router.replace({
            path: route.path,
            query: { ...route.query, refresh: undefined },
          })
        }
      },
      { immediate: false },
    )

    // 页面激活时获取数据
    onActivated(() => {
      if (list.value.length === 0) {
        getList(true)
      }
    })

    // 初始化数据
    getList(true)

    return {
      list,
      listStatus,
      refresh,
      scrollBottom,
      goApply,
      editActivity,
    }
  },
}
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}

// 提示信息样式
.tips_box {
  background: #fff;
  border-radius: 0.32rem;
  margin: 0.32rem;
  padding: 0.32rem;

  .tips_text {
    font-size: 0.3733rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    color: #3d3d3d;
    line-height: 0.5333rem;
    margin: 0;
  }
}

// 列表样式
.list {
  padding: 0 0.32rem;
}

// 骨架屏样式
.activity_skeleton {
  background: #fff;
  border-radius: 0.32rem;
  margin-bottom: 0.32rem;
  padding: 0.4267rem;

  .skeleton_content {
    .skeleton_title {
      width: 70%;
      height: 0.5867rem;
      background: #f1f1f1;
      border-radius: 0.1333rem;
      margin-bottom: 0.32rem;
    }

    .skeleton_address {
      width: 85%;
      height: 0.4533rem;
      background: #f1f1f1;
      border-radius: 0.1333rem;
      margin-bottom: 0.2667rem;
    }

    .skeleton_time {
      width: 60%;
      height: 0.4533rem;
      background: #f1f1f1;
      border-radius: 0.1333rem;
      margin-bottom: 0.32rem;
    }

    .skeleton_status {
      width: 40%;
      height: 0.4533rem;
      background: #f1f1f1;
      border-radius: 0.1333rem;
    }
  }
}
</style>

<template>
  <div class="activity_item">
    <div class="item_title">
      {{ item.title }}
    </div>

    <div class="item_info">
      <span class="info_label">活动地址：</span>
      <span class="info_value">{{ item.address }}</span>
    </div>

    <div class="item_info">
      <span class="info_label">活动时间：</span>
      <span class="info_value">{{ item.starttime_t }}</span>
    </div>

    <div class="item_footer">
      <div class="status_box">
        <span
          class="status_text"
          :class="{
            status_pending: item.flag == 0,
            status_approved: item.flag == 1,
            status_rejected: item.flag == 2,
          }"
        >
          {{ item.flag == 0 ? '待处理' : item.flag == 1 ? '已处理' : '已拒绝' }}
        </span>
      </div>
      <div class="action_box">
        <img class="edit" src="@/assets/images/edit_to.png" />
        <span class="edit_btn" @click="handleEdit">编辑</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ActivityItem',
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  emits: ['edit'],
  setup(props, { emit }) {
    const handleEdit = () => {
      emit('edit', props.item)
    }

    return {
      handleEdit,
    }
  },
}
</script>

<style lang="scss" scoped>
.activity_item {
  background: #fff;
  border-radius: 0.32rem;
  padding: 0.32rem 0.4267rem;
  margin-bottom: 0.32rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .item_title {
    font-size: 0.4267rem;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    margin-bottom: 0.32rem;
    word-break: break-all;
  }

  .item_info {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.2667rem;

    .info_label {
      font-size: 0.3733rem;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: normal;
      color: #666;
      line-height: 0.5333rem;
      flex-shrink: 0;
    }

    .info_value {
      font-size: 0.3733rem;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: normal;
      color: #666;
      line-height: 0.5333rem;
      flex: 1;
      word-break: break-all;
    }
  }

  .item_footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.32rem;
    padding-top: 0.32rem;
    border-top: 0.0267rem solid #f2f4f5;

    .status_box {
      .status_text {
        font-size: 0.3733rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        line-height: 0.5333rem;

        &.status_pending {
          color: #3d3d3d;
        }

        &.status_approved {
          color: #67c23a;
        }

        &.status_rejected {
          color: hsl(0, 87%, 69%);
        }
      }
    }

    .action_box {
      display: flex;
      align-items: center;

      img {
        width: 0.64rem;
        height: 0.64rem;
        display: block;
        margin-right: 0.1067rem;
      }

      .edit_btn {
        font-size: 0.3733rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        color: #7d68fe;
        line-height: 0.5333rem;
        border-radius: 0.32rem;
        cursor: pointer;
        transition: all 0.3s;
      }
    }
  }
}
</style>

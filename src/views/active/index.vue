<!-- @format -->

<template>
  <div v-if="flag">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">开通角色</div>
    </div>
    <div class="h44"></div>
  </div>
  <div class="nav_box flex flex_ac flex_el">
    <div @click="changActive(1)" class="flex_dc flex_v w33per item" :class="active == 1 ? 'current' : ''">
      <div class="bg">
        <span>
          <img v-if="active == 1" src="@/assets/images/active1s.png" />
          <img v-else src="@/assets/images/active1.png" />
        </span>
        <p>推广红娘</p>
      </div>
    </div>
    <div v-if="config.union_type2 == 1 && config.union_partner == 2" @click="changActive(2)" class="flex_dc flex_v w33per item" :class="active == 2 ? 'current' : ''">
      <div class="bg">
        <span>
          <img v-if="active == 2" src="@/assets/images/active2s.png" />
          <img v-else src="@/assets/images/active2.png" />
        </span>
        <p>红娘团长</p>
      </div>
    </div>
    <div v-if="config.union_type3 == 1" @click="changActive(3)" class="flex_dc flex_v w33per item" :class="active == 3 ? 'current' : ''">
      <div class="bg">
        <span>
          <img v-if="active == 3" src="@/assets/images/active3s.png" />
          <img v-else src="@/assets/images/active3.png" />
        </span>
        <p>服务红娘</p>
      </div>
    </div>
  </div>
  <div class="content">
    <union1 :type="flag" v-if="active == 1" />
    <union2 :type="flag" v-if="active == 2 && config.union_type2 == 1 && config.union_partner == 2" />
    <union3 :type="flag" v-if="active == 3 && config.union_type3 == 1" />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import union1 from './union1.vue'
import union2 from './union2.vue'
import union3 from './union3.vue'

const store = useStore()
const route = useRoute()

const unionInfo = computed(() => store.state.unionInfo)
const config = computed(() => store.state.config)

const active = ref(1)

if (unionInfo.value.level1_flag != 1) {
  active.value = 1
} else if (unionInfo.value.level2_flag != 1 && config.value.union_type2 == 1 && config.value.union_partner == 2) {
  active.value = 2
} else if (unionInfo.value.level3_flag != 1 && config.value.union_type3 == 1) {
  active.value = 3
}

const flag = ref(true)
if (route.query.flag) {
  flag.value = false
}

const changActive = val => {
  active.value = val
  store.dispatch('getUserInfo')
}

watch(
  () => config.value,
  newInfo => {
    if (newInfo) {
      config.value = newInfo
    }
  },
  { immediate: true, deep: true },
)
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.nav_box {
  font-family: PingFang SC, PingFang SC;
  padding: 0.64rem 0.8533rem;
  padding-bottom: 0;
  .item {
    opacity: 0.66;
    &.current {
      opacity: 1;
      box-sizing: border-box;
      background: url('~@/assets/images/active_bg.png') no-repeat;
      background-size: cover;
      .bg {
        span {
          background: #fff;
        }
        p {
          color: $color_main;
        }
      }
    }
    .bg {
      padding: 0.32rem;
      padding-bottom: 0.16rem;
      span {
        width: 1.3867rem;
        height: 1.3867rem;
        border-radius: 50%;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      p {
        font-size: 0.32rem;
        font-weight: normal;
        color: #3d3d3d;
        text-align: center;
        line-height: 0.4533rem;
      }
    }
  }
}
.content {
  padding: 0 0.4267rem;
  box-sizing: border-box;
}
.w33per{
  width: 33%;
}
</style>

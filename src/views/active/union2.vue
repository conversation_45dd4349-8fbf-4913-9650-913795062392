<!-- @format -->

<template>
  <div class="box">
    <div class="head">
      <img v-if="config.union2_img" v-lazy="config.union2_img" alt="" />
      <img v-else src="@/assets/images/active2_bg.png" alt="" />
    </div>
    <div class="tip" v-if="config.union2_intro" v-html="filterHtml(config.union2_intro)"></div>
    <div class="tip" v-else>性质是和平台合作推广运营的一种角色身份，可免费申请成为平台的推广红娘团长（简称红娘团长），红娘团长可利用自身资源或个人业务能力与平台合作推广或大量发展推广红娘，从而为平台快速稳定、可持续性拓展用户和变现，除了自身推广的分成外，享受整个团队的业绩分成，系统根据团队业绩或发展用户数量将红娘团长划分为多个等级，不同等级享有不同的分佣标准。 红娘团长拥有独立的管理中心，团队成员、业绩明细、分佣明细、有效用户数据均清晰明了，分佣提成可随时申请提现。</div>
  </div>
  <div class="footer">
    <div class="btn flex_dc">
      <template v-if="unionInfo.level2_flag == 1">
        <span class="audit" v-if="type">红娘团长({{ unionInfo.level2_name }})</span>
        <span v-else @click="$router.replace('/home')">已是红娘团长,进入工作台</span>
      </template>

      <span class="audit" v-else-if="unionInfo.level2_flag == 2">申请已提交,等待审核</span>
      <span @click="btn" v-else>申请成为红娘团长</span>
    </div>
    <div class="flex_dc" style="margin-top: 0.4rem; line-height: 0.5333rem">
      <span @click="is_check = !is_check" class="flex_dc flex_s color_main" style="width: 0.5333rem; height: 0.5333rem">
        <i class="iconfont icon-dui pr" style="font-size: 0.3733rem; top: 0.0267rem" v-if="is_check"></i>
        <i v-else class="iconfont icon-rediobtn_nor" style="font-size: 0.5333rem"></i>
      </span>
      <div style="line-height: 0.5333rem; font-size: 0.32rem">
        请阅读并同意
        <span @click="getTcpHtml" class="color_main" style="text-decoration: underline">《红娘角色升级退款协议》</span>
      </div>
    </div>
  </div>
  <oe_popup ref="selectGrade" width="100%" background="#F2F4F5">
    <div class="selectGrade">
      <div class="nav flex_dc">
        <span @click="proxy.$refs.selectGrade.close()" class="back flex_dc">
          <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
        </span>
        <div class="title flex_dc">选择开通级别</div>
      </div>
      <div class="h44"></div>
      <div class="list">
        <template v-for="item in list" :key="item.grid">
          <div class="item bg_f">
            <div class="title flex flex_ac flex_jsb">
              <div class="flex flex_ac">
                <span class="group" :class="'group' + item.grid">
                  <i>
                    <img :src="getPgroupUrl('tgroup', item.grid)" alt="" />
                  </i>
                  <em>{{ item.asname }}</em>
                </span>
              </div>
              <span @click="openUnion(item.grid)" v-if="Number(item.price) > 0" class="goup flex flex_ac">
                ¥{{ Number(item.price) }}立即开通
                <i class="iconfont icon-youjiantou-01"></i>
              </span>
            </div>
            <div class="detail flex flex_v">
              <div class="flex flex_ac flex_jsb">
                <div class="flex flex_ac">
                  <span class="icon">
                    <img src="@/assets/images/level_tips1.png" alt="" />
                  </span>
                  <p class="name">用户注册奖励</p>
                </div>
                <div class="price flex flex_ac">
                  <span class="flex flex_ac" style="margin-right: 0.1333rem">
                    <i class="iconfont icon-nanbiao-01"></i>
                    {{ Number(item.user_money1) }}元/人
                  </span>
                  <span class="flex flex_ac">
                    <i class="iconfont icon-nvbiao-01"></i>
                    {{ Number(item.user_money2) }}元/人
                  </span>
                </div>
              </div>
              <div class="flex flex_ac flex_jsb">
                <div class="flex flex_ac">
                  <span class="icon" style="margin-left: -0.0533rem">
                    <img src="@/assets/images/level_tips2.png" alt="" />
                  </span>
                  <p class="name">用户消费分成</p>
                </div>
                <div class="rwfalg flex flex_ac">
                  <span v-if="item.rwflag == 1">有分成</span>
                  <span v-else class="fail">无分成</span>
                  <span @click="showLevelDetail(item.grid)" class="flex flex_ac more">
                    查看标准
                    <i class="iconfont icon-youjiantou-01"></i>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </oe_popup>

  <oe_popup ref="level_dialog" mode="bottom" roundStyle=".64rem" round="true">
    <div class="level_dialog">
      <span class="close iconfont icon-cha" @click="proxy.$refs.level_dialog.close()"></span>
      <p class="title">消费分成标准</p>
      <div class="item name">
        <p class="ws">用户消费项目</p>
        <div>
          <span>推广奖励(%)</span>
        </div>
      </div>
      <div class="list" v-if="grade.length">
        <template v-for="item in grade" :key="item.idmark">
          <template v-if="item.idmark == 'viptc' || item.idmark == 'appwxtc' || item.idmark == 'aftertc' || item.idmark == 'note' || item.idmark == 'mentor'">
            <p class="type">{{ item.title }}</p>
            <div class="item" v-for="val in item.data" :key="val.idmark">
              <p class="ws">{{ val.title }}</p>
              <div>
                <span>{{ val.rw || 0 }}%</span>
              </div>
            </div>
          </template>
          <div class="item" v-else>
            <p class="ws">{{ item.title }}</p>
            <div>
              <span>{{ item.rw || 0 }}%</span>
            </div>
          </div>
        </template>
      </div>
    </div>
  </oe_popup>

  <oe_popup ref="result_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">等待审核</p>
      <p class="tip">尊敬的推广用户，系统已收到您的申请信息，我们将尽快安排专人审核，请耐心等待审核结果！</p>
      <!--<p class="tips">未通过审核前，您将以推广红娘【默认等级】身份使用系统，该等级无法享有推广拉新或用户消费分成。</p>-->
      <div @click="awaitAudit" class="event">确认</div>
    </div>
  </oe_popup>

  <oe_popup ref="success_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="success_dialog">
      <div class="tips_img">
        <img src="@/assets/images/chat_tips.png" alt="" />
      </div>
      <p class="tips">恭喜你，已成功开通【{{ upgrade_name }}红娘】身份</p>
      <div @click="confirmGrade" class="event">确定</div>
    </div>
  </oe_popup>

  <oe_popup ref="teamresult_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">温馨提示</p>
      <p class="tip">您已加入他人团队,若要申请红娘团长身份,请先退出当前红娘团队。</p>
      <div @click="proxy.$refs.teamresult_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>
  <oe_popup ref="tcpTips" mode="center" width="80%" :round="true" :maskClose="false">
    <div class="tcp_tips">
      <h3 class="title">红娘角色升级退款协议</h3>
      <div class="content" v-html="htmlTcp"></div>
      <div class="btn" @click="tcpAgree">同意并遵守协议</div>
    </div>
  </oe_popup>
</template>

<script setup>
import { ref, getCurrentInstance, computed, watch, nextTick } from 'vue'
import oe_popup from '@/oeui/popup.vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { initUnion2, openUnion2, checkOpen2 } from '@/api/active.js'
import { getLevel2, getLevel2Detail } from '@/api/level.js'
import { getWeinxinPay, wechatPay } from '@/utils/jssdk.js'
import { setHtmlExp } from '@/utils/main'
import { getXieyi } from '@/api/login.js'
defineProps({
  type: {
    type: Boolean,
  },
})

const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const router = useRouter()
const unionInfo = computed(() => store.state.unionInfo)
const config = computed(() => store.state.config)
const state = ref({})
const level1_flag = ref(null)
const paynum = ref(null)

const is_check = ref(false)
const htmlTcp = ref('')
const getTcpHtml = () => {
  getXieyi({
    idmark: 'meipotk_xieyi',
  }).then(res => {
    if (res.ret == 1) {
      htmlTcp.value = setHtmlExp(res.result.content)
      nextTick(() => {
        proxy.$refs.tcpTips.open()
      })
    }
  })
}
const tcpAgree = () => {
  is_check.value = true
  proxy.$refs.tcpTips.close()
}

const filterHtml = str => {
  return str.replace('\n', '<br />')
}
const getPgroupUrl = (type, val) => {
  return require(`@/assets/images/grade/${type}${val}.png`)
}

const init = () => {
  initUnion2().then(res => {
    if (res.ret == 1) {
      state.value = res.result.group1
      level1_flag.value = res.result.level1_flag
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const list = ref([])
const getList = () => {
  getLevel2({
    type: 'open',
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      list.value = res.result.data
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

//查看分成
const grade = ref([])
const is_show = ref(true)
const showLevelDetail = id => {
  if (!is_show.value) return
  is_show.value = false
  getLevel2Detail({
    id,
  })
    .then(res => {
      if (res.ret == 1) {
        res.result.data.power_data = res.result.data.power_data || []
        grade.value = res.result.data.power_data
        proxy.$refs.level_dialog.open()
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试',
        })
      }
      setTimeout(() => {
        is_show.value = true
      }, 500)
    })
    .catch(() => {
      is_show.value = true
      OEUI.toast('系统繁忙,请稍后再试!')
    })
}

const btn = () => {
  if (!is_check.value) {
    return OEUI.toast('请勾选并阅读红娘角色开通/升级退款协议')
  }
  if (unionInfo.value.be_leaderid > 0) {
    return proxy.$refs.teamresult_dialog.open()
  }
  if (Number(state.value.price) > 0) {
    getList()
    proxy.$refs.selectGrade.open()
    return
  }
  openUnion(state.value.grid)
}

const pay_url = ref(null)
const is_open = ref(true)
const upgrade_name = ref('')
const openUnion = id => {
  if (!is_open.value) return
  is_open.value = false
  OEUI.loading.show()
  openUnion2({
    grid: id,
  })
    .then(res => {
      OEUI.loading.hide()
      if (res.ret == 1) {
        //成功

        store.dispatch('getUserInfo').then(res => {
          if (res.ret == 1) {
            upgrade_name.value = res.result.info.level2_name
            proxy.$refs.success_dialog.open()
          }
        })
      } else if (res.ret == 3) {
        //审核
        proxy.$refs.result_dialog.open()
      } else if (res.ret == 2) {
        let url = config.value.siteurl + 'index.php?m=wap&c=pay&paynum=' + res.result + '&forward=' + encodeURIComponent(config.value.siteurl + 'vue/union/#/active')
        if (url) {
          window.location.replace(url)
        }
      } else if (res.ret == 22) {
        paynum.value = res.result
        getWeinxinPay(res.result)
          .then(data => {
            if (data.ret == 1) {
              pay_url.value = config.value.siteurl + 'index.php?m=wap&c=pay&paynum=' + data.result.paynum + '&forward=' + encodeURIComponent(config.value.siteurl + 'vue/union/#/active')
              weixinPay(data.result.data)
            } else {
              OEUI.toast({
                text: data.msg || '下单失败，请联系客服!',
              })
            }
          })
          .catch(() => {
            OEUI.toast({
              text: '获取支付参数失败，请稍后再试',
            })
          })
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试',
        })
      }
      store.dispatch('getUserInfo')
      setTimeout(() => {
        is_open.value = true
      }, 500)
    })
    .catch(() => {
      OEUI.loading.hide()
      is_open.value = true
      OEUI.toast('系统繁忙,请稍后再试!')
    })
}

const weixinPay = data => {
  try {
    wechatPay(data, status => {
      if (status == 'success') {
        checkOpen2(paynum.value).then(data => {
          if (data.ret == 1) {
            store.dispatch('getUserInfo').then(res => {
              if (res.ret == 1) {
                upgrade_name.value = res.result.info.level2_name
                proxy.$refs.success_dialog.open()
              }
            })
          } else if (data.ret == 2) {
            store.dispatch('getUserInfo').then(() => {
              proxy.$refs.result_dialog.open()
            })
          } else {
            OEUI.toast({
              text: data.msg || '系统繁忙，请稍后再试',
            })
          }
        })
      } else if (status == 'cancel') {
        OEUI.toast({
          text: '取消支付成功!',
        })
      } else if (status == 'error') {
        OEUI.toast({
          text: '下单成功,即将跳转到收银台!',
        })
        setTimeout(() => {
          if (pay_url.value) window.location.replace(pay_url.value)
        }, 200)
      }
    })
  } catch (error) {
    OEUI.toast({
      text: '获取支付参数失败，请稍后再试!',
    })
  }
}

const confirmGrade = () => {
  router.replace('/home')
  proxy.$refs.success_dialog.close()
}

const awaitAudit = () => {
  proxy.$refs.selectGrade.close()
  proxy.$refs.result_dialog.close()
}

init()

watch(
  () => unionInfo.value,
  newInfo => {
    if (newInfo) {
      unionInfo.value = newInfo
    }
  },
  { immediate: true, deep: true },
)
</script>

<style lang="scss" scoped>
.box {
  background: #fff;
  border-radius: 0.64rem;
  padding: 0.4267rem;
  padding-bottom: 2.6667rem;
  margin-bottom: 1.8667rem;
  max-height: 48vh;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    display: none;
  }

  .head {
    margin-bottom: 0.5333rem;
    border-radius: 0.2667rem;
    overflow: hidden;
    width: 100%;
    img {
      width: 100%;
      object-fit: cover;
    }
  }
  .tip {
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #31293b;
    line-height: 0.64rem;
  }
}
.footer {
  position: fixed;
  width: 100%;
  bottom: 0.4rem;
  left: 0;

  .btn {
    span {
      font-size: 0.3733rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      color: #ffffff;
      line-height: 0.64rem;
      background: $color_main;
      padding: 0.2133rem 1.28rem;
      cursor: pointer;
      border-radius: 0.5867rem;
    }
    .audit {
      background: #ccc;
    }
  }
}
.selectGrade {
  width: 100%;
  font-family: PingFang SC, PingFang SC;
  background: url('~@/assets/images/bg_main.png') no-repeat;
  .nav {
    height: 1.1733rem;
    position: fixed;
    top: 0;
    width: 100vw;
    z-index: 100;
    font-family: PingFang SC-Regular, PingFang SC;
    span {
      width: 1.1733rem;
      height: 1.1733rem;
      i {
        font-size: 0.64rem;
        position: relative;
        top: 0.0267rem;
      }
    }
    .back {
      position: absolute;
      left: 0;
    }
    .title {
      font-size: 0.48rem;
      line-height: 0.64rem;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #2a2546;
    }
  }
  .list {
    overflow-y: scroll;
    height: calc(100vh - 1.28rem);
    padding: 0.1067rem 0.4267rem;
    box-sizing: border-box;
    .item {
      border-radius: 0.32rem;
      padding: 0.4267rem;
      margin-bottom: 0.32rem;
      border: 0.0267rem solid #ffffff;
      box-sizing: border-box;
      &.bg {
        background: #eeebff;
      }
      .title {
        .group {
          margin-left: 0.1067rem;
        }
        .people {
          font-size: 0.32rem;
          font-weight: normal;
          color: #666666;
          line-height: 0.4533rem;
        }
        .now {
          font-size: 0.3467rem;
          font-weight: normal;
          color: $color_main;
          line-height: 0.48rem;
        }
        .goup {
          background: $color_main;
          font-size: 0.32rem;
          line-height: 0.4267rem;
          font-weight: normal;
          color: #fff;
          line-height: 0.48rem;
          padding: 0.08rem 0.2133rem;
          border-radius: 0.8rem;

          i {
            margin-left: 0.1067rem;
          }
        }
      }
      .detail {
        margin-top: 0.4267rem;

        > div {
          .icon {
            width: 0.8533rem;
            height: 0.8533rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.08rem;
            position: relative;
            top: 0.0267rem;
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          .name {
            font-size: 0.3733rem;
            font-weight: 500;
            color: #31293b;
            line-height: 0.64rem;
          }
          .price {
            font-size: 0.32rem;
            font-weight: 500;
            color: #666666;
            line-height: 0.64rem;

            i {
              margin-right: 0.0533rem;
            }

            .icon-nanbiao-01 {
              color: #0570f1;
            }

            .icon-nvbiao-01 {
              color: #fe6897;
            }
          }
          .rwfalg {
            span {
              font-size: 0.32rem;
              font-weight: normal;
              color: $color_main;
              line-height: 0.64rem;
            }

            .fail {
              color: #ccc;
            }

            .more {
              color: #999999;
              margin-left: 0.5333rem;
            }
          }
        }
      }
    }
  }
}

.add_card {
  border-radius: 0.64rem 0.64rem 0 0;
  padding: 0.64rem 0.4267rem;
  .title {
    text-align: center;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
  }
  .content {
    margin-top: 0.64rem;
    margin-bottom: 0.64rem;
    text-align: center;
    input {
      width: 70%;
      text-align: center;
      border: 0.0267rem solid #ccc;
      border-radius: 0.5333rem;
      padding: 0.32rem 0;
    }
  }
  .event {
    text-align: center;
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin: 0 0.7733rem;
    padding: 0.24rem 0;
  }
  .close {
    position: absolute;
    font-size: 0.7467rem;
    color: #fff;
    left: 50%;
    transform: translateX(-50%);
    bottom: -1.3333rem;
  }
}

.level_dialog {
  padding-bottom: 0.32rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;
  max-height: 60vh;
  overflow-y: auto;
  .close {
    position: absolute;
    right: 0.5333rem;
    top: 0.6133rem;
    font-size: 0.5333rem;
  }
  .title {
    margin-top: 0.32rem;
    font-size: 0.48rem;
    font-weight: 600;
    color: #31293b;
    line-height: 0.6667rem;
    padding: 0.2667rem 0.4267rem;
  }
  .name {
    border-radius: 0.64rem 0.64rem 0 0;
    position: sticky;
    top: 0;
    background: #fff;
    width: calc(100% - 0.8533rem);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.32rem 0.4267rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.5333rem;
    font-weight: 600 !important;
    color: #31293b !important;
    p {
      max-width: 3.2rem;
    }
    span {
      font-weight: 600 !important;
      color: #31293b !important;
      margin-left: 0.8rem;
      width: 2.1333rem;
      text-align: center;
    }
  }
  .list {
    padding: 0 0.4267rem;
    .type {
      color: #f40;
      font-size: 0.4267rem;
      padding: 0.2133rem 0;
    }
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.32rem 0;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5333rem;
      p {
        max-width: 3.2rem;
      }
      span {
        color: #666666;
        margin-left: 0.8rem;
        width: 2.1333rem;
        text-align: center;
      }
      &.name {
        font-weight: 600 !important;
        color: #31293b !important;
        span {
          font-weight: 600 !important;
          color: #31293b !important;
        }
      }
    }
  }
}
.success_dialog {
  text-align: center;
  padding: 0.64rem 1.2533rem;
  background: url('../../assets/images/dailog_bg.png');
  background-size: cover;
  border-radius: 0.64rem;
  overflow: hidden;

  .tips_img {
    position: absolute;
    left: 50%;
    top: -1.7333rem;
    transform: translateX(-50%);
    width: 4.2667rem;
    height: 2.6133rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .tips {
    margin-top: 0.64rem;
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
  }
  .event {
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin-top: 0.64rem;
    padding: 0.24rem 0;
  }
}
.tcp_tips {
  .title {
    text-align: center;
    font-size: 0.48rem;
    font-weight: 600;
    padding-top: 0.67rem;
    padding-bottom: 0;
    color: #333333;
  }

  .content {
    font-size: 0.373rem;
    color: #333333;
    padding: 0.533rem 0.533rem 0;
    max-height: 7rem;
    overflow-x: hidden;
    overflow-y: scroll;

    span {
      display: inline-block;
    }
  }

  .btn {
    margin: 0.533rem auto 0.667rem;
    width: 80%;
    height: 1.2rem;
    line-height: 1.2rem;
    color: #ffffff;
    background: $color_main;
    border-radius: 1rem;
    text-align: center;
    font-size: 0.4rem;
    cursor: pointer;
  }
}
</style>

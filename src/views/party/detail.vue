<!-- @format -->

<template>
  <div class="main">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="nav"></div>
    <div class="head">
      <img v-if="info.drawimg" v-lazy="info.drawimg_url" alt="" />
      <img v-else v-lazy="info.drawimg_url" alt="" />
    </div>
    <div class="content">
      <div class="info">
        <p class="title">{{ info.title }}</p>
        <div class="monery">
          <div class="data">佣金：女士 ¥{{ info.ps2 }} ｜ 男士 ¥{{ info.ps1 }}</div>
          <div class="price">
            <span>
              女士：
              <template v-if="info.moneylady > 0">¥{{ info.moneylady }}</template>
              <template v-else>免费</template>
            </span>
            <span>
              男士：
              <template v-if="info.moneyman > 0">¥{{ info.moneyman }}</template>
              <template v-else>免费</template>
            </span>
          </div>
        </div>
        <div class="more flex">
          <div class="flex_1">
            <div>
              <p class="name">
                <i class="iconfont icon-shizhong"></i>
                活动时间
              </p>
              <p class="value">{{ getWeekText(info.starttime) }} {{ getTime(info.starttime) }}</p>
            </div>
            <div>
              <p class="name">
                <i class="iconfont icon-shizhong"></i>
                联系电话
              </p>
              <p class="value">{{ info.contact }}</p>
            </div>
          </div>
          <div class="flex_1">
            <div>
              <p class="name">
                <i class="iconfont icon-dingwei-01"></i>
                活动地址
              </p>
              <p class="value">{{ info.address }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="bmdata">
        <div class="tit flex flex_ac flex_jsb">
          <div class="flex flex_ac">
            <span>报名人数：</span>
            <p v-if="info.bm_nums > 0">
              男
              <em>{{ info.bm_user1 }}</em>
              人&emsp;女
              <em>{{ info.bm_user2 }}</em>
              人
            </p>
          </div>
          <span v-if="false" class="flex_dc" @click="$router.push('/party/bmlist?id=' + info.partyid)">
            查看全部
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="nums flex flex_ac" v-if="info.bm_nums > 0">
          <template v-for="(item, index) in info.bmuser" :key="item.bmid">
            <span v-if="index < 7">
              <img v-lazy="item.user?.headimg_s_url" type="user" alt="" />
            </span>
          </template>
          <span v-if="false" @click="$router.push('/party/bmlist?id=' + info.partyid)">
            <i class="iconfont icon-more"></i>
          </span>
        </div>
        <p v-else>暂无报名</p>
      </div>
      <div class="intro flex flex_ac flex_jsb">
        <div class="flex flex_ac flex_1 ws">
          <i class="iconfont icon-tishi"></i>
          <span>佣金说明：</span>
          <p class="ws">推广用户报名参与互选后推广用户报名参与互选后推广用户报名参与互选后推广用户报名参与互选后...</p>
        </div>
        <span class="flex_s" @click="showIntro">查看</span>
      </div>
      <div class="detail">
        <p class="tit">活动详情</p>
        <div class="value" v-if="info.content" v-html="info.content"></div>
        <div class="value" v-else>暂无详情</div>
      </div>
    </div>
    <div class="h80"></div>
    <div class="btn flex_dc">
      <span class="flex_1 flex_dc" @click="$router.push('/party/bmlist?id=' + info.partyid)">
        <i class="iconfont icon-baomingjilu"></i>
        报名记录
      </span>
      <span class="flex_1 flex_dc" @click="btnShare">
        <i class="iconfont icon-fenxiangfangshi"></i>
        推广分享
      </span>
    </div>
  </div>

  <oe_popup ref="intro_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">佣金说明</p>
      <p class="tip">
        推广用户报名参与活动/互选后且成功支付相关费用，系统根据对应的平台用户报名费用将对应佣金在活动/互选结束后结算至绑定推广红娘账户。如系统设置用户报名费用为0元，则推广红娘对应获取佣金也为0元。
      </p>
      <div @click="proxy.$refs.intro_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <party_share ref="partyShare" />

</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import oe_popup from '@/oeui/popup.vue'
import party_share from '@/views/party/components/party_share.vue'
import { getPartyDetail } from '@/api/party.js'
import { getTime, getWeekText } from '@/utils/hooks.js'
import { isWeiXin, setWechatShare } from '@/utils/jssdk'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const route = useRoute()

const info = ref({})

const getDetail = () => {
  getPartyDetail({
    id: route.query.id,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data.content = setHtmlExp(res.result.data.content)
      info.value = res.result.data
      if (isWeiXin()) setWechatShare(res.result.share_data)
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}
const setHtmlExp = result => {
  if (result) {
    let exp = new RegExp('&amp;nbsp;', 'g')
    result = result
      .replace(result ? /&(?!#?\w+;)/g : /&/g, '&amp;')
      .replace(/&lt;/g, '<')
      .replace(/<img/g, '<img style="max-width: 100%;height: auto;border-radius: .32rem;" ')
      .replace(/height=/g, '')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&rarr;/g, '-')
      .replace(/&ldquo;/g, '"')
      .replace(/&rdquo;/g, '"')
    return result.replace(exp, '\u3000')
  }
}

const showIntro = () => {
  proxy.$refs.intro_dialog.open()
}

const btnShare = () => {
  proxy.$refs.partyShare.open(route.query.id)
}

onMounted(() => {
  getDetail()
})
</script>

<style lang="scss" scoped>
.main {
  position: relative;
  background: #f2f4f5;
  width: 100%;
  min-height: 100vh;
  .back {
    width: 0.64rem;
    height: 0.64rem;
    border-radius: 0.1067rem;
    background: #00000081;
    color: #fff;
    position: fixed;
    top: 0.2667rem;
    left: 0.4267rem;
    z-index: 300;
  }
  .nav {
    font-size: 0.48rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #2a2546;
    line-height: 1.1733rem;
    text-align: center;
    position: absolute;
    width: 100%;
  }
  .head {
    height: 4.84rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .content {
    padding: 0 0.2667rem;
    margin-top: -0.5333rem;
    position: relative;
    z-index: 100;
    .info {
      background: #fff;
      padding: 0.4267rem 0.4267rem 0.32rem 0.32rem;
      border-radius: 0.32rem;
      .title {
        font-size: 0.48rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        color: #31293b;
        line-height: 0.6667rem;
        margin-bottom: 0.2133rem;
      }
      .monery {
        box-sizing: border-box;
        font-family: PingFang SC-Regular, PingFang SC;
        .data {
          color: #7d68fe;
          font-size: 0.48rem;
          line-height: 0.6667rem;
          font-weight: 500;
        }
        .price {
          margin-top: 0.1067rem;
          font-size: 0.32rem;
          line-height: 0.4533rem;
          font-weight: normal;
          display: flex;
          align-items: center;
          color: #999999;
          span {
            margin-right: 0.5333rem;
          }
        }
      }
      .more {
        font-family: PingFang SC, PingFang SC;
        background: #f2f4f5;
        margin-top: 0.4267rem;
        border-radius: 0.32rem;
        padding: 0.32rem 0;
        & > div {
          padding: 0 0.4267rem;

          > div {
            margin-bottom: 0.32rem;
            .name {
              i {
                position: relative;
                top: 0.0267rem;
                font-weight: 500;
              }
              font-size: 0.3733rem;
              font-weight: 500;
              color: #31293b;
              line-height: 0.5333rem;
            }
            .value {
              font-size: 0.32rem;
              font-weight: normal;
              color: #666666;
              line-height: 0.4533rem;
              margin-top: 0.0533rem;
            }
          }
          & > div:last-child {
            margin-bottom: 0 !important;
          }
        }
        & > div:first-child {
          border-right: 0.0267rem solid #fff;
        }
      }
    }
    .bmdata {
      margin-top: 0.32rem;
      background: #fff;
      padding: 0.4267rem;
      border-radius: 0.32rem;
      font-family: PingFang SC, PingFang SC;
      .tit {
        > div {
          span {
            font-size: 0.4267rem;
            font-weight: 500;
            color: #31293b;
            line-height: 0.5867rem;
          }
          p {
            font-size: 0.3733rem;
            font-weight: normal;
            color: #31293b;
            position: relative;
            top: 0.0267rem;
          }
          em {
            color: $color_main;
          }
        }
        > span {
          font-size: 0.3733rem;
          font-weight: normal;
          color: #666666;
          line-height: 0.5333rem;
          i {
            position: relative;
            top: 0.0267rem;
          }
        }
      }
      .nums {
        margin-top: 0.4267rem;
        > span:first-child {
          margin-left: 0;
        }
        span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 1.3333rem;
          height: 1.3333rem;
          border: 0.0667rem solid #fff;
          background: #fff;
          border-radius: 50%;
          overflow: hidden;
          box-sizing: border-box;
          margin-left: -0.2667rem;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          i {
            color: #000;
            font-size: 0.5333rem;
          }
        }
      }
    }
    .intro {
      margin-top: 0.32rem;
      background: #fff;
      border-radius: 0.32rem;
      padding: 0.4267rem 0.32rem;
      font-family: PingFang SC, PingFang SC;
      > div {
        margin-right: 0.4rem;
        font-size: 0.3733rem;
        font-weight: 400;
        line-height: 0.5333rem;
        i {
          color: #000;
          margin-right: 0.2133rem;
          position: relative;
          top: 0.0267rem;
        }
        span {
          color: #31293b;
        }
        p {
          color: #666666;
        }
      }

      > span {
        cursor: pointer;
        background: $color_main;
        color: #fff;
        font-size: 0.3733rem;
        font-weight: normal;
        line-height: 0.5333rem;
        padding: 0.0533rem 0.32rem;
        border-radius: 0.32rem;
      }
    }
    .detail {
      margin-top: 0.32rem;
      font-family: PingFang SC, PingFang SC;
      .tit {
        padding: 0.2667rem;
        font-size: 0.48rem;
        font-weight: 500;
        color: #2a2546;
        line-height: 0.6667rem;
      }
    }
  }
  .btn {
    position: fixed;
    width: 100%;
    height: 1.6rem;
    bottom: 0.2667rem;
    z-index: 300;
    font-family: PingFang SC, PingFang SC;
    span {
      i {
        margin-right: 0.2133rem;
        font-size: 0.48rem;
      }
      width: fit-content;
      cursor: pointer;
      background: $color_main;
      font-size: 0.4267rem;
      font-weight: normal;
      color: #ffffff;
      line-height: 0.5867rem;
      padding: 0.2667rem 0;
      border-radius: 0.5867rem;
      margin: 0 0.8533rem;
    }
  }
}

</style>

<style lang="scss">
.detail {
  .value {
    padding: 0 0.2667rem;
    margin-top: 0.32rem;
    font-size: 0.3733rem;
    line-height: 0.64rem;
    color: #2a2546;
    font-weight: normal;
    white-space: pre-line;
    white-space: normal;
    word-break: break-word;

    img {
      margin: .2667rem 0;
      max-width: 100%;
      object-fit: cover;
      border-radius: 0.32rem;
    }
  }
}
</style>

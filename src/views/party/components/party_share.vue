<!-- @format -->

<template>
  <oe_popup ref="shareDialog" mode="bottom" roundStyle=".64rem" round="true">
    <div class="shareDialog">
      <p class="title">分享给好友</p>
      <div class="channel">
        <div @click="openwxshare" v-if="isWeiXin()">
          <span>
            <img src="@/assets/images/weixin_share.png" alt="" />
          </span>
          <p>微信好友</p>
        </div>
        <div @click="cardShard">
          <span>
            <img src="@/assets/images/card_share.png" alt="" />
          </span>
          <p>生成海报</p>
        </div>
        <div @click="copyLink">
          <span>
            <img src="@/assets/images/link_share.png" alt="" />
          </span>
          <p>复制链接</p>
        </div>
      </div>
      <span @click="proxy.$refs.shareDialog.close()" class="close iconfont icon-cha"></span>
    </div>
  </oe_popup>
  <party_card ref="partyCard" :info="info" />
  <pop_custom ref="wxsharepopup" :bgrount="false" bg="none">
    <div @click="proxy.$refs.wxsharepopup.close()" style="height: 100vh; background: rgba(0, 0, 0, 0.45)">
      <img style="width: 50vw; right: 0.2667rem; top: 0.2667rem" class="pa" src="@/assets/images/jian.png" alt="" />
    </div>
  </pop_custom>
</template>

<script setup>
  import { ref, getCurrentInstance } from 'vue'
  import oe_popup from '@/oeui/popup.vue'
  import pop_custom from '@/components/pop_custom.vue'
  import party_card from '@/views/party/components/party_card.vue'
  import { getPartyDetail } from '@/api/party.js'
  import { isWeiXin, setWechatShare } from '@/utils/jssdk'
  const { proxy } = getCurrentInstance()
  const OEUI = proxy.OEUI
  const partyid = ref(null)
  const info = ref({})
  const shareUrl = ref(null)

  const open = id => {
    getDetail(id)
    partyid.value = id
  }

  const getDetail = id => {
    getPartyDetail({
      id
    }).then(res => {
      if (res.ret == 1) {
        shareUrl.value = res.result.url
        info.value = res.result.data
        proxy.$refs.shareDialog.open()
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试'
        })
      }
    })
  }

  const copy = data => {
    let elInput = document.createElement('input')
    elInput.value = data
    document.body.appendChild(elInput)
    elInput.select()
    document.execCommand('Copy')
    OEUI.modal({
      title: '复制成功',
      text: '链接复制成功,你可以将链接发给您的微信好友!',
      cancelShow: false,
      confirm: () => {}
    })
    elInput.remove()
  }

  const openwxshare = () => {
    proxy.$refs.shareDialog.close(() => {
      proxy.$refs.wxsharepopup.open()
    })
  }

  const cardShard = () => {
    proxy.$refs.shareDialog.close(() => {
      proxy.$refs.partyCard.open(partyid.value)
    })
  }

  const copyLink = () => {
    proxy.$refs.shareDialog.close(() => {
      copy(info.value.title + ' :' + shareUrl.value)
    })
  }

  defineExpose({
    open
  })
</script>

<style lang="scss" scoped></style>

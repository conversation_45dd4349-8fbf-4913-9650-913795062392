<!-- @format -->

<template>
  <!-- 海报定位 -->
  <div class="pr bg_f" style="width: 10rem; height: 17.787rem; overflow: hidden" ref="cardBox">
    <img class="pa lt w100" src="@/assets/images/poster/poster1_bg.png" />
    <div class="pa l15" style="right: 0.373rem; top: 1.04rem">
      <img class="w100" src="@/assets/images/poster/poster1_bg2.png" />
    </div>
    <div class="pr" style="z-index: 1">
      <img class="pa l33" style="width: 6.693rem; top: 1.733rem" src="@/assets/images/poster/poster1_tit.png" />
      <img class="pa r33" style="width: 1.173rem; top: 1.867rem" src="@/assets/images/poster/poster1_tips.png" />
      <img class="pa l33" style="width: 8.293rem; top: 2.693rem" src="@/assets/images/poster/poster1_line.png" />
      <div class="pa oh l33 r33" style="top: 3.093rem; height: 5.253rem">
        <img class="w100" :src="info.drawimg_base64" v-if="info.drawimg_base64" />
        <img src="@/assets/images/loading_img.png" class="w100" v-else />
      </div>
      <div class="pa fz14 lh39" style="left: 1.28rem; top: 8.667rem; color: #2e225a">
        <p v-if="Number(info.limitman) + Number(info.limitlady) > 0">活动人数：{{ Number(info.limitman) + Number(info.limitlady) }}人</p>
        <p v-else>活动人数：不限制</p>
        <p>活动时间：{{ info.starttime > 0 ? getTime(info.starttime) : '--' }}</p>
        <p>截止报名时间：{{ info.endbmtime > 0 ? getTime(info.endbmtime) : '--' }}</p>
      </div>
      <img class="pa l33" style="width: 8.293rem; top: 12.373rem" src="@/assets/images/poster/poster1_line.png" />
      <img class="pa" style="width: 3.947rem; left: 1.253rem; top: 13.307rem" src="@/assets/images/poster/poster1_ewm.png" />
      <div class="pa fz13" style="left: 1.253rem; top: 14.027rem; color: #5b5b5b">在对的时间开启你的寻爱之旅</div>
      <div class="pa img_66" style="right: 1.28rem; top: 13.013rem">
        <img class="w100" :src="qrcode_img" v-if="qrcode_img" />
        <img src="@/assets/images/loading_img.png" class="w100" v-else />
      </div>
    </div>
  </div>
</template>
<script setup>
import { getCurrentInstance, ref, nextTick } from 'vue'
import { getPartyQrcode } from '@/api/party.js'
import { getTime } from '@/utils/hooks.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const emit = defineEmits(['save'])
const props = defineProps({
  info: {
    type: Object,
  },
})

const qrcode_img = ref('')

// 获取模板列表
const getQrcode = id => {
  OEUI.loading.show('模板加载中')
  getPartyQrcode({ id })
    .then(res => {
      OEUI.loading.hide()
      if (res.ret == 1) {
        qrcode_img.value = res.result.data
        nextTick(() => {
          emit('save')
        })
      } else {
        OEUI.toast('获取模板失败')
      }
    })
    .catch(() => {
      OEUI.toast('获取模板失败')
    })
}

const open = id => {
  getQrcode(id || props.info.partyid)
}

defineExpose({
  open,
})
</script>
<style lang="scss" scoped>
.bg_f {
  background: #fff;
}

.l15 {
  left: 0.4rem;
}

.lt {
  left: 0;
  top: 0;
}

.l33 {
  left: 0.88rem;
}

.r33 {
  right: 0.88rem;
}

.fz14 {
  font-size: 0.3733rem;
}

.lh39 {
  line-height: 1.04rem;
}

.fz13 {
  font-size: 0.3467rem;
}

.img_66 {
  width: 1.76rem;
  height: 1.76rem;
}

.w100 {
  width: 100%;
}
</style>

<!-- @format -->

<template>
  <div @click="goDetail(item.partyid)" class="item_box" v-if="item.partyid">
    <div class="head pr">
      <img v-lazy="item.drawimg_url" alt="" />
      <div class="data">
        <p class="title ws">{{ item.title }}</p>
        <div class="time flex flex_ac flex_jsb">
          <span>活动时间：{{ getTime(item.starttime) }}</span>
          <span v-if="item.area2 > 0">
            <i class="iconfont icon-dingwei-01 pr" style="top: 0.0267rem"></i>
            {{ item.area2_t }}
            <template v-if="item.area3 > 0">
              ·
              {{ item.area3_t }}
            </template>
            <template v-if="item.area4 > 0">
              ·
              {{ item.area4_t }}
            </template>
          </span>
        </div>
      </div>
    </div>
    <div class="info">
      <div class="data">推广佣金：女士 ¥{{ item.ps2 }} ｜ 男士 ¥{{ item.ps1 }}</div>
      <div class="price">
        <span>报名费用：</span>
        <span>
          女士：
          <template v-if="item.moneylady > 0">¥{{ item.moneylady }}</template>
          <template v-else>免费</template>
        </span>
        <span>
          男士：
          <template v-if="item.moneyman > 0">¥{{ item.moneyman }}</template>
          <template v-else>免费</template>
        </span>
      </div>
    </div>
    <div class="join">
      <div class="people">
        <div class="users">
          <template v-for="(val, index) in item.bmuser" :key="val.bmid">
            <span v-if="index < 3">
              <img :src="val?.user?.headimg_s_url" alt="" />
            </span>
          </template>
          <span></span>
        </div>
        <!--<div class="silder">
          <div style="width: 20%"></div>
        </div>-->
        <span class="nums">已报名：{{ item.bm_nums }}</span>
      </div>
      <!-- <span v-if="item.state == 1" class="btn" @click.stop="share(item.partyid)">邀请报名</span> -->
      <span v-if="item.state == 1" class="btn">邀请报名</span>
      <span v-else class="btn">推广分享</span>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { getTime } from '@/utils/hooks.js'
import { useRouter } from 'vue-router'
const { proxy } = getCurrentInstance()

const emits = defineEmits(['share'])

const router = useRouter()

defineProps({
  item: {
    type: Object,
    required: true,
  },
})

const goDetail = id => {
  router.push('/party/detail?id=' + id)
}

const share = id => {
  emits('share', id)
}
</script>

<style lang="scss" scoped>
.item_box {
  cursor: pointer;
  background: #fff;
  border-radius: 0.32rem;
  overflow: hidden;
  margin-bottom: 0.32rem;
  .head {
    height: 4.4267rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .data {
      position: absolute;
      width: 100%;
      height: 2.2133rem;
      bottom: 0;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 97%);
      padding: 0 0.32rem;
      box-sizing: border-box;

      .title {
        font-size: 0.4267rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 0.5867rem;
        margin-top: 0.7467rem;
      }
      .time {
        margin-top: 0.2133rem;
        font-size: 0.32rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #c5c3c7;
        line-height: 0.4533rem;
      }
    }
  }
  .info {
    padding: 0.32rem;
    box-sizing: border-box;
    border-bottom: 0.0267rem solid #f2f4f5;
    .data {
      color: #7d68fe;
      font-size: 0.48rem;
      line-height: 0.6667rem;
      font-weight: 500;
    }
    .price {
      margin-top: 0.08rem;
      font-size: 0.32rem;
      line-height: 0.4533rem;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: normal;
      display: flex;
      align-items: center;
      color: #999999;
      span {
        margin-right: 0.5333rem;
      }
    }
  }
  .join {
    padding: 0.2133rem 0.32rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .people {
      display: flex;
      align-items: center;
      .users {
        display: flex;
        align-items: center;
        padding-left: 0.2133rem;
        span {
          width: 0.5867rem;
          height: 0.5867rem;
          border-radius: 50%;
          margin-left: -0.2667rem;
          border: 0.0533rem solid #fff;
          box-sizing: border-box;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        span:last-child {
          background: #fff;
        }
      }
      .silder {
        margin-left: -0.2667rem;
        width: 2.0267rem;
        height: 0.2133rem;
        background: #f2f4f5;
        border-radius: 0.1067rem;
        position: relative;
        div {
          position: absolute;
          height: 100%;
          background: #7d68fe;
          border-radius: 0.1067rem;
        }
      }
      .nums {
        margin-left: -0.2133rem;
      }
    }
    .btn {
      background: #7d68fe;
      color: #fff;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: normal;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      padding: 0.16rem 0.4267rem;
      border-radius: 0.4267rem;
      &.fail {
        background: #ccc;
      }
    }
  }
}
</style>

<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">活动推广记录</div>
    </div>

    <oeui-list :top="1.1733" @refresh="refresh" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="list">
        <template v-if="list.length">
          <template v-for="item in list" :key="item.partyid">
            <div @click="goDetail(item.partyid)" class="item_box" v-if="item.partyid">
              <div class="head pr">
                <img v-lazy="item.drawimg_url" alt="" />
                <div class="data">
                  <p class="title ws">{{ item.title }}</p>
                  <div class="time flex flex_ac flex_jsb">
                    <span>活动时间：{{ getTime(item.starttime) }}</span>
                    <span v-if="item.area2 > 0">
                      <i class="iconfont icon-dingwei-01 pr" style="top: 0.0267rem"></i>
                      {{ item.area2_t }}
                      <template v-if="item.area3 > 0">
                        ·
                        {{ item.area3_t }}
                      </template>
                    </span>
                  </div>
                </div>
              </div>
              <div class="info">
                <div class="data">推广佣金：女士 ¥{{ item.ps2 }} ｜ 男士 ¥{{ item.ps1 }}</div>
                <div class="price">
                  <span>报名费用：</span>
                  <span>
                    女士：
                    <template v-if="item.moneylady > 0">¥{{ item.moneylady }}</template>
                    <template v-else>免费</template>
                  </span>
                  <span>
                    男士：
                    <template v-if="item.moneyman > 0">¥{{ item.moneyman }}</template>
                    <template v-else>免费</template>
                  </span>
                </div>
              </div>
              <div class="join">
                <div class="people">
                  <div class="users">
                    <template v-for="(val, index) in item.bmuser" :key="val.bmid">
                      <span v-if="index < 4">
                        <img :src="val.user.headimg_s_url" alt="" />
                      </span>
                    </template>
                    <span></span>
                  </div>
                  <span class="nums">已推广：{{ item.bmuser_total }}</span>
                </div>
                <span class="btn" @click.stop="showBmList(item.partyid)">查看报名</span>
              </div>
            </div>
          </template>
        </template>
        <template v-else-if="listStatus != 'no_data'">
          <div class="party_skelecton">
            <div class="head"></div>
            <div class="info">
              <p></p>
              <div class="flex flex_ac">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </template>
      </div>
    </oeui-list>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue'
import oeuiList from '@/oeui/list.vue'
import { getMyParty } from '@/api/party.js'
import { getTime } from '@/utils/hooks.js'
import { useRouter } from 'vue-router'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const router = useRouter()
const list = ref([])
const page = ref(1)
const pageCount = ref('0')
const listStatus = ref('loading')

const goDetail = id => {
  router.push('/party/detail?id=' + id)
}

const showBmList = id => {
  router.push('/party/bmlist?id=' + id)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getMyParty({
    page: page.value,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}
const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}
getList()
</script>

<style lang="scss" scoped>
.main {
  position: absolute;
  width: 100vw;
  min-height: 100vh;
  background: #f7f8fa;
  .list {
    padding: 0.1067rem 0.4267rem;
    .item_box {
      cursor: pointer;
      background: #fff;
      border-radius: 0.32rem;
      overflow: hidden;
      margin-bottom: 0.32rem;
      .head {
        height: 4.4267rem;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .data {
          position: absolute;
          width: 100%;
          height: 2.2133rem;
          bottom: 0;
          background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 97%);
          padding: 0 0.32rem;
          box-sizing: border-box;

          .title {
            font-size: 0.4267rem;
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 0.5867rem;
            margin-top: 0.7467rem;
          }
          .time {
            margin-top: 0.2133rem;
            font-size: 0.32rem;
            font-family: PingFang SC, PingFang SC;
            font-weight: normal;
            color: #c5c3c7;
            line-height: 0.4533rem;
          }
        }
      }
      .info {
        padding: 0.32rem;
        box-sizing: border-box;
        border-bottom: 0.0267rem solid #f2f4f5;
        .data {
          color: #7d68fe;
          font-size: 0.48rem;
          line-height: 0.6667rem;
          font-weight: 500;
        }
        .price {
          margin-top: 0.08rem;
          font-size: 0.32rem;
          line-height: 0.4533rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          display: flex;
          align-items: center;
          color: #999999;
          span {
            margin-right: 0.5333rem;
          }
        }
      }
      .join {
        padding: 0.2133rem 0.32rem;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .people {
          display: flex;
          align-items: center;
          .users {
            display: flex;
            align-items: center;
            padding-left: 0.2133rem;
            span {
              width: 0.5867rem;
              height: 0.5867rem;
              border-radius: 50%;
              margin-left: -0.2667rem;
              border: 0.0533rem solid #fff;
              box-sizing: border-box;
              overflow: hidden;
              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            span:last-child {
              background: #fff;
            }
          }
          .silder {
            margin-left: -0.2667rem;
            width: 2.0267rem;
            height: 0.2133rem;
            background: #f2f4f5;
            border-radius: 0.1067rem;
            position: relative;
            div {
              position: absolute;
              height: 100%;
              background: #7d68fe;
              border-radius: 0.1067rem;
            }
          }
          .nums {
            margin-left: -0.2133rem;
          }
        }
        .btn {
          background: #7d68fe;
          color: #fff;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          padding: 0.16rem 0.4267rem;
          border-radius: 0.4267rem;
        }
      }
    }
  }
}
</style>

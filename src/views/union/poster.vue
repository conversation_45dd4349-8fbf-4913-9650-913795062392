<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="back" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc flex_1">
      <p @click="changeTgtype('user')" :class="{ current: tgtype == 'user' }" style="margin-right: 0.64rem">邀请单身</p>
      <p @click="changeTgtype('union')" :class="{ current: tgtype == 'union' }">邀请红娘</p>
    </div>

    <span @click="openGuidance" class="pa flex_dc" style="right: 0 !important; width: 1.1733rem; height: 1.1733rem">
      <i class="iconfont icon-wenhaoxiao color_main" style="font-size: 0.48rem"></i>
    </span>
  </div>
  <div class="h44"></div>
  <div class="main">
    <div class="slogan">
      <div class="title flex flex_ac flex_jsb">
        <span class="name">发圈素材</span>
        <span
          class="updata flex flex_ac"
          @click="
            $router.replace({
              path: '/slogan',
              query: {
                type: tgtype,
              },
            })
          "
        >
          <i class="iconfont icon-quanbuhuati"></i>
          全部文案
        </span>
      </div>
      <div class="intro flex flex_v">
        <p>{{ slogan }}</p>
        <div class="btn">
          <span @click="openLink">
            <i class="iconfont icon-lianjie" style="font-size: 0.48rem; top: 0.05rem"></i>
            生成链接
          </span>
          <span @click="copy(slogan)">
            <i class="iconfont icon-fuzhi"></i>
            复制文案
          </span>
        </div>
      </div>
    </div>
    <div class="poster">
      <div class="title">
        <span class="name">海报素材</span>
        <span>(点击预览保存)</span>
      </div>
      <div class="list">
        <template v-for="item in posterList" :key="item.postid">
          <div @click="changCard(item.postid)" class="item" v-if="item.postid">
            <div class="img"><img v-lazy="item?.thumbimg_url || item.postimg_url" /></div>
            <p>生成海报</p>
          </div>
        </template>
      </div>
    </div>
  </div>

  <oe_popup ref="linkIntro" mode="bottom" roundStyle=".64rem" round="true">
    <div class="linkIntro">
      <div class="title flex flex_ac" @click="proxy.$refs.linkIntro.close()">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
        <p>文案及链接说明</p>
      </div>
      <p class="tips">复制出来的口令可发给微信朋友，或者微信群</p>
      <p class="tit">格式如下：</p>
      <div class="data">
        <!-- 关注公众号 👉#{{ config.wxpublic_name }} -->
        <!-- <br /> -->
        <br />
        {{ slogan }}
        <div class="flex flex_ac undata" @click="getSlogan(true)">
          <i class="iconfont icon-shuaxin1" :class="is_refresh ? 'refresh_anime' : ''"></i>
          换一个
        </div>
        -----------------------------------
        <br />
        ☎ 服务热线: {{ config.sitetel }}
        <br />
        <br />
        点击下方链接进一步了解
        <br />
        {{ config.siteurl }}
        <br />
      </div>

      <div class="btn" @click="copyLink">复制链接</div>
    </div>
  </oe_popup>
  <oe_popup ref="copysuccess_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="copysuccess_dialog">
      <p class="tit">复制成功</p>
      <p class="tips">请到朋友圈或群粘贴</p>
      <div @click="proxy.$refs.copysuccess_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <oe_popup ref="guidanceEl" mode="right" width="100%">
    <div class="pr" style="background: #f4f5f7; width: 100vw; height: 100vh">
      <div class="top_box">
        <img src="@/assets/images/bg_main.png" />
      </div>
      <div class="pf top_nav flex_dc">
        <span @click="closeGuidance" class="back flex_dc">
          <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
        </span>
        <div class="flex_dc" style="font-size: 0.4267rem">如何分享？</div>
      </div>
      <div class="h44"></div>
      <div class="main pr oy" style="z-index: 100; height: calc(100vh - 1.1733rem); padding-bottom: 1.0667rem">
        <div style="width: 100%">
          <img src="@/assets/images/poster/guidance1.png" style="width: 100%; object-fit: cover" />
        </div>
        <div style="font-size: 0.3733rem; line-height: 0.64rem; color: #333; margin-top: 0.3733rem">
          <span class="color_main">生成链接：</span>
          点击生成链接系统会展示模板文案内容以及 推广链接，复制后粘贴转发，用户点击链接自动跳转对 应内容页面。
        </div>
        <div style="font-size: 0.3733rem; line-height: 0.64rem; color: #333; margin-top: 0.3733rem">
          <span class="color_main">复制文案：</span>
          复制展示卡片中文案内容，粘贴转发。
        </div>
        <div style="width: 100%; margin-top: 0.3733rem">
          <img src="@/assets/images/poster/guidance2.png" style="width: 100%; object-fit: cover" />
        </div>
        <div style="font-size: 0.3733rem; line-height: 0.64rem; color: #333; margin-top: 0.3733rem">
          <span class="color_main">海报素材：</span>
          点击查看大图海报，长按保存本地或转发。
        </div>
      </div>
    </div>
  </oe_popup>

  <union_card ref="unionCard" />
</template>

<script setup>
import { ref, getCurrentInstance, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import oe_popup from '@/oeui/popup.vue'
import union_card from '@/views/union/components/union_card.vue'
import { getTgSlogan, getTgPoster } from '@/api/union.js'
import { getTgSLink } from '@/api/user.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const config = computed(() => store.state.config)

const route = useRoute()
const router = useRouter()

const tgtype = ref(route.query.type)
const changeTgtype = val => {
  proxy.$refs.unionCard.cancelCardUrl(() => {
    tgtype.value = val
    getSlogan()
    getPosterList()
  })
}

const default_mbid = ref(null)
const my_mbid = ref(null)

const posterList = ref([])
const getPosterList = () => {
  getTgPoster({
    tgtype: tgtype.value,
  }).then(res => {
    if (res.ret == 1) {
      default_mbid.value = res.result.default_mbid
      my_mbid.value = res.result.my_mbid
      posterList.value = res.result.data
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const back = () => {
  if (window.history.length <= 1) {
    router.push('/home')
    return
  } else if (!window.history.state.back) {
    router.replace({ path: '/home' })
    return
  } else {
    router.back()
  }
}

const openGuidance = () => {
  proxy.$refs.guidanceEl.open()
}
const closeGuidance = () => {
  proxy.$refs.guidanceEl.close()
}

const slogan = ref(route.query.slogan || '')
const is_refresh = ref(false)
const getSlogan = flag => {
  if (is_refresh.value) return
  if (flag) is_refresh.value = true
  getTgSlogan({
    s_type: tgtype.value == 'user' ? 1 : 2,
  }).then(res => {
    if (flag) is_refresh.value = false
    if (res.ret == 1) {
      slogan.value = res.result.data.content
    } else {
      if (flag) is_refresh.value = false
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const shareLink = ref('')

const openLink = () => {
  getLink(() => {
    proxy.$refs.linkIntro.open()
  })
}

const getLink = callback => {
  getTgSLink({
    tgtype: tgtype.value,
  }).then(res => {
    if (res.ret == 1) {
      shareLink.value = res.result
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const copyLink = () => {
  // 关注公众号 👉 #${config.value.wxpublic_name}\r
  let str = `\r${slogan.value}\r-----------------------------------\r☎ 服务热线:${config.value.sitetel}\r\r点击下方链接进一步了解\r${shareLink.value}\r`
  copy(str, () => {
    proxy.$refs.linkIntro.close()
  })
}

const copy = (data, callback) => {
  if (callback) callback()
  let elInput = document.createElement('textarea')
  elInput.value = data
  document.body.appendChild(elInput)
  elInput.select()
  document.execCommand('Copy')
  proxy.$refs.copysuccess_dialog.open()
  elInput.remove()
}

const changCard = val => {
  proxy.$refs.unionCard.open(val, tgtype.value)
}

if (!route.query.slogan) {
  getSlogan()
}
getPosterList()
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;

  .title {
    font-size: 0.3733rem;
    line-height: 0.64rem;
    color: #666;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;

    .current {
      font-size: 0.5333rem;
      line-height: 0.7467rem;
      color: #2a2546;
      font-weight: 500;
      position: relative;
      top: -0.0533rem;

      &::after {
        position: absolute;
        content: '';
        height: 0.08rem;
        width: 0.32rem;
        background: $color_main;
        border-radius: 0.0533rem;
        left: 50%;
        transform: translateX(-50%);
        bottom: -0.1333rem;
      }
    }
  }
}

.main {
  padding: 0.32rem 0.4267rem;
  font-family: PingFang SC, PingFang SC;

  .slogan {
    .title {
      .name {
        font-size: 0.4267rem;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 0.5867rem;
      }

      .updata {
        color: #0570f1;
        font-size: 0.3733rem;
        font-weight: normal;
        color: #0570f1;
        line-height: 0.64rem;
        transform: all 0.3s;

        i {
          margin-right: 0.1333rem;
          position: relative;
          top: 0.0267rem;
        }

        .refresh_anime {
          transform: rotate(100deg);
        }
      }
    }

    .intro {
      background: #fff;
      margin-top: 0.32rem;
      padding: 0.32rem;
      border-radius: 0.32rem;

      p {
        font-size: 0.3733rem;
        font-weight: normal;
        color: #666666;
        line-height: 0.5867rem;
      }

      .btn {
        margin-top: 0.2933rem;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        span {
          width: fit-content;
          cursor: pointer;
          font-size: 0.3733rem;
          font-weight: normal;
          color: $color_main;
          line-height: 0.5333rem;
          box-sizing: border-box;
          margin-left: 0.4rem;
          .iconfont {
            position: relative;
            top: 0.0267rem;
          }
        }
      }
    }
  }

  .poster {
    margin-top: 0.64rem;

    .title {
      span {
        font-size: 0.32rem;
        font-weight: normal;
        color: #666666;
        line-height: 0.4533rem;
      }

      .name {
        font-size: 0.4267rem;
        font-weight: 500;
        color: #31293b;
        line-height: 0.5867rem;
      }
    }

    .list {
      margin-top: 0.32rem;
      display: flex;
      flex-wrap: wrap;

      .item {
        width: calc(30%);
        margin-right: 0.4267rem;
        .img {
          background: #fafafa;
          border-radius: 0.32rem;
          overflow: hidden;
          width: 100%;
          aspect-ratio: 375/667;
        }
        p {
          text-align: center;
          background: $color_main;
          color: #fff;
          width: fit-content;
          margin: 0 auto;
          margin-top: 0.2667rem;
          font-size: 0.32rem;
          width: 1.6rem;
          line-height: 0.6667rem;
          border-radius: 0.1333rem;
        }

        &:nth-child(3n) {
          margin-right: 0;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        margin-bottom: 0.4267rem;
      }
    }
  }
}

.linkIntro {
  padding: 0.5867rem 0.4267rem 0.64rem 0.4267rem;

  .title {
    font-size: 0.48rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #31293b;
    line-height: 0.6667rem;

    i {
      font-weight: 600;
      font-size: 0.48rem;
      margin-right: 0.0533rem;
    }
  }

  .tips {
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #666666;
    line-height: 0.64rem;
    padding-left: 0.5333rem;
  }

  .tit {
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #999999;
    line-height: 0.64rem;
    margin: 0.4267rem;
  }

  .data {
    max-height: 8rem;
    overflow-y: auto;
    margin-top: 0.5333rem;
    background: #f2f4f5;
    border-radius: 0.32rem;
    padding: 0.4267rem;
    margin-bottom: 0.4267rem;
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #31293b;
    line-height: 0.64rem;
  }

  .undata {
    color: #287fff;
    margin-top: 0.1333rem;
    margin-bottom: 0.1333rem;
    transition: all 0.3s;

    i {
      position: relative;
      margin-right: 0.1333rem;
    }
  }

  .btn {
    background: $color_main;
    color: #fff;
    text-align: center;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    font-weight: normal;
    font-family: PingFang SC, PingFang SC;
    padding: 0.24rem 0;
    margin: 0 0.5333rem;
    border-radius: 0.5333rem;
  }
}

.copysuccess_dialog {
  text-align: center;
  padding: 0.64rem 1.2533rem;
  border-radius: 0.64rem;
  overflow: hidden;

  .tit {
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-weight: 600;
  }

  .tips {
    margin-top: 0.4267rem;
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #666666;
    line-height: 0.5867rem;
  }

  .event {
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin-top: 0.64rem;
    padding: 0.24rem 0;
  }
}

.refresh_anime {
  transform: rotate(100deg);
}
</style>

<!-- @format -->

<template>
  <!-- 相亲卡定位 -->
  <div class="pr bg_b3" style="width: 375px; height: 667px; overflow: hidden" ref="cardBox">
    <img :src="blindcard.postimg_base64" alt="" class="w100 pr z200" />
    <!-- 头像 -->
    <div
      class="oh bo_op0 z300"
      v-if="blindcard.avatar_hide != '1'"
      :style="{
        position: 'absolute',
        width: `${blindcard.avatar_w || 120}px`,
        height: `${blindcard.avatar_h || 120}px`,
        top: `${blindcard.avatar_y || 47}px`,
        left: `${blindcard.avatar_x || 129}px`,
        borderRadius: `${blindcard.avatar_round == 1 ? `50%` : '0px'}`
      }">
      <img :src="unioninfo.headimg_url" alt="" class="w100" />
    </div>
    <!-- 二维码 -->
    <img
      class="bo_op0 z300"
      v-if="blindcard.qrcode_hide != '1' && qrcode_img"
      :src="qrcode_img"
      alt=""
      :style="{
        width: `${blindcard.qrcode_h || 70}px`,
        height: `${blindcard.qrcode_w || 70}px`,
        position: 'absolute',
        top: `${blindcard.qrcode_y || 534}px`,
        left: `${blindcard.qrcode_x || 233}px`
      }" />
    <!-- 二维码文字描述 -->
    <div
      class="z300"
      v-if="blindcard.qrtip_hide != '1' && blindcard.qrtip_cont"
      :style="{
        position: 'absolute',
        width: `${blindcard.qrtip_w || 228}px`,
        height: `${blindcard.qrtip_h || 20}px`,
        top: `${blindcard.qrtip_y || 623}px`,
        left: `${blindcard.qrtip_x || 88}px`,
        color: `${blindcard.qrtip_color || `#9588a4`}`,
        fontSize: `${blindcard.qrtip_size || 12}px`,
        lineHeight: `${blindcard.qrtip_lineheight || 22}px`
      }">
      {{ blindcard.qrtip_cont }}
    </div>
    <!--网站名称-->
    <div
      class="bo_op0 z300"
      v-if="blindcard.name_hide != '1' && blindcard.name_cont"
      :style="{
        position: 'absolute',
        width: `${blindcard.name_w || 228}px`,
        height: `${blindcard.name_h || 20}px`,
        top: `${blindcard.name_y || 623}px`,
        left: `${blindcard.name_x || 88}px`,
        color: `${blindcard.name_color || `#9588a4`}`,
        fontSize: `${blindcard.name_size || 12}px`,
        lineHeight: `${blindcard.name_lineheight || 22}px`
      }">
      {{ blindcard.name_cont }}
    </div>
  </div>
</template>
<script setup>
  import {  getCurrentInstance, ref, nextTick } from 'vue'
  import { getTgPosterInfo } from '@/api/union.js'
  const { proxy } = getCurrentInstance()
  const OEUI = proxy.OEUI
  const emit = defineEmits(['save', 'fail'])
  defineProps({
    // 用户信息
    unioninfo: {
      type: Object
    }
  })

  const qrcode_img = ref('')
  const blindcard = ref({})
  // 获取模板信息
  const getOnetpl = (id, type) => {
    OEUI.loading.show('模板加载中')
    getTgPosterInfo({
      id: id,
      tgtype: type
    })
      .then(res => {
        OEUI.loading.hide()
        if (res.ret == 1) {
          qrcode_img.value = res.result.qr_code_base64
          blindcard.value = res.result.data
          nextTick(() => {
            emit('save')
          })
        } else {
          OEUI.toast('获取模板失败')
          nextTick(() => {
            emit('fail')
          })
        }
      })
      .catch(() => {
        OEUI.toast('获取模板失败')
        nextTick(() => {
          emit('fail')
        })
      })
  }

  const open = (id, type) => {
    getOnetpl(id, type)
  }

  defineExpose({
    open
  })
</script>
<style lang="scss" scoped>
  .bo_op0 {
    border: 1px solid rgba(255, 255, 255, 0);
  }
  .p5 {
    padding: 5px;
  }
  .min_w33 {
    min-width: 33.33%;
  }
  .min_w50 {
    min-width: 50%;
  }
  .t_center {
    text-align: center;
  }
  .inline {
    display: inline;
  }
  .w100 {
    width: 100%;
  }
  .pr {
    position: relative;
  }
  .z100 {
    z-index: 100;
  }
  .oh {
    overflow: hidden;
  }
  .mr10 {
    margin-right: 10px;
  }
  .z300 {
    z-index: 300;
  }
  .z200 {
    z-index: 200;
  }
</style>

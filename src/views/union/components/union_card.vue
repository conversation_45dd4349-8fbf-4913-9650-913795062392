<!-- @format -->

<template>
  <div class="pf popCardBox" ref="cardBox" style="top: 266rem">
    <card-public id="popCardBox" ref="popCardBox" :unioninfo="unionInfo" @save="generateCard" @fail="openFail"></card-public>
  </div>

  <oe_popup ref="cardContent" :maskClose="false" width="6.4rem" mode="center" background="none">
    <div class="cardContent">
      <div class="content">
        <img :src="cardUrl" v-if="cardUrl" alt="" />
      </div>
      <p class="tips">长按图片保存/分享</p>
      <span class="btn flex_dc flex_v">
        <i @click="closeCard" class="iconfont icon-guanbi1 close"></i>
      </span>
    </div>
  </oe_popup>
</template>

<script setup>
import cardPublic from '@/views/union/components/card_public'
import oe_popup from '@/oeui/popup.vue'
import html2canvas from 'html2canvas'
import { getCurrentInstance, nextTick, ref, computed } from 'vue'
import { useStore } from 'vuex'
import { saveTgPoster } from '@/api/union.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const unionInfo = computed(() => store.state.unionInfo)
const cardUrl = ref('')
const cardid = ref(null) //当前模板id
const tgtype = ref('')
const open = (id, type) => {
  tgtype.value = type
  openCard(id, type)
}

const is_open = ref(true)
const openCard = (id, type) => {
  if (!is_open.value) return
  is_open.value = false
  if (id == cardid.value && cardUrl.value) {
    proxy.$refs.cardContent.open()
  } else {
    cardid.value = id
    cardUrl.value = ''
    proxy.$refs.popCardBox.open(id, type)
  }
}

const openFail = () => {
  is_open.value = true
}

const saveTpl = id => {
  saveTgPoster({
    tgtype: tgtype.value,
    mbid: id,
  })
}

//生成海报
let generateCard = () => {
  nextTick(() => {
    let imgWidth = proxy.$refs.cardBox.offsetWidth || 375
    let imgHeight = proxy.$refs.cardBox.offsetHeight || 667
    OEUI.loading.show('海报生成中')
    let el = document.querySelector('#popCardBox')
    html2canvas(el, {
      scale: 2,
      allowTaint: true,
      useCORS: true,
      width: imgWidth,
      height: imgHeight,
      imageTimeout: 5000,
      backgroundColor: '#fff', //设置背景颜色
    })
      .then(canvas => {
        OEUI.loading.hide()
        let url = canvas.toDataURL('image/png')
        cardUrl.value = url
        nextTick(() => {
          proxy.$refs.cardContent.open()
        })
        if (cardid.value) saveTpl(cardid.value)
      })
      .catch(() => {
        is_open.value = true
        OEUI.loading.hide()
        OEUI.toast('生成推广海报失败')
      })
  })
}
const closeCard = () => {
  proxy.$refs.cardContent.close(() => {
    is_open.value = true
  })
}

const cancelCardUrl = callback => {
  cardUrl.value = ''
  if (callback) callback()
}

defineExpose({
  open,
  cancelCardUrl,
})
</script>
<style lang="scss" scoped>
.popCardBox {
  width: 375px;
  height: 667px;
}
.cardContent {
  font-family: PingFang SC, PingFang SC;
  .content {
    width: 6.4rem;
    height: 11.3835rem;
    border-radius: 0.32rem;
    overflow: hidden;
    background: #fff;
    img {
      width: 100%;
      object-fit: cover;
      border-radius: 0.32rem;
    }
  }
  .tips {
    color: #fff;
    text-align: center;
    font-size: 0.3733rem;

    font-weight: normal;
    line-height: 0.5333rem;
    margin-top: 0.32rem;
  }
  .btn {
    span {
      background: #fff;
      font-size: 0.3733rem;
      font-weight: normal;
      color: $color_main;
      line-height: 0.5333rem;
      padding: 0.16rem 0.5067rem;
      border-radius: 0.4267rem;
      cursor: pointer;
      margin-top: 0.64rem;
    }
    .close {
      font-size: 0.5867rem;
      color: #fff;
      cursor: pointer;
      margin-top: 0.64rem;
    }
  }
}
</style>

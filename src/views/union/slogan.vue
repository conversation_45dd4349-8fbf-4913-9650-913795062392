<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="back" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc flex_1">
      <p @click="changeTgtype('user')" :class="{ current: tgtype == 'user' }" style="margin-right: 0.64rem">邀请单身</p>
      <p @click="changeTgtype('union')" :class="{ current: tgtype == 'union' }">邀请红娘</p>
    </div>
  </div>
  <div class="h44"></div>
  <oeui-list :top="0" @refresh="refresh" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
    <div class="main" v-if="list.length">
      <div class="item" v-for="item in list" :key="item.wdid">
        <div class="data">
          <div>
            <span>
              <img src="@/assets/images/slogan.png" alt="" />
            </span>
            <div>
              <p class="id">编号:{{ item.wdid }}</p>
              <p class="time">{{ getTime(item.addtime) }}</p>
            </div>
          </div>
          <span @click="copy(item.content)">复制文案</span>
        </div>
        <div class="content">{{ item.content }}</div>
      </div>
    </div>
  </oeui-list>

  <oe_popup ref="copysuccess_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="copysuccess_dialog">
      <p class="tit">复制成功</p>
      <p class="tips">请到朋友圈或群粘贴</p>
      <div @click="proxy.$refs.copysuccess_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import oeuiList from '@/oeui/list.vue'
import { useRoute, useRouter } from 'vue-router'
import { getTgSloganAll } from '@/api/union.js'
import { getTime } from '@/utils/hooks.js'
import oe_popup from '@/oeui/popup.vue'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const route = useRoute()
const router = useRouter()
const tgtype = ref(route.query.type)
const list = ref([])
const page = ref(1)
const pageCount = ref('0')
const listStatus = ref('loading')
const changeTgtype = val => {
  tgtype.value = val
  slogan.value = ''
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getSlogan(true)
}

const slogan = ref('')

const getSlogan = (flag, callback) => {
  if (page.value == 0) return
  getTgSloganAll({
    s_type: tgtype.value == 'user' ? 1 : 2,
    page: page.value,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const copy = data => {
  let elInput = document.createElement('textarea')
  elInput.value = data
  document.body.appendChild(elInput)
  elInput.select()
  document.execCommand('Copy')
  proxy.$refs.copysuccess_dialog.open()
  slogan.value = data
  elInput.remove()
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getSlogan(true, done)
}
const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

getSlogan()

const back = () => {
  router.replace({
    path: '/poster',
    query: {
      type: tgtype.value,
      slogan: slogan.value,
    },
  })
}
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
  .title {
    font-size: 0.3733rem;
    line-height: 0.64rem;
    color: #666;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    .current {
      font-size: 0.5333rem;
      line-height: 0.7467rem;
      color: #2a2546;
      font-weight: 500;
      position: relative;
      top: -0.0533rem;
      &::after {
        position: absolute;
        content: '';
        height: 0.08rem;
        width: 0.32rem;
        background: $color_main;
        border-radius: 0.0533rem;
        left: 50%;
        transform: translateX(-50%);
        bottom: -0.1333rem;
      }
    }
  }
}
.main {
  padding: 0.32rem 0.4267rem;
  font-family: PingFang SC, PingFang SC;
  padding-bottom: 1.3333rem;
  .item {
    background: #fff;
    border-radius: 0.32rem;
    box-sizing: border-box;
    margin-bottom: 0.32rem;
    .data {
      padding: 0.32rem;
      border-bottom: 0.0267rem solid #f2f4f5;
      display: flex;
      align-items: center;
      justify-content: space-between;
      > div {
        display: flex;
        align-items: center;
        span {
          width: 0.8533rem;
          height: 0.8533rem;
          margin-right: 0.2133rem;
          flex-shrink: 0;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .id {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-weight: normal;
          color: #31293b;
        }
        .time {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-weight: normal;
          color: #999;
        }
      }
      > span {
        background: $color_main;
        color: #fff;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: normal;
        padding: 0.16rem 0.4267rem;
        border-radius: 0.5333rem;
        cursor: pointer;
      }
    }
    .content {
      padding: 0.32rem;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      color: #31293b;
      font-weight: normal;
    }
  }
}

.copysuccess_dialog {
  text-align: center;
  padding: 0.64rem 1.2533rem;
  border-radius: 0.64rem;
  overflow: hidden;
  .tit {
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-weight: 600;
  }

  .tips {
    margin-top: 0.4267rem;
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #666666;
    line-height: 0.5867rem;
  }
  .event {
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin-top: 0.64rem;
    padding: 0.24rem 0;
  }
}
</style>

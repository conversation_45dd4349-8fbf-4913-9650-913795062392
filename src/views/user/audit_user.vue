<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">审核素材</div>
    <span @click="btn_search" class="more iconfont icon-shaixuan" style="font-size: 0.48rem"></span>
  </div>
  <div class="h44"></div>
  <div class="list">
    <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <audit_item :item="item" v-for="item in list" :key="item.userid" />
    </oeui-list>
  </div>
  <user_card ref="userCard" :id="userid" />
  <user_search ref="userSearch" @sendSearch="sendSearch" />
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import oeuiList from '@/oeui/list.vue'
import audit_item from '@/views/index/components/audit_item.vue'
import user_card from '@/views/user/components/user_card.vue'
import user_search from './components/user_search.vue'
import { getAuditUser } from '@/api/audit_user.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')
const state = ref({})

const getList = (flag, callback) => {
  if (page.value == 0) return
  getAuditUser({
    page: page.value,
    ...state.value,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}
const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

const btn_search = () => {
  proxy.$refs.userSearch.open()
}

const sendSearch = data => {
  for (let key in data) {
    state.value['s_' + key] = data[key]
  }
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}
getList()
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.list {
  padding: 0.1067rem 0.4267rem;
}
</style>

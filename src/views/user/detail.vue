<!-- @format -->

<template>
  <div class="mian">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">用户资料</div>
    </div>
    <div class="h44"></div>
    <div class="content">
      <div class="user flex flex_ac">
        <div class="head flex_s">
          <img v-lazy="infoData.headimg_m1_url" type="user" v-if="infoData.headimg" alt="" />
          <template v-else>
            <img v-if="infoData.gender == 1" src="@/assets/images/gender_1.png" alt="" />
            <img v-else-if="infoData.gender == 2" src="@/assets/images/gender_2.png" alt="" />
            <img v-else src="@/assets/images/gender_0.png" alt="" />
          </template>
        </div>
        <div class="flex_1">
          <p class="name">{{ infoData.username }}</p>
          <div class="flex flex_ac flex_jsb" style="margin-top: 0.0533rem">
            <div class="id flex flex_ac">
              <i class="iconfont icon-nanbiao-01" v-if="infoData.gender == 1"></i>
              <i class="iconfont icon-nvbiao-01" v-else></i>
              编号: {{ infoData.userid }}
            </div>
            <p class="addtime">注册时间：{{ getTime(infoData.addtime, true) }}</p>
          </div>
        </div>
      </div>
      <div class="lab flex flex_ac">
        <span @click="changView('info')" :class="is_active == 'info' ? 'current' : ''">基本资料</span>
        <span @click="changView('cond')" :class="is_active == 'cond' ? 'current' : ''">择偶条件</span>
        <span v-if="route.query.type == 'crm'" @click="changView('receipt')" :class="is_active == 'receipt' ? 'current' : ''">收款单</span>
      </div>
      <div>
        <user_info :item="infoData" v-if="is_active == 'info'" />
        <user_cond :item="infoCond" v-if="is_active == 'cond'" />
        <user_receipt v-if="is_active == 'receipt'" />
      </div>
    </div>
  </div>
  <div class="h70"></div>
  <div class="share flex_dc">
    <span @click="btnShare" class="flex_dc">
      <i class="iconfont icon-fenxiangfangshi"></i>
      推广分享
    </span>
  </div>
  <oe_popup ref="shareDialog" mode="bottom" roundStyle=".64rem" round="true">
    <div class="shareDialog">
      <p class="title">分享给好友</p>
      <div class="channel">
        <div @click="openwxshare">
          <span>
            <img src="@/assets/images/weixin_share.png" alt="" />
          </span>
          <p>微信好友</p>
        </div>
        <div
          @click="
            $router.push({
              path: '/user/share',
              query: {
                id: route.query.id,
              },
            })
          "
        >
          <span>
            <img src="@/assets/images/other_share.png" alt="" />
          </span>
          <p>海报文案</p>
        </div>
      </div>
      <span @click="proxy.$refs.shareDialog.close()" class="close iconfont icon-cha"></span>
    </div>
  </oe_popup>

  <pop_custom ref="wxsharepopup" :bgrount="false" bg="none">
    <div @click="proxy.$refs.wxsharepopup.close()" style="height: 100vh; background: rgba(0, 0, 0, 0.45)">
      <img style="width: 50vw; right: 0.2667rem; top: 0.2667rem" class="pa" src="@/assets/images/jian.png" alt="" />
    </div>
  </pop_custom>
</template>

<script setup>
import { ref, getCurrentInstance, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { getUserDetail } from '@/api/user.js'
import { isWeiXin, setWechatShare } from '@/utils/jssdk'
import pop_custom from '@/components/pop_custom.vue'
import { getTime } from '@/utils/hooks.js'
import oe_popup from '@/oeui/popup.vue'
import user_info from './info.vue'
import user_cond from './cond.vue'
import user_receipt from './receipt.vue'

const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const config = computed(() => store.state.config)

const route = useRoute()
const router = useRouter()
const infoData = ref({})
const infoCond = ref({})
const slogan = ref('')
const share_url = ref('')

const getDetail = () => {
  getUserDetail({
    id: route.query.id,
  }).then(res => {
    console.log(res.result);

    if (res.ret == 1) {
      share_url.value = res.result.url
      infoData.value = res.result.data
      infoCond.value = res.result.cond
      slogan.value = res.result.word.content
      if (isWeiXin()) setWechatShare(res.result.share_data)
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_active = ref('info')

const changView = val => {
  is_active.value = val
}

const btnShare = () => {
  if (isWeiXin()) {
    proxy.$refs.shareDialog.open()
    return
  }
  router.push('/user/share?id=' + route.query.id)
}

const openwxshare = () => {
  proxy.$refs.shareDialog.close()
  proxy.$refs.wxsharepopup.open()
}

getDetail()

watch(
  () => store.state.config,
  newInfo => {
    if (newInfo) {
      config.value = newInfo
    }
  },
  { immediate: true, deep: true }
)
</script>

<style lang="scss" scoped>
.mian {
  .top_nav {
    background: url('~@/assets/images/bg_main.png') no-repeat;
    background-size: cover;
  }
  .content {
    padding: 0 0.4267rem;
    .user {
      padding-left: 0.2133rem;
      .head {
        width: 1.3867rem;
        height: 1.3867rem;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 0.32rem;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .name {
        font-size: 0.4267rem;
        line-height: 0.5867rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        color: #31293b;
      }
      .id {
        font-size: 0.32rem;
        line-height: 0.4533rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #666666;
        i {
          position: relative;
          font-size: 0.4267rem;
          margin-right: 0.0533rem;
        }
        .icon-nanbiao-01 {
          color: #0570f1;
        }
        .icon-nvbiao-01 {
          color: #fe6897;
        }
      }
      .addtime {
        font-size: 0.32rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
    }
    .lab {
      margin-top: 0.4267rem;
      margin-bottom: 0.2133rem;
      height: 1.1733rem;
      span {
        margin-right: 0.64rem;
        font-size: 0.3733rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #666666;
        line-height: 0.5333rem;
        position: relative;
        top: 2px;
        &.current {
          font-size: 0.48rem;
          font-weight: 500;
          color: #3d3d3d;
          line-height: 0.6667rem;
          top: 0;
        }
      }
    }
  }
}
.share {
  position: fixed;
  width: 100%;
  height: 1.6rem;
  bottom: 0.2667rem;
  z-index: 300;
  span {
    cursor: pointer;
    i {
      margin-right: 0.2667rem;
    }
    background: $color_main;
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #ffffff;
    line-height: 0.5867rem;
    padding: 0.2933rem 1.0667rem;
    border-radius: 0.5867rem;
  }
}

.linkDialog {
  font-family: PingFang SC, PingFang SC;
  padding: 0.5867rem 0.4267rem 0.64rem 0.4267rem;
  .tit {
    span {
      font-size: 0.48rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      color: #31293b;
      line-height: 0.6667rem;
    }
    i {
      cursor: pointer;
      color: #000;
      font-size: 0.48rem;
    }
  }
  .slogan {
    margin-top: 0.3467rem;
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #31293b;
    line-height: 0.64rem;
  }
  .refresh {
    transform: all 0.3s;
    color: #0570f1;
    i {
      margin-right: 0.1333rem;
    }
    margin-top: 0.4267rem;
    display: flex;
    align-items: center;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #0570f1;
    line-height: 0.64rem;
  }
  .data {
    margin-top: 0.5333rem;
    background: #f2f4f5;
    border-radius: 0.32rem;
    padding: 0.4267rem;
    margin-bottom: 0.4267rem;
    display: flex;
    flex-wrap: wrap;
    span {
      width: 50%;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #31293b;
      line-height: 0.64rem;
    }
  }
  .tips {
    font-size: 0.32rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #666666;
    line-height: 0.64rem;
  }
  .btn {
    margin-top: 0.64rem;
    span {
      cursor: pointer;
      font-size: 0.4267rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      color: #ffffff;
      line-height: 0.5867rem;
      background: $color_main;
      padding: 0.24rem 2.3467rem;
      border-radius: 0.5333rem;
    }
  }
}
.linkIntro {
  padding: 0.5867rem 0.4267rem 0.64rem 0.4267rem;
  .title {
    font-size: 0.48rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #31293b;
    line-height: 0.6667rem;
    i {
      font-weight: 600;
      font-size: 0.48rem;
      margin-right: 0.0533rem;
    }
  }
  .tips {
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #666666;
    line-height: 0.64rem;
    padding-left: 0.5333rem;
  }
  .tit {
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #999999;
    line-height: 0.64rem;
    margin: 0.4267rem;
  }
  .data {
    margin-top: 0.5333rem;
    background: #f2f4f5;
    border-radius: 0.32rem;
    padding: 0.4267rem;
    margin-bottom: 0.4267rem;
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #31293b;
    line-height: 0.64rem;
  }
}
.refresh_anime {
  transform: rotate(100deg);
}
</style>

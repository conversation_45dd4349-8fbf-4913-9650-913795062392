<!-- @format -->

<template>
  <!-- <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">编辑资料</div>
    </div>
    <div class="h44"></div>
    <div class="menu">
      <div class="item" @click="openItemPicker('unionname')">
        <span class="name">昵称</span>
        <div class="event">
          <span class="value" v-if="info.unionname">{{ info.unionname }}</span>
          <span v-else>请填写</span>
          <i class="iconfont icon-you"></i>
        </div>
      </div>

      <div class="item">
        <span class="name">手机</span>
        <div class="event">
          <span class="value" v-if="info.mobile">{{ info.mobile }}</span>
          <span v-else>未绑定</span>
          <i class="iconfont icon-you" v-if="!info.mobile"></i>
        </div>
      </div>
      <div class="item" @click="openItemPicker('weixin')">
        <span class="name">微信</span>
        <div class="event">
          <span class="value" v-if="info.weixin">{{ info.weixin }}</span>
          <span v-else>修改</span>
          <i class="iconfont icon-you"></i>
        </div>
      </div>

      <div class="item" @click="$router.push('/edit/password')">
        <span class="name">密码</span>
        <div class="event">
          <span>修改</span>
          <i class="iconfont icon-you"></i>
        </div>
      </div>
      <div class="item" @click="openItemPicker('area')">
        <span class="name">{{ config.dist_title || '居住地' }}</span>
        <div class="event">
          <span class="value" v-if="info.area1 > 0">
            {{ info.area1_t }}
            {{ info.area2_t }}
            {{ info.area3_t }}
            {{ info.area4_t }}
          </span>
          <span v-else>修改</span>
          <i class="iconfont icon-you"></i>
        </div>
      </div>
      <div class="item">
        <span class="name">注册时间</span>
        <div class="event">
          <span class="value" v-if="unionInfo.addtime > 0">{{ getTime(unionInfo.addtime || 0) }}</span>
        </div>
      </div>
    </div>
  </div> -->

  <div class="main">
    <div class="pf top_nav flex_dc" style="background: #f2f4f5">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">编辑资源</div>
    </div>
    <div class="h44"></div>
    <div class="content">
      <p class="tit">基础资料</p>
      <div class="module">
        <!-- <div class="item" @click="openItemPicker('nickname')">
          <span class="name">
            <font color="red">*</font>
            昵称
          </span>
          <span class="value" v-if="state.nickname">
            <p>{{ state.nickname }}</p>
          </span>
          <span v-else>请输入</span>
        </div>

        <div class="item">
          <span class="name">
            <font color="red">*</font>
            性别
          </span>
          <div class="gender">
            <span @click="changeGender(1)" :class="state.gender == 1 ? 'current' : ''" style="border-right: 0.0267rem solid #f2f4f5">男</span>
            <span :class="state.gender == 2 ? 'current' : ''" @click="changeGender(2)">女</span>
          </div>
        </div> -->
        <div class="item" @click="openItemPicker('age')">
          <span class="name">生日</span>
          <span class="value" v-if="state.ageyear > 0">
            {{ state.ageyear }}-{{ state.agemonth }}-{{ state.ageday }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('lunar')">
          <span class="name">生肖</span>
          <span class="value" v-if="state.lunar > 0">
            {{ stateText.lunar }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('astro')">
          <span class="name">星座</span>
          <span class="value" v-if="state.astro > 0">
            {{ stateText.astro }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('marry')">
          <span class="name">婚况</span>
          <span class="value" v-if="state.marry > 0">
            {{ stateText.marry }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('education')">
          <span class="name">学历</span>
          <span class="value" v-if="state.education > 0">
            {{ stateText.education }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('salary')">
          <span class="name">年收入</span>
          <span class="value" v-if="state.salary > 0">
            {{ stateText.salary }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('area')">
          <span class="name">{{ config.dist_title || '居住地' }}</span>
          <span class="value" v-if="state.dist1 > 0">
            <p>{{ stateText.dist1 }} {{ stateText.dist2 }} {{ stateText.dist3 }} {{ stateText.dist4 }}</p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('home')">
          <span class="name">{{ config.home_title || '户籍地' }}</span>
          <span class="value" v-if="state.home1 > 0">
            <p>{{ stateText.home1 }}{{ stateText.home2 }} {{ stateText.home3 }} {{ stateText.home4 }}</p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>

        <div class="item" @click="openItemPicker('height')">
          <span class="name">身高</span>
          <span class="value" v-if="state.height">
            {{ state.height }}cm
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('weight')">
          <span class="name">体重</span>
          <span class="value" v-if="state.weight">
            {{ state.weight }}kg
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>

        <div class="item" @click="openItemPicker('job')">
          <span class="name">职业</span>
          <span class="value" v-if="state.job">
            {{ stateText.job }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>

        <div class="item" @click="openItemPicker('house')">
          <span class="name">购房</span>
          <span class="value" v-if="state.house">
            {{ stateText.house }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>

        <!-- 购车 -->
        <div class="item" @click="openItemPicker('car')">
          <span class="name">购车</span>
          <span class="value" v-if="state.car">
            {{ stateText.car }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
      </div>
    </div>
    <div class="h44"></div>
  </div>

  <!-- <edit-text ref="nicknamePicker" title="昵称" inputText="请输入昵称" @next="sendNickname" :current="state.nickname"></edit-text> -->

  <oeui-time ref="agePicker" :start="18" :end="70" :current="[Number(state.ageyear), Number(state.agemonth), Number(state.ageday)]" @confirm="sendAge"></oeui-time>

  <oeui-picker ref="lunarPicker" title="生肖" :list="listState.lunarList" :current="[Number(state.lunar)]" v-if="listState.lunarList.length > 0" @confirm="sendLunar"></oeui-picker>
  <oeui-picker ref="astroPicker" title="星座" :list="listState.astroList" :current="[Number(state.astro)]" v-if="listState.astroList.length > 0" @confirm="sendAstro"></oeui-picker>

  <oeui-picker ref="marryPicker" title="婚况" :list="listState.marryList" :current="[Number(state.marry)]" v-if="listState.marryList.length > 0" @confirm="sendMarry"></oeui-picker>
  <oeui-picker ref="educationPicker" title="学历" :list="listState.educationList" :current="[Number(state.education)]" v-if="listState.educationList.length > 0" @confirm="sendEducation" />
  <oeui-picker ref="salaryPicker" title="年收入" :list="listState.salaryList" :current="[Number(state.salary)]" v-if="listState.salaryList.length > 0" @confirm="sendSalary"></oeui-picker>

  <oeui-picker ref="heightPicker" title="身高" :list="listState.heightList" :current="[Number(state.height)]" v-if="listState.heightList.length > 0" @confirm="sendHeight"></oeui-picker>
  <oeui-picker ref="weightPicker" title="体重" :list="listState.weightList" :current="[Number(state.weight)]" v-if="listState.weightList.length > 0" @confirm="sendWeight"></oeui-picker>
  <oeui-picker ref="jobPicker" title="职业" :list="listState.jobList" :current="[Number(state.job)]" v-if="listState.jobList.length > 0" @confirm="sendJob"></oeui-picker>
  <oeui-picker ref="housePicker" title="购房" :list="listState.houseList" :current="[Number(state.house)]" v-if="listState.houseList.length > 0" @confirm="sendHouse"></oeui-picker>
  <oeui-picker ref="carPicker" title="购车" :list="listState.carList" :current="[Number(state.car)]" v-if="listState.carList.length > 0" @confirm="sendCar"></oeui-picker>

  <oeui-area ref="areaPicker" :title="config.dist_title || '居住地'" :list="listState.areaList" :current="[state.dist1, state.dist2, state.dist3, state.dist4]" @change="sendArea" v-if="listState.areaList.length > 0"></oeui-area>
  <oeui-area ref="homePicker" :title="config.home_title || '户籍地'" :list="listState.homeList" :current="[state.home1, state.home2, state.home3, state.home4]" @change="sendHome" v-if="listState.homeList.length > 0"></oeui-area>
</template>

<script setup>
import { ref, getCurrentInstance, reactive, nextTick, watch, computed } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'

import oeuiArea from '@/oeui/area.vue'
import oeuiTime from '@/oeui/datetime.vue'
import oeuiPicker from '@/oeui/picker.vue'

import { getDistPicker, getPickerData, getHomePicker } from '@/utils/main.js'
import { getUserDetail, saveage, savedist, savehometown, saveauto } from '@/api/user.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const route = useRoute()
const unionInfo = ref({})
const config = computed(() => store.state.config)

const info = ref({})

const listState = reactive({
  ageList: [],
  lunarList: [],
  astroList: [],
  marryList: [],
  educationList: [],
  salaryList: [],
  areaList: [],
  homeList: [],
  heightList: [],
  weightList: [],
  nationalList: [],
  jobList: [],

  ageCondList: [],
  heightCondList: [],
  weightCondList: [],
  marryCondList: [],
  educationCondList: [],
  salaryCondList: [],
  houseCondList: [],
  carCondList: [],
  childCondList: [],
  lunarCondList: [],
  astroCondList: [],
  distCondList: [],
  houseList: [],
  carList: [],
})

const state = reactive({
  nickname: '',
  mobile: '',
  gender: '',
  ageyear: '',
  agemonth: '',
  ageday: '',
  lunar: '',
  astro: '',
  dist1: '',
  dist2: '',
  dist3: '',
  dist4: '',
  home1: '',
  home2: '',
  home3: '',
  home4: '',
  marry: '',
  job: '',
  house: '',
  car: '',
  education: '',
  salary: '',
  height: '',
  weight: '',
  school: '',
  national: '',
  cond_age1: '',
  cond_age2: '',
  cond_height1: '',
  cond_height2: '',
  cond_weight1: '',
  cond_weight2: '',
  cond_marry: '',
  cond_education: '',
  cond_salary: '',
  cond_house: '',
  cond_car: '',
  cond_child: '',
  cond_lunar: '',
  cond_astro: '',
  cond_dist1: '',
  cond_dist2: '',
  cond_dist3: '',
  cond_dist4: '',
})

const stateText = reactive({
  lunar: '',
  astro: '',
  marry: '',
  education: '',
  salary: '',
  dist1: '',
  dist2: '',
  dist3: '',
  dist4: '',
  home1: '',
  home2: '',
  home3: '',
  home4: '',
  job: '',
  house: '',
  car: '',
  national: '',
  cond_marry: '',
  cond_education: '',
  cond_salary: '',
  cond_house: '',
  cond_car: '',
  cond_child: '',
  cond_lunar: '',
  cond_astro: '',
  cond_dist1: '',
  cond_dist2: '',
  cond_dist3: '',
  cond_dist4: '',
})

const dataList = ['headimg_url', 'unionname', 'mobile', 'weixin', 'wxcode_url', 'area1', 'area1_t', 'area2', 'area2_t', 'area3', 'area3_t', 'area4', 'area4_t']

const openItemPicker = item => {
  let text = item.toLowerCase()
  let condText = text.replace('cond', '')
  if (text.includes('cond')) {
    if (condText == 'area') {
      return getDistDataCond()
    } else if (listState[condText + 'CondList'] && listState[condText + 'CondList'].length == 0) {
      return getItemDataCond(condText)
    }
    nextTick(() => {
      proxy.$refs[item + 'Picker'].open()
    })
    return
  }
  if (item == 'school' || item == 'nickname' || item == 'mobile' || item == 'age') {
    return proxy.$refs[item + 'Picker'].open()
  } else if (item == 'area') {
    return getDistData()
  } else if (item == 'home') {
    return getHomeData()
  } else if (listState[text + 'List'] && listState[text + 'List'].length == 0) {
    return getItemData(text)
  }
  proxy.$refs[item + 'Picker'].open()
}

const getItemDataCond = (item, limit = true) => {
  //获取picker数据
  const p = getPickerData(item, limit)
  p.then(res => {
    if (res.ret == 1) {
      listState[item + 'CondList'] = res.result
      nextTick(() => {
        proxy.$refs[`cond${titleUpperCase(item)}Picker`].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
  return p
}

const titleUpperCase = str => {
  return str.slice(0, 1).toUpperCase() + str.slice(1).toLowerCase()
}

const getItemData = (item, limit = false) => {
  //获取picker数据
  const p = getPickerData(item, limit)
  p.then(res => {
    if (res.ret == 1) {
      listState[item + 'List'] = res.result
      nextTick(() => {
        proxy.$refs[item + 'Picker'].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
  return p
}

const getDistDataCond = () => {
  //获取居住地数据
  getDistPicker().then(res => {
    if (res.ret == 1) {
      listState.distCondList = res.result
      nextTick(() => {
        proxy.$refs['condDistPicker'].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
}
const getHomeData = () => {
  //获取户籍地数据
  getHomePicker().then(res => {
    if (res.ret == 1) {
      listState.homeList = res.result
      nextTick(() => {
        proxy.$refs['homePicker'].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
}

const sendAge = val => {
  state.ageyear = val[0]?.value || ''
  state.agemonth = val[1]?.value || ''
  state.ageday = val[2]?.value || ''

  saveage({
    id: route.query.id,
    ageyear: val[0]?.value || '',
    agemonth: val[1]?.value || '',
    ageday: val[2]?.value || '',
  }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}

const sendLunar = val => {
  state.lunar = val[0].value
  stateText.lunar = val[0].text
  saveauto({ id: route.query.id, item: 'lunar', value: val[0].value }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}
const sendAstro = val => {
  state.astro = val[0].value
  stateText.astro = val[0].text
  saveauto({ id: route.query.id, item: 'astro', value: val[0].value }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}
const sendMarry = val => {
  state.marry = val[0].value
  stateText.marry = val[0].text
  saveauto({ id: route.query.id, item: 'marry', value: val[0].value }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}
const sendEducation = val => {
  state.education = val[0].value
  stateText.education = val[0].text
  saveauto({ id: route.query.id, item: 'education', value: val[0].value }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}
const sendSalary = val => {
  state.salary = val[0].value
  stateText.salary = val[0].text
  saveauto({ id: route.query.id, item: 'salary', value: val[0].value }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}

const sendArea = val => {
  state.dist1 = val[0]?.value || ''
  stateText.dist1 = val[0]?.text || ''
  state.dist2 = val[1]?.value || ''
  stateText.dist2 = val[1]?.text || ''
  state.dist3 = val[2]?.value || ''
  stateText.dist3 = val[2]?.text || ''
  state.dist4 = val[3]?.value || ''
  stateText.dist4 = val[3]?.text || ''

  savedist({
    id: route.query.id,
    dist1: val[0]?.value || '',
    dist2: val[1]?.value || '',
    dist3: val[2]?.value || '',
    dist4: val[3]?.value || '',
  }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}

const sendHome = val => {
  state.home1 = val[0]?.value || ''
  stateText.home1 = val[0]?.text || ''
  state.home2 = val[1]?.value || ''
  stateText.home2 = val[1]?.text || ''
  state.home3 = val[2]?.value || ''
  stateText.home3 = val[2]?.text || ''
  state.home4 = val[3]?.value || ''
  stateText.home4 = val[3]?.text || ''

  savehometown({
    id: route.query.id,
    home1: val[0]?.value || '',
    home2: val[1]?.value || '',
    home3: val[2]?.value || '',
    home4: val[3]?.value || '',
  }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}

const sendHeight = val => {
  state.height = val[0].value
  saveauto({ id: route.query.id, item: 'height', value: val[0].value }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}
const sendWeight = val => {
  state.weight = val[0].value
  saveauto({ id: route.query.id, item: 'weight', value: val[0].value }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}
const sendJob = val => {
  state.job = val[0].value
  stateText.job = val[0].text
  saveauto({ id: route.query.id, item: 'job', value: val[0].value }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}
const sendHouse = val => {
  state.house = val[0].value
  stateText.house = val[0].text
  saveauto({ id: route.query.id, item: 'house', value: val[0].value }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}
const sendCar = val => {
  state.car = val[0].value
  stateText.car = val[0].text
  saveauto({ id: route.query.id, item: 'car', value: val[0].value }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}

const initState = info => {
  state.id = info.mid
  state.nickname = info.username
  state.gender = info.gender
  state.ageyear = info.ageyear
  state.agemonth = info.agemonth
  state.ageday = info.ageday
  state.lunar = info.lunar
  stateText.lunar = info.lunar_t
  state.astro = info.astro
  stateText.astro = info.astro_t
  state.dist1 = info.dist1
  stateText.dist1 = info.dist1_t
  state.dist2 = info.dist2
  stateText.dist2 = info.dist2_t
  state.dist3 = info.dist3
  stateText.dist3 = info.dist3_t
  state.dist4 = info.dist4
  stateText.dist4 = info.dist4_t

  state.home1 = info.home1
  stateText.home1 = info.home1_t
  state.home2 = info.home2
  stateText.home2 = info.home2_t
  state.home3 = info.home3
  stateText.home3 = info.home3_t
  state.home4 = info.home4
  stateText.home4 = info.home4_t
  state.marry = info.marry
  stateText.marry = info.marry_t
  state.education = info.education
  stateText.education = info.education_t
  state.salary = info.salary
  stateText.salary = info.salary_t
  state.height = info.height
  stateText.height = info.height_t
  state.weight = info.weight
  stateText.weight = info.weight_t

  state.job = info.job
  stateText.job = info.job_t
  state.house = info.house
  stateText.house = info.house_t
  state.car = info.car
  stateText.car = info.car_t
}

const getDetail = () => {
  getUserDetail({
    id: route.query.id,
  }).then(res => {
    if (res.ret == 1) {
      initState(res.result.data)
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const getDistData = () => {
  //获取居住地数据
  getDistPicker().then(res => {
    if (res.ret == 1) {
      listState.areaList = res.result
      nextTick(() => {
        proxy.$refs['areaPicker'].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
}

getDetail()

watch(
  () => store.state.unionInfo,
  newInfo => {
    if (newInfo) {
      unionInfo.value = newInfo
      dataList.forEach(v => {
        info.value[v] = newInfo[v] || ''
      })
    }
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.main {
  position: absolute;
  width: 100vw;
  min-height: 100vh;
  background: #f7f8fa;

  .menu {
    padding: 0 0.4267rem;

    .item {
      height: 1.28rem;
      margin: 0.0533rem 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .name {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #31293b;
      }

      .event {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.3733rem;
        line-height: 0.4533rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #999999;

        .head {
          width: 0.64rem;
          height: 0.64rem;
          border-radius: 50%;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        //.value {
        //  color: #2a2546;
        //}
        i {
          font-size: 0.32rem;
          position: relative;
          top: 0.0267rem;
          margin-left: 0.1067rem;
          color: #666666;
        }
      }
    }
  }
}

.login_out {
  position: fixed;
  width: 100%;
  bottom: 0.8rem;
  font-size: 0.4267rem;
  cursor: pointer;

  span {
    color: #31293b;
    background: #fff;
    padding: 0.2133rem 0.64rem;
    border-radius: 0.4267rem;
    cursor: pointer;
  }
}

.weixin_slot {
  i {
    font-size: 0.5333rem;
    margin-right: 0.08rem;
    color: #999;

    &.current {
      color: $color_main;
    }
  }

  span {
    font-size: 0.3733rem;
    font-weight: normal;
    line-height: 0.5333rem;
    color: #666;
  }
}
</style>
<style lang="scss" scoped>
.main {
  position: absolute;
  width: 100vw;
  min-height: 100vh;
  background: #f2f4f5;

  .content {
    padding: 0 0.4267rem;
    font-family: PingFang SC, PingFang SC;

    .tit {
      font-size: 0.4267rem;
      font-weight: 500;
      color: #31293b;
      line-height: 0.5867rem;
      padding: 0.32rem 0;
    }

    .module {
      background: #fff;
      padding: 0.32rem 0.4267rem;
      border-radius: 0.32rem;

      .item {
        height: 1.1733rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #c5c3c7;
        font-size: 0.3733rem;
        font-weight: normal;
        line-height: 0.5333rem;

        .name {
          color: #666666;
        }

        .value {
          color: #31293b;
          display: flex;
          align-items: center;

          p {
            max-width: 4.5333rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }

        input {
          text-align: right;
        }

        i {
          color: #000;
          position: relative;
          top: 0.0267rem;
        }

        .gender {
          font-size: 0.3733rem;
          font-weight: normal;
          color: #c5c3c7;
          line-height: 0.5333rem;
          border: 0.0267rem solid #f2f4f5;
          border-radius: 0.3733rem;
          overflow: hidden;

          span {
            padding: 0.1067rem 0.32rem;

            &.current {
              background: $color_main;
              color: #fff;
            }
          }
        }
      }
    }
  }
}
</style>

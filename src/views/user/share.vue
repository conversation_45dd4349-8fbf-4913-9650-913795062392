<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">分享用户</div>
    <span @click="openGuidance" class="pa flex_dc" style="right: 0 !important; width: 1.1733rem; height: 1.1733rem">
      <i class="iconfont icon-wenhaoxiao color_main" style="font-size: 0.48rem"></i>
    </span>
  </div>
  <div class="h44"></div>
  <div class="main">
    <div class="data ws">
      <div class="item">
        <span>昵称：</span>
        <p>{{ infoData.username }}</p>
      </div>
      <div class="item">
        <span>性别：</span>
        <p>{{ infoData.gender_t }}</p>
      </div>
      <div class="item">
        <span>年龄：</span>
        <p>{{ infoData.age }}岁</p>
      </div>
      <div class="item">
        <span>{{ config.dist_title || '居住地' }}：</span>
        <p class="ws">
          {{ infoData.details_dist_t }}
        </p>
      </div>
      <div class="item">
        <span>职业：</span>
        <p>{{ infoData.job > 0 ? infoData.job_t : '无' }}</p>
      </div>
    </div>
    <div class="slogan">
      <div class="title flex flex_ac flex_jsb">
        <span class="name">发圈素材</span>
        <span class="updata flex flex_ac" @click="proxy.$refs.sloganMore.open()">
          <i class="iconfont icon-quanbuhuati"></i>
          全部文案
        </span>
      </div>
      <p class="tips">· 复制文案粘贴后会自动附上用户链接，文案可修改。</p>
      <div class="intro flex flex_v">
        <p>{{ slogan }}</p>
        <div class="btn">
          <span @click="openLink">生成链接</span>
          <span class="copy" @click="copy(slogan)">复制文案</span>
        </div>
      </div>
    </div>
    <div class="poster">
      <div class="title">
        <span class="name">海报素材</span>
        <span>(点击预览保存)</span>
      </div>
      <div class="list">
        <template v-for="item in posterList" :key="item.postid">
          <div @click="changCard(item.postid)" class="item" v-if="item.postid">
            <img v-lazy="item.thumbimg_url" alt="" />
          </div>
        </template>
      </div>
    </div>
  </div>

  <oe_popup ref="sloganMore" width="100%">
    <div class="sloganMore">
      <div class="nav flex_dc">
        <span class="back flex_dc" @click="proxy.$refs.sloganMore.close()">
          <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
        </span>
        <span class="title">推广文案</span>
      </div>
      <div class="h44"></div>
      <div class="list">
        <oeui-list :top="0" @refresh="refresh" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
          <div class="item" v-for="item in list" :key="item.wdid">
            <div class="data">
              <div>
                <span>
                  <img src="@/assets/images/slogan.png" alt="" />
                </span>
                <div>
                  <p class="id">编号:{{ item.wdid }}</p>
                  <p class="time">{{ getTime(item.addtime) }}</p>
                </div>
              </div>
              <span @click="copySlogan(item.content)">复制文案</span>
            </div>
            <div class="content">{{ item.content }}</div>
          </div>
          <div style="height: 1.6rem"></div>
        </oeui-list>
      </div>
    </div>
  </oe_popup>

  <oe_popup ref="linkIntro" mode="bottom" roundStyle=".64rem" round="true">
    <div class="linkIntro">
      <div class="title flex flex_ac" @click="proxy.$refs.linkIntro.close()">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
        <p>文案及链接说明</p>
      </div>
      <p class="tips">复制出来的口令可发给微信朋友，或者微信群</p>
      <p class="tit">格式如下：</p>
      <div class="data">
        <!-- 关注公众号 👉 #{{ config.wxpublic_name }} -->
        <!-- <br /> -->
        <br />
        {{ slogan }}
        <div class="flex flex_ac undata" @click="getSlogan(true)">
          <i class="iconfont icon-shuaxin1" :class="is_refresh ? 'refresh_anime' : ''"></i>
          换一个
        </div>
        昵称：{{ infoData.username }}
        <br />
        性别：{{ infoData.gender_t }}
        <br />
        年龄：{{ infoData.age }}岁
        <br />
        职业：{{ infoData.job > 0 ? infoData.job_t : '无' }}
        <br />
        {{ config.dist_title || '居住地' }}：{{ infoData.details_dist_t }}
        <br />
        -----------------------------------
        <br />
        ☎ 服务热线:{{ config.sitetel }}
        <br />
        <br />
        点击下方链接进一步了解
        <br />
        {{ config.siteurl }}
        <br />
      </div>

      <div class="btn" @click="copyLink">复制链接</div>
    </div>
  </oe_popup>

  <oe_popup ref="copysuccess_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="copysuccess_dialog">
      <p class="tit">复制成功</p>
      <p class="tips">请到朋友圈或群粘贴</p>
      <div @click="closeCopyDialog" class="event">知道了</div>
    </div>
  </oe_popup>

  <oe_popup ref="guidanceEl" mode="right" width="100%">
    <div class="pr" style="background: #f4f5f7; width: 100vw; height: 100vh">
      <div class="top_box">
        <img src="@/assets/images/bg_main.png" />
      </div>
      <div class="pf top_nav flex_dc">
        <span @click="closeGuidance" class="back flex_dc">
          <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
        </span>
        <div class="flex_dc" style="font-size: 0.4267rem">如何分享？</div>
      </div>
      <div class="h44"></div>
      <div class="main pr oy" style="z-index: 100; height: calc(100vh - 1.1733rem); padding-bottom: 1.0667rem">
        <div style="width: 100%">
          <img src="@/assets/images/poster/guidance1.png" style="width: 100%; object-fit: cover" />
        </div>
        <div style="font-size: 0.3733rem; line-height: 0.64rem; color: #333; margin-top: 0.3733rem">
          <span class="color_main">生成链接：</span>
          点击生成链接系统会展示模板文案内容以及 推广链接，复制后粘贴转发，用户点击链接自动跳转对 应内容页面。
        </div>
        <div style="font-size: 0.3733rem; line-height: 0.64rem; color: #333; margin-top: 0.3733rem">
          <span class="color_main">复制文案：</span>
          复制展示卡片中文案内容，粘贴转发。
        </div>
        <div style="width: 100%; margin-top: 0.3733rem">
          <img src="@/assets/images/poster/guidance2.png" style="width: 100%; object-fit: cover" />
        </div>
        <div style="font-size: 0.3733rem; line-height: 0.64rem; color: #333; margin-top: 0.3733rem">
          <span class="color_main">海报素材：</span>
          点击查看大图海报，长按保存本地或转发。
        </div>
      </div>
    </div>
  </oe_popup>

  <user_card ref="userCard" :id="route.query.id" />
</template>

<script setup>
import { ref, getCurrentInstance, computed, onMounted } from 'vue'
import { getUserDetail, getTgSlogan, getUserCard, getTgSloganAll } from '@/api/user.js'

import user_card from '@/views/user/components/user_card.vue'
import oe_popup from '@/oeui/popup.vue'
import oeuiList from '@/oeui/list.vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import { getTime } from '@/utils/hooks.js'
const { proxy } = getCurrentInstance()

const OEUI = proxy.OEUI
const store = useStore()
const config = computed(() => store.state.config)

const route = useRoute()
const infoData = ref({})
const infoCond = ref({})
const slogan = ref('')
const share_url = ref('')
const posterList = ref([])

const openGuidance = () => {
  proxy.$refs.guidanceEl.open()
}
const closeGuidance = () => {
  proxy.$refs.guidanceEl.close()
}

const getDetail = () => {
  getUserDetail({
    id: route.query.id,
  }).then(res => {
    if (res.ret == 1) {
      share_url.value = res.result.url
      infoData.value = res.result.data
      infoCond.value = res.result.cond
      slogan.value = res.result.word.content
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const getTplData = () => {
  getUserCard({
    id: route.query.id,
  })
    .then(res => {
      if (res.ret == 1) {
        posterList.value = res.result.all_blindcard
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试',
        })
      }
    })
    .catch(() => {
      OEUI.toast({
        text: '系统繁忙，请稍后再试',
      })
    })
}

const changCard = val => {
  proxy.$refs.userCard.open(val)
}

const openLink = () => {
  proxy.$refs.linkIntro.open()
}
const is_refresh = ref(false)
const getSlogan = flag => {
  if (flag) is_refresh.value = true
  getTgSlogan({
    s_type: '1',
  }).then(res => {
    if (flag) is_refresh.value = false
    if (res.ret == 1) {
      slogan.value = res.result.data.content
    } else {
      if (flag) is_refresh.value = false
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const copyLink = () => {
  let dist = infoData.value.details_dist_t
  let str = `关注公众号 👉 #${config.value.wxpublic_name}\r\r${slogan.value}\r\r昵称：${infoData.value.username}\r性别：${infoData.value.gender == 1 ? '男' : '女'}\r年龄：${infoData.value.age}岁\r职业：${infoData.value.job > 0 ? infoData.value.job_t : '无'}\r${config.value.dist_title || '居住地'}：${dist} \r-----------------------------------\r☎ 服务热线:${config.value.sitetel}\r\r点击下方链接进一步了解\r${share_url.value}`
  copy(str, () => {
    proxy.$refs.linkIntro.close()
  })
}

const copy = (data, callback) => {
  if (callback) callback()
  let elInput = document.createElement('textarea')
  elInput.value = data
  document.body.appendChild(elInput)
  elInput.select()
  document.execCommand('Copy')
  proxy.$refs.copysuccess_dialog.open()
  elInput.remove()
}

const list = ref([])
const page = ref(1)
const pageCount = ref('0')
const listStatus = ref('loading')

const getSloganAll = (flag, callback) => {
  if (page.value == 0) return
  getTgSloganAll({
    s_type: 1,
    page: page.value,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}
const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getSloganAll(true, done)
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getSloganAll(false, () => {
    is_pull.value = true
  })
}

const copySlogan = val => {
  slogan.value = val
  copy(val)
}
const closeCopyDialog = () => {
  proxy.$refs.copysuccess_dialog.close()
  proxy.$refs.sloganMore.close()
}

onMounted(() => {
  getSloganAll()
  getDetail()
  getTplData()
})
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}

.main {
  padding: 0.32rem 0.4267rem;
  font-family: PingFang SC, PingFang SC;

  .data {
    background: rgba(255, 255, 255, 0.32);
    border: 0.0267rem solid #ffffff;
    padding: 0.32rem;
    border-radius: 0.32rem;
    margin-bottom: 0.64rem;
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 0;

    .item {
      width: 50%;
      display: flex;
      align-items: center;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      font-weight: normal;
      margin-bottom: 0.32rem;

      span {
        color: #666;
      }

      p {
        color: #31293b;
      }
    }
  }

  .slogan {
    .title {
      .name {
        font-size: 0.4267rem;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 0.5867rem;
      }

      .updata {
        color: #0570f1;
        font-size: 0.3733rem;
        font-weight: normal;
        color: #0570f1;
        line-height: 0.64rem;
        transform: all 0.3s;

        i {
          margin-right: 0.1333rem;
          position: relative;
          top: 0.0267rem;
        }

        .refresh_anime {
          transform: rotate(100deg);
        }
      }
    }

    .tips {
      font-size: 0.32rem;
      font-weight: normal;
      color: #666;
      line-height: 0.5333rem;
      margin-top: 0.32rem;
    }

    .intro {
      background: #fff;
      margin-top: 0.32rem;
      padding: 0.32rem;
      border-radius: 0.32rem;

      p {
        font-size: 0.3733rem;
        font-weight: normal;
        color: #666666;
        line-height: 0.5867rem;
      }

      .btn {
        margin-top: 0.2933rem;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        span {
          width: fit-content;
          cursor: pointer;
          font-size: 0.3733rem;
          font-weight: normal;
          color: $color_main;
          line-height: 0.5333rem;
          border: 0.0267rem solid $color_main;
          padding: 0.1333rem 0.4267rem;
          border-radius: 0.4267rem;
          box-sizing: border-box;
          margin-left: 0.2133rem;

          &.copy {
            border: 0.0267rem solid $color_main;
            background: $color_main;
            color: #fff;
          }
        }
      }
    }
  }

  .poster {
    margin-top: 0.64rem;

    .title {
      span {
        font-size: 0.32rem;
        font-weight: normal;
        color: #666666;
        line-height: 0.4533rem;
      }

      .name {
        font-size: 0.4267rem;
        font-weight: 500;
        color: #31293b;
        line-height: 0.5867rem;
      }
    }

    .list {
      margin-top: 0.32rem;
      display: flex;
      flex-wrap: wrap;

      .item {
        background: #fafafa;
        width: calc(30%);
        aspect-ratio: 375/667;
        margin-bottom: 0.4267rem;
        border-radius: 0.32rem;
        overflow: hidden;
        margin-right: 0.4267rem;

        &:nth-child(3n) {
          margin-right: 0;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}

.linkIntro {
  padding: 0.5867rem 0.4267rem 0.64rem 0.4267rem;

  .title {
    font-size: 0.48rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #31293b;
    line-height: 0.6667rem;

    i {
      font-weight: 600;
      font-size: 0.48rem;
      margin-right: 0.0533rem;
    }
  }

  .tips {
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #666666;
    line-height: 0.64rem;
    padding-left: 0.5333rem;
  }

  .tit {
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #999999;
    line-height: 0.64rem;
    margin: 0.4267rem;
  }

  .data {
    margin-top: 0.5333rem;
    background: #f2f4f5;
    border-radius: 0.32rem;
    max-height: 8rem;
    overflow-y: auto;
    padding: 0.4267rem;
    margin-bottom: 0.4267rem;
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #31293b;
    line-height: 0.64rem;
  }

  .undata {
    color: #287fff;
    margin-top: 0.1333rem;
    margin-bottom: 0.1333rem;
    transform: all 0.3s;

    i {
      position: relative;
      margin-right: 0.1333rem;
    }
  }

  .btn {
    background: $color_main;
    color: #fff;
    text-align: center;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    font-weight: normal;
    font-family: PingFang SC, PingFang SC;
    padding: 0.24rem 0;
    margin: 0 0.5333rem;
    border-radius: 0.5333rem;
  }
}

.copysuccess_dialog {
  text-align: center;
  padding: 0.64rem 1.2533rem;
  border-radius: 0.64rem;
  overflow: hidden;

  .tit {
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-weight: 600;
  }

  .tips {
    margin-top: 0.4267rem;
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #666666;
    line-height: 0.5867rem;
  }

  .event {
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin-top: 0.64rem;
    padding: 0.24rem 0;
  }
}

.refresh_anime {
  transform: rotate(100deg);
}

.sloganMore {
  font-family: PingFang SC, PingFang SC;
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
  height: 100vh;

  .nav {
    background: url('~@/assets/images/bg_main.png') no-repeat;
    background-size: 100%;
    position: fixed;
    height: 1.1733rem;
    z-index: 300;
    width: 100%;
    left: 0;
    top: 0;
    overflow: hidden;

    .back {
      position: absolute;
      width: 1.1733rem;
      height: 1.1733rem;
      left: 0;
      top: 0;

      i {
        font-size: 0.64rem;
        color: #666;
      }
    }

    .title {
      font-size: 0.48rem;
      line-height: 0.64rem;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #2a2546;
    }
  }

  .list {
    padding: 0.1067rem 0.4267rem;
    box-sizing: border-box;
    flex-wrap: wrap;
    overflow-y: scroll;

    .item {
      background: #fff;
      border-radius: 0.32rem;
      box-sizing: border-box;
      margin-bottom: 0.32rem;

      .data {
        padding: 0.32rem;
        border-bottom: 0.0267rem solid #f2f4f5;
        display: flex;
        align-items: center;
        justify-content: space-between;

        > div {
          display: flex;
          align-items: center;

          span {
            width: 0.8533rem;
            height: 0.8533rem;
            margin-right: 0.2133rem;
            flex-shrink: 0;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .id {
            font-size: 0.3733rem;
            line-height: 0.5333rem;
            font-weight: normal;
            color: #31293b;
          }

          .time {
            font-size: 0.3733rem;
            line-height: 0.5333rem;
            font-weight: normal;
            color: #999;
          }
        }

        > span {
          background: $color_main;
          color: #fff;
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-weight: normal;
          padding: 0.16rem 0.4267rem;
          border-radius: 0.5333rem;
          cursor: pointer;
        }
      }

      .content {
        padding: 0.32rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        color: #31293b;
        font-weight: normal;
      }
    }
  }
}
</style>

<!-- @format -->

<template>
  <div class="mian">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">用户资料</div>
    </div>
    <div class="h44"></div>
    <div class="content">
      <div class="user flex flex_ac">
        <div class="head flex_s">
          <img v-if="infoData.headimg" type="user" v-lazy="infoData.headimg_m1_url" alt="" />
          <template v-else>
            <img v-if="infoData.gender == 1" src="@/assets/images/gender_1.png" alt="" />
            <img v-else-if="infoData.gender == 2" src="@/assets/images/gender_2.png" alt="" />
            <img v-else src="@/assets/images/gender_0.png" alt="" />
          </template>
        </div>
        <div class="flex_1">
          <p class="name">{{ infoData.username }}</p>
          <div class="flex flex_ac flex_jsb" style="margin-top: 0.0533rem">
            <div class="id flex flex_ac">
              <i class="iconfont icon-nanbiao-01" v-if="infoData.gender == 1"></i>
              <i class="iconfont icon-nvbiao-01" v-else></i>
              编号: {{ infoData.userid }}
            </div>
            <p class="addtime">注册时间：{{ getTime(infoData.addtime, true) }}</p>
          </div>
        </div>
      </div>
      <div class="lab flex flex_ac">
        <span @click="changView('info')" :class="is_active == 'info' ? 'current' : ''">基本资料</span>
        <span @click="changView('cond')" :class="is_active == 'cond' ? 'current' : ''">择偶条件</span>
      </div>
      <div>
        <user_info :item="infoData" v-if="is_active == 'info'" />
        <user_cond :item="infoCond" v-if="is_active == 'cond'" />
      </div>
    </div>
  </div>
  <div class="h70"></div>
  <div class="share flex_dc" v-if="infoData.userid">
    <span @click="auditStartEvent" v-if="infoData.flag == 0" class="flex_dc"> 立即审核 </span>
    <span v-else-if="infoData.flag == 2" class="flex_dc fail"> 审核不通过 </span>
    <span v-else class="flex_dc success"> 审核通过 </span>
  </div>

  <!--审核-->
  <oe_popup ref="audit_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="audit_dialog">
      <p class="title">资料审核</p>
      <p class="tip">请如实审核用户资料，通过用户填写的 信息是否真实，完善度是否达到要求。 该操作不可逆，请谨慎操作。</p>
      <div class="btn flex_dc">
        <span @click="failAuditEvent" class="fail">不通过</span>
        <span @click="sendAuditPass">通过</span>
      </div>
      <span @click="proxy.$refs.audit_dialog.close()" class="close iconfont icon-guanbi2"></span>
    </div>
  </oe_popup>

  <oe_popup ref="auditFail_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="auditFail_dialog">
      <p class="title">审核不通过原因</p>
      <div class="content">
        <textarea v-model="fail_remark" maxlength="200" placeholder="请输入审核不通过的原因"></textarea>
        <span class="nums">{{ fail_remark.length }}/200</span>
      </div>
      <div class="btn flex_dc">
        <span @click="sendAuditFail(false)" class="fail">不填</span>
        <span @click="sendAuditFail(true)">确定</span>
      </div>
      <span
        @click="
          proxy.$refs.auditFail_dialog.close(() => {
            fail_remark = ''
          })
        "
        class="close iconfont icon-guanbi2"></span>
    </div>
  </oe_popup>
</template>

<script setup>
  import { ref, getCurrentInstance } from 'vue'
  import { useRoute } from 'vue-router'
  import { getAuditUserDetail, auditPass, auditFail } from '@/api/audit_user.js'
  import { getTime } from '@/utils/hooks.js'
  import user_info from './info.vue'
  import user_cond from './cond.vue'
  import oe_popup from '@/oeui/popup.vue'

  const { proxy } = getCurrentInstance()
  const OEUI = proxy.OEUI

  const route = useRoute()

  const infoData = ref({})
  const infoCond = ref({})

  const getDetail = () => {
    getAuditUserDetail({
      id: route.query.id
    }).then(res => {
      if (res.ret == 1) {
        infoData.value = res.result.data
        infoCond.value = res.result.cond
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试'
        })
      }
    })
  }
  const is_active = ref('info')
  const changView = val => {
    is_active.value = val
  }

  const auditStartEvent = () => {
    proxy.$refs.audit_dialog.open()
  }

  const failAuditEvent = () => {
    proxy.$refs.audit_dialog.close(() => {
      proxy.$refs.auditFail_dialog.open()
    })
  }
  const is_send = ref(true)
  const fail_remark = ref('')
  const sendAuditPass = () => {
    if (!is_send.value) return
    is_send.value = false
    auditPass({
      id: infoData.value.userid
    }).then(res => {
      if (res.ret == 1) {
        proxy.$refs.audit_dialog.close(() => {
          OEUI.toast({
            text: '操作成功'
          })
          setTimeout(() => {
            getDetail()
          }, 500)
        })
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试'
        })
      }
      setTimeout(() => {
        is_send.value = true
      }, 500)
    })
  }
  const sendAuditFail = id => {
    if (!is_send.value) return
    is_send.value = false
    auditFail({
      id: infoData.value.userid,
      remark: fail_remark.value
    }).then(res => {
      if (res.ret == 1) {
        proxy.$refs.auditFail_dialog.close(() => {
          OEUI.toast({
            text: '操作成功'
          })
          setTimeout(() => {
            getDetail()
          }, 500)
        })
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试'
        })
      }
      setTimeout(() => {
        is_send.value = true
      }, 500)
    })
  }

  getDetail()
</script>

<style lang="scss" scoped>
  .mian {
    .top_nav {
      background: url('~@/assets/images/bg_main.png') no-repeat;
      background-size: cover;
    }
    .content {
      padding: 0 0.4267rem;
      .user {
        padding-left: 0.2133rem;
        .head {
          width: 1.3867rem;
          height: 1.3867rem;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 0.32rem;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .name {
          font-size: 0.4267rem;
          line-height: 0.5867rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          color: #31293b;
        }
        .id {
          font-size: 0.32rem;
          line-height: 0.4533rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: normal;
          color: #666666;
          i {
            position: relative;
            font-size: 0.4267rem;
            margin-right: 0.0533rem;
          }
          .icon-nanbiao-01 {
            color: #0570f1;
          }
          .icon-nvbiao-01 {
            color: #fe6897;
          }
        }
        .addtime {
          font-size: 0.32rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: normal;
          color: #999999;
          line-height: 0.4533rem;
        }
      }
      .lab {
        margin-top: 0.4267rem;
        margin-bottom: 0.2133rem;
        height: 1.1733rem;
        span {
          margin-right: 0.64rem;
          font-size: 0.3733rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: normal;
          color: #666666;
          line-height: 0.5333rem;
          position: relative;
          top: 2px;
          &.current {
            font-size: 0.48rem;
            font-weight: 500;
            color: #3d3d3d;
            line-height: 0.6667rem;
            top: 0;
          }
        }
      }
    }
  }
  .share {
    position: fixed;
    width: 100%;
    height: 1.6rem;
    bottom: 0.2667rem;
    z-index: 300;
    span {
      cursor: pointer;
      i {
        margin-right: 0.2667rem;
      }
      background: $color_main;
      font-size: 0.4267rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      color: #ffffff;
      line-height: 0.5867rem;
      padding: 0.2933rem 1.0667rem;
      border-radius: 0.5867rem;
    }
    .fail {
      background: #ccc;
    }
    .success{
      background: #15ce7b;
    }
  }

  .linkDialog {
    font-family: PingFang SC, PingFang SC;
    padding: 0.5867rem 0.4267rem 0.64rem 0.4267rem;
    .tit {
      span {
        font-size: 0.48rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        color: #31293b;
        line-height: 0.6667rem;
      }
      i {
        cursor: pointer;
        color: #000;
        font-size: 0.48rem;
      }
    }
    .slogan {
      margin-top: 0.3467rem;
      font-size: 0.3733rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      color: #31293b;
      line-height: 0.64rem;
    }
    .refresh {
      transform: all 0.3s;
      color: #0570f1;
      i {
        margin-right: 0.1333rem;
      }
      margin-top: 0.4267rem;
      display: flex;
      align-items: center;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #0570f1;
      line-height: 0.64rem;
    }
    .data {
      margin-top: 0.5333rem;
      background: #f2f4f5;
      border-radius: 0.32rem;
      padding: 0.4267rem;
      margin-bottom: 0.4267rem;
      display: flex;
      flex-wrap: wrap;
      span {
        width: 50%;
        font-size: 0.3733rem;
        font-weight: normal;
        color: #31293b;
        line-height: 0.64rem;
      }
    }
    .tips {
      font-size: 0.32rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      color: #666666;
      line-height: 0.64rem;
    }
    .btn {
      margin-top: 0.64rem;
      span {
        cursor: pointer;
        font-size: 0.4267rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #ffffff;
        line-height: 0.5867rem;
        background: $color_main;
        padding: 0.24rem 2.3467rem;
        border-radius: 0.5333rem;
      }
    }
  }
  .linkIntro {
    padding: 0.5867rem 0.4267rem 0.64rem 0.4267rem;
    .title {
      font-size: 0.48rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      color: #31293b;
      line-height: 0.6667rem;
      i {
        font-weight: 600;
        font-size: 0.48rem;
        margin-right: 0.0533rem;
      }
    }
    .tips {
      font-size: 0.3733rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      color: #666666;
      line-height: 0.64rem;
      padding-left: 0.5333rem;
    }
    .tit {
      font-size: 0.4267rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      color: #999999;
      line-height: 0.64rem;
      margin: 0.4267rem;
    }
    .data {
      margin-top: 0.5333rem;
      background: #f2f4f5;
      border-radius: 0.32rem;
      padding: 0.4267rem;
      margin-bottom: 0.4267rem;
      font-size: 0.3733rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      color: #31293b;
      line-height: 0.64rem;
    }
  }
  .refresh_anime {
    transform: rotate(100deg);
  }

  .audit_dialog {
    padding: 0.64rem;
    font-family: PingFang SC, PingFang SC;
    position: relative;
    .title {
      font-size: 0.4267rem;
      font-weight: 500;
      color: #3d3d3d;
      line-height: 0.5867rem;
      text-align: center;
    }
    .tip {
      margin-top: 0.4267rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5867rem;
    }
    .btn {
      margin-top: 0.5333rem;
      span {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.4267rem;
        font-weight: normal;
        line-height: 0.5867rem;
        padding: 0.24rem 0;
        cursor: pointer;
        background: $color_main;
        border-radius: 0.5333rem;
        color: #fff;
      }
      .fail {
        margin-right: 0.5867rem;
        color: $color_main;
        background: rgba(125, 104, 254, 0.24);
      }
    }
    .close {
      position: absolute;
      right: 0.4rem;
      top: 0.2667rem;
      font-size: 0.48rem;
    }
  }
  .auditFail_dialog {
    padding: 0.4267rem 0.64rem;
    font-family: PingFang SC, PingFang SC;
    position: relative;
    .title {
      font-size: 0.4267rem;
      font-weight: 500;
      color: #3d3d3d;
      line-height: 0.5867rem;
      text-align: center;
    }
    .content {
      margin-top: 0.32rem;
      background: #f2f4f5;
      height: 2.8rem;
      border-radius: 0.32rem;
      padding: 0.32rem;
      box-sizing: border-box;
      position: relative;
      textarea {
        resize: none;
        border: none;
        width: 100%;
        height: 100%;
        background: none;
        &::placeholder {
          font-size: 0.3733rem;
          font-weight: normal;
          color: #c5c3c7;
          line-height: 0.5333rem;
        }
      }
      .nums {
        position: absolute;
        font-size: 0.32rem;
        font-weight: normal;
        color: #c5c3c7;
        line-height: 0.4533rem;
        right: 0.4rem;
        bottom: 0.0533rem;
      }
    }
    .btn {
      margin-top: 0.4rem;
      padding: 0 0.2133rem;
      span {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.4267rem;
        font-weight: normal;
        line-height: 0.5867rem;
        padding: 0.24rem 0;
        cursor: pointer;
        background: $color_main;
        border-radius: 0.5333rem;
        color: #fff;
      }
      .fail {
        margin-right: 0.5867rem;
        color: $color_main;
        background: rgba(125, 104, 254, 0.24);
      }
    }
    .close {
      position: absolute;
      right: 0.4rem;
      top: 0.2667rem;
      font-size: 0.48rem;
    }
  }
</style>

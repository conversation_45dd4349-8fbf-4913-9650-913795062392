<!-- @format -->

<template>
  <div class="item_box" @click="$router.push('/user/detail?id=' + item.userid)" v-if="item.userid">
    <div class="data">
      <div class="head">
        <img v-lazy="item.headimg_m1_url" type="user" v-if="item.headimg" alt="" />
        <template v-else>
          <img v-if="item.gender == 1" src="@/assets/images/gender_1.png" alt="" />
          <img v-else-if="item.gender == 2" src="@/assets/images/gender_2.png" alt="" />
          <img v-else src="@/assets/images/gender_0.png" alt="" />
        </template>
      </div>
      <div class="info">
        <p class="job">
          {{ item.age }}岁
          <template v-if="item.job > 0">/{{ item.job_t }}</template>
        </p>
        <p class="name">
          昵称: &nbsp;
          <em>{{ item.username }}</em>
          &emsp; (编号:&nbsp;{{ item.userid }})
        </p>
        <p class="base" v-if="item.marry > 0 || item.education > 0 || item.height > 0">
          <template v-if="item.marry > 0">{{ item.marry_t }}</template>
          <template v-if="item.marry > 0">·</template>
          <template v-if="item.education > 0">{{ item.education_t }}</template>
          <template v-if="item.education > 0">·</template>
          <template v-if="item.height > 0">{{ item.height }}cm</template>
        </p>
        <div class="dist">
          <template v-if="item.dist1 > 0 && item.list_dist_t">
            <div class="flex_1 ws flex flex_ac" style="margin-right: 0.2667rem">
              <span>现居：{{ item.list_dist_t }}</span>
            </div>
          </template>
          <span class="flex_1 ws" v-if="item.salary > 0">年收入：{{ item.salary_t }}</span>
        </div>
      </div>
    </div>
    <div class="share">
      <div>
        <span>
          <img src="@/assets/images/share_info.png" alt="" />
        </span>
        <p>用户信息</p>
      </div>
      <div
        @click.stop="
          $router.push({
            path: '/user/share',
            query: {
              id: item.userid,
            },
          })
        "
      >
        <i class="iconfont icon-fenxiang01"></i>
        <p>分享</p>
      </div>
    </div>
    <div class="tips" v-if="item.idrz > 0">已实名</div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, computed } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const config = computed(() => store.state.config)
const { proxy } = getCurrentInstance()
defineProps({
  item: {
    type: Object,
  },
})
</script>

<style lang="scss" scoped>
.item_box {
  cursor: pointer;
  background: #fff;
  border-radius: 0.32rem;
  box-sizing: border-box;
  margin-bottom: 0.32rem;
  position: relative;
  overflow: hidden;

  .data {
    padding: 0.32rem;
    box-sizing: border-box;
    display: flex;
    align-content: center;
    border-bottom: 0.0267rem solid #f2f4f5;

    .head {
      width: 2.0533rem;
      height: 2.0533rem;
      border-radius: 0.32rem;
      overflow: hidden;
      margin-right: 0.2133rem;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .job {
        font-size: 0.48rem;
        line-height: 0.6667rem;
        color: #3d3d3d;
        font-weight: 500;
      }

      .name {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
        display: flex;
        align-items: center;

        em {
          max-width: 2.6667rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }

      .base {
        margin-top: 0.2133rem;
        font-size: 0.3467rem;
        line-height: 0.48rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }

      .dist {
        width: 100%;
        margin-top: 0.2133rem;
        font-size: 0.3467rem;
        line-height: 0.48rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }

  .share {
    padding: 0.32rem;
    box-sizing: border-box;
    display: flex;
    align-content: center;

    div {
      color: #7d68fe;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      span {
        width: 0.64rem;
        height: 0.64rem;
        margin-right: 0.1067rem;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      i {
        color: #bdb3fe;
        margin-right: 0.1333rem;
        font-size: 0.48rem;
        position: relative;
        top: 0.0267rem;
      }

      p {
        position: relative;
        top: 0.0267rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }
    }
  }

  .tips {
    position: absolute;
    background: $color_main;
    color: #fff;
    right: -0.6133rem;
    top: -0.1067rem;
    padding: 0.4rem 0.5333rem 0.0533rem 0.5333rem;
    font-size: 0.2667rem;
    font-weight: normal;
    line-height: 0.3733rem;
    transform: rotate(45deg);

    &.success {
      background: #ccc;
    }
  }
}
</style>

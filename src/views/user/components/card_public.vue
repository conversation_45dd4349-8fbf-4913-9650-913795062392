<!-- @format -->

<template>
  <!-- 相亲卡定位 -->
  <div class="pr bg_b3" style="width: 375px; height: 667px; overflow: hidden" ref="cardBox">
    <img :src="blindcard.postimg_base64" class="w100 pr z200" />
    <!-- 头像 -->
    <div
      class="oh bo_op0 z100"
      :class="blindcard.avatar_theback == 1 ? 'z100' : 'z300'"
      v-if="blindcard.avatar_hide != '1'"
      :style="{
        position: 'absolute',
        width: `${blindcard.avatar_w || 120}px`,
        height: `${blindcard.avatar_h || 120}px`,
        top: `${blindcard.avatar_y || 47}px`,
        left: `${blindcard.avatar_x || 129}px`,
        borderRadius: `${blindcard.avatar_round == 1 ? `50%` : '0px'}`,
      }"
    >
      <img :src="user.headimg_base64" class="ob_c" />
    </div>
    <!-- 用户编号 -->
    <div
      class="bo_op0 z300 p5"
      v-if="blindcard.title_hide != '1' && user.userid"
      :style="{
        position: 'absolute',
        width: `${blindcard.title_w || 210}px`,
        height: `${blindcard.title_h || 50}px`,
        top: `${blindcard.title_y - 8 || 210}px`,
        left: `${blindcard.title_x || 116}px`,
        color: `${blindcard.title_color || `#000`}`,
        fontSize: `${blindcard.title_size || 16}px`,
        lineHeight: `${blindcard.title_lineheight || 22}px`,
        fontWeight: 'bold',
      }"
    >
      用户编号:{{ user.userid || '' }}
    </div>
    <!-- 嘉宾介绍 -->
    <div
      class="bo_op0 z300"
      v-if="blindcard.data_hide != 1"
      :style="{
        position: 'absolute',
        width: `${blindcard.data_w || 228}px`,
        top: `${blindcard.data_y || 319}px`,
        left: `${blindcard.data_x || 77}px`,
        color: `${blindcard.data_color || `#9588a4`}`,
        fontSize: `${blindcard.data_size || 12}px`,
        lineHeight: `${blindcard.data_lineheight || 22}px`,
      }"
    >
      <span class="mr10">性别:{{ user.gender_t }}</span>
      <span class="mr10" v-if="user.age > 0">年龄:{{ user.age + '岁' }}</span>
      <span class="mr10" v-if="user.height > 0">身高:{{ user.height + 'cm' }}</span>
      <span class="mr10" v-if="user.education > 0">学历:{{ user.education_t }}</span>

      <span class="mr10" v-if="user.card_dist_t">{{ config.dist_title || '居住地' }}:{{ user.card_dist_t }}</span>

      <span class="mr10" v-if="user.card_home_t">{{ config.home_title || '户籍地' }}:{{ user.card_home_t }}</span>

      <span class="mr10" v-if="user.job > 0">职业:{{ user.job_t }}</span>
      <span class="mr10" v-if="user.salary > 0">年收入:{{ user.salary_t }}</span>
    </div>
    <!-- 择偶要求 -->
    <div
      class="bo_op0 z300"
      v-if="blindcard.cond_hide != '1'"
      :style="{
        position: 'absolute',
        width: `${blindcard.cond_w || 228}px`,
        top: `${blindcard.cond_y || 447}px`,
        left: `${blindcard.cond_x || 80}px`,
        color: `${blindcard.cond_color || `#9588a4`}`,
        fontSize: `${blindcard.cond_size || 12}px`,
        lineHeight: `${blindcard.cond_lineheight || 22}px`,
      }"
    >
      <span class="mr10">
        年龄:
        <template v-if="cond.age1 > 0 && cond.age2 > 0">{{ cond.age1 + '-' + cond.age2 }}岁</template>
        <template v-else>不限</template>
      </span>
      <span class="mr10">
        身高:
        <template v-if="cond.height1 > 0 && cond.height2 > 0">{{ cond.height1 + '-' + cond.height2 }}CM</template>
        <template v-else>不限</template>
      </span>
      <span class="mr10">
        学历:
        <template v-if="cond.education > 0">{{ cond.education_t }}</template>
        <template v-else>不限</template>
      </span>
      <span class="mr10">
        年收入:
        <template v-if="cond.salary > 0">{{ cond.salary_t }}以上</template>
        <template v-else>不限</template>
      </span>
      <span class="mr10">
        婚况:
        <template v-if="cond.marry > 0">{{ cond.marry_t }}</template>
        <template v-else>不限</template>
      </span>
    </div>
    <!-- 二维码 -->
    <img
      class="bo_op0 z300"
      v-if="blindcard.qrcode_hide != '1' && qrcode_img"
      :src="qrcode_img"
      :style="{
        width: `${blindcard.qrcode_h || 70}px`,
        height: `${blindcard.qrcode_w || 70}px`,
        position: 'absolute',
        top: `${blindcard.qrcode_y || 534}px`,
        left: `${blindcard.qrcode_x || 233}px`,
      }"
    />
    <!-- 二维码有效期 -->
    <div
      class="bo_op0 z300"
      v-if="blindcard.qrtime_hide != '1' && blindcard.qrtime_cont"
      :style="{
        position: 'absolute',
        width: `${blindcard.qrtime_w || 228}px`,
        height: `${blindcard.qrtime_h || 20}px`,
        top: `${blindcard.qrtime_y || 603}px`,
        left: `${blindcard.qrtime_x || 200}px`,
        color: `${blindcard.qrtime_color || `#000`}`,
        fontSize: `${blindcard.qrtime_size || 10}px`,
        lineHeight: `${blindcard.qrtime_lineheight || 22}px`,
      }"
    >
      {{ blindcard.qrtime_cont }}
    </div>
    <!-- 版权信息 -->
    <!-- 二维码文字描述 -->
    <div
      class="z300"
      v-if="blindcard.qrtip_hide != '1' && blindcard.qrtip_cont"
      :style="{
        position: 'absolute',
        width: `${blindcard.qrtip_w || 228}px`,
        height: `${blindcard.qrtip_h || 20}px`,
        top: `${blindcard.qrtip_y || 623}px`,
        left: `${blindcard.qrtip_x || 88}px`,
        color: `${blindcard.qrtip_color || `#9588a4`}`,
        fontSize: `${blindcard.qrtip_size || 12}px`,
        lineHeight: `${blindcard.qrtip_lineheight || 22}px`,
      }"
    >
      {{ blindcard.qrtip_cont }}
    </div>

    <div
      class="bo_op0 z300"
      v-if="blindcard.name_hide != '1' && blindcard.name_cont"
      :style="{
        position: 'absolute',
        width: `${blindcard.name_w || 228}px`,
        height: `${blindcard.name_h || 20}px`,
        top: `${blindcard.name_y || 623}px`,
        left: `${blindcard.name_x || 88}px`,
        color: `${blindcard.name_color || `#9588a4`}`,
        fontSize: `${blindcard.name_size || 12}px`,
        lineHeight: `${blindcard.name_lineheight || 22}px`,
      }"
    >
      {{ blindcard.name_cont }}
    </div>
  </div>
</template>
<script setup>
import { getCurrentInstance, ref, nextTick, computed } from 'vue'
import { getUserOnetp, getTgQrcode } from '@/api/user.js'
import { useStore } from 'vuex'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const emit = defineEmits(['save', 'fail'])
const props = defineProps({
  postid: {
    type: Number,
  },
  //qrcode_img: {
  //  type: String
  //},
  // 用户信息
  user: {
    type: Object,
  },
  // 择偶对象
  cond: {
    type: Object,
  },
})

const store = useStore()
const config = computed(() => store.state.config)

const qrcode_img = ref('')
const blindcard = ref({})
// 获取模板列表
const getOnetpl = id => {
  getQrcode(props.user.userid).then(data => {
    if (data.ret == 1) {
      OEUI.loading.show('模板加载中')
      getUserOnetp({
        postid: id,
      })
        .then(res => {
          OEUI.loading.hide()
          if (res.ret == 1) {
            blindcard.value = res.result.data
            nextTick(() => {
              emit('save')
            })
          } else {
            OEUI.toast('获取模板失败')
            emit('fail')
          }
        })
        .catch(() => {
          OEUI.toast('获取模板失败')
          emit('fail')
        })
    } else {
      OEUI.toast({ type: 'error', text: '二维码生成失败' })
      emit('fail')
    }
  })
}

//  获取二维码
const getQrcode = id => {
  return new Promise((resolve, reject) => {
    getTgQrcode({
      id,
    }).then(res => {
      if (res.ret == 1) {
        qrcode_img.value = res.result.data
        resolve(res)
      } else {
        resolve(res)
      }
    })
  })
}

const open = id => {
  getOnetpl(id || props.postid)
}

defineExpose({
  open,
})
</script>
<style lang="scss" scoped>
.bo_op0 {
  border: 1px solid rgba(255, 255, 255, 0);
}

.p5 {
  padding: 5px;
}

.min_w33 {
  min-width: 33.33%;
}

.min_w50 {
  min-width: 50%;
}

.t_center {
  text-align: center;
}

.inline {
  display: inline;
}

.w100 {
  width: 100%;
}

.pr {
  position: relative;
}

.z100 {
  z-index: 100;
}

.oh {
  overflow: hidden;
}

.mr10 {
  margin-right: 10px;
}

.z300 {
  z-index: 300;
}

.z200 {
  z-index: 200;
}
.ob_c {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>

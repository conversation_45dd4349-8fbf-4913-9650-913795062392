<!-- @format -->

<template>
  <oeui-popup class="popSearch" ref="popSearch" width="85%">
    <div class="main">
      <div class="h44 flex_dc top">
        <span class="title">条件搜索</span>
        <span @click="proxy.$refs.popSearch.close()" class="back flex_dc iconfont icon-guanbi2"></span>
      </div>
      <div class="content">
        <div class="name_box">
          <input type="text" v-model="state.userid" maxlength="15" placeholder="输入嘉宾ID搜索" />
        </div>
        <div class="list">
          <div class="item_range">
            <div class="title flex flex_ac flex_jsb">
              <span class="name">年龄</span>
              <span class="value">{{ age_range.min }}-{{ age_range.max }}岁</span>
            </div>
            <div class="slider">
              <oeui-slider ref="ageRange" :min="18" :max="64" active-color="#9062fe" block-color="#9062fe" :minValue="age_range.min" :maxValue="age_range.max" @change="ageChange" />
            </div>
          </div>
          <div class="item_range">
            <div class="title flex flex_ac flex_jsb">
              <span class="name">身高</span>
              <span class="value">{{ height_range.min }}-{{ height_range.max }}cm</span>
            </div>
            <div class="slider">
              <oeui-slider ref="heightRange" :min="140" :max="200" active-color="#9062fe" block-color="#9062fe" :minValue="height_range.min" :maxValue="height_range.max" @change="heightChange" />
            </div>
          </div>
          <div class="item_range">
            <div class="title flex flex_ac flex_jsb">
              <span class="name">体重</span>
              <span class="value">{{ weight_range.min }}-{{ weight_range.max }}kg</span>
            </div>
            <div class="slider">
              <oeui-slider ref="weightRange" :min="40" :max="90" active-color="#9062fe" block-color="#9062fe" :minValue="weight_range.min" :maxValue="weight_range.max" @change="weightChange" />
            </div>
          </div>
          <div class="item_more">
            <div class="title flex flex_ac">
              <span class="name">性别</span>
            </div>
            <div class="data">
              <span @click="changeGender(0)" :class="!state.gender ? 'current' : ''">不限</span>
              <span @click="changeGender(1)" :class="state.gender == 1 ? 'current' : ''">男</span>
              <span @click="changeGender(2)" :class="state.gender == 2 ? 'current' : ''">女</span>
              <div class="clear"></div>
            </div>
          </div>
          <div class="item_more">
            <div class="title flex flex_ac">
              <span class="name">婚况</span>
            </div>
            <div class="data">
              <span :class="state.marry == item.value ? 'current' : ''" @click="changeMarry(item.value)" v-for="item in listState.marryList" :key="item.value">{{ item.text }}</span>
              <div class="clear"></div>
            </div>
          </div>
          <div class="item_more">
            <div class="title flex flex_ac">
              <span class="name">头像</span>
            </div>
            <div class="data">
              <span @click="changeAvatar(0)" :class="state.avatar == 0 ? 'current' : ''">不限</span>
              <span @click="changeAvatar(1)" :class="state.avatar == 1 ? 'current' : ''">有头像</span>
              <div class="clear"></div>
            </div>
          </div>
          <div class="item_more">
            <div class="title flex flex_ac">
              <span class="name">实名认证</span>
            </div>
            <div class="data">
              <span @click="changeIdrz(0)" :class="state.idrz == 0 ? 'current' : ''">不限</span>
              <span @click="changeIdrz(1)" :class="state.idrz == 1 ? 'current' : ''">已实名</span>
              <span @click="changeIdrz(2)" :class="state.idrz == 2 ? 'current' : ''">未实名</span>
              <div class="clear"></div>
            </div>
          </div>
          <div class="item" @click="openItemPicker('education')">
            <span class="name">学历</span>
            <div class="flex flex_ac">
              <span class="value" v-if="Number(state.education) > 0">{{ stateText.education }}</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('area')">
            <span class="name">{{ config.dist_title || '居住地' }}</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.dist1 > 0">{{ stateText.dist1 }} {{ stateText.dist2 }} {{ stateText.dist3 }} {{ stateText.dist4 }}</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('home')">
            <span class="name">{{ config.home_title || '户籍地' }}</span>
            <div class="flex flex_ac">
              <span class="value" v-if="Number(state.home1) > 0">{{ stateText.home1 }} {{ stateText.home2 }} {{ stateText.home3 }} {{ stateText.home4 }}</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('job')">
            <span class="name">职业</span>
            <div class="flex flex_ac">
              <span class="value" v-if="Number(state.job) > 0">{{ stateText.job }}</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('salary')">
            <span class="name">年收入</span>
            <div class="flex flex_ac">
              <span class="value" v-if="Number(state.salary) > 0">{{ stateText.salary }}</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('lunar')">
            <span class="name">生肖</span>
            <div class="flex flex_ac">
              <span class="value" v-if="Number(state.lunar) > 0">{{ stateText.lunar }}</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('astro')">
            <span class="name">星座</span>
            <div class="flex flex_ac">
              <span class="value" v-if="Number(state.astro) > 0">{{ stateText.astro }}</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('child')">
            <span class="name">小孩情况</span>
            <div class="flex flex_ac">
              <span class="value" v-if="Number(state.child) > 0">{{ stateText.child }}</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('house')">
            <span class="name">住房情况</span>
            <div class="flex flex_ac">
              <span class="value" v-if="Number(state.house) > 0">{{ stateText.house }}</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('car')">
            <span class="name">购车情况</span>
            <div class="flex flex_ac">
              <span class="value" v-if="Number(state.car) > 0">{{ stateText.car }}</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
        </div>
      </div>
      <div style="height: 1.6rem"></div>
      <div class="flex_dc btn">
        <span class="reset" @click="reset">重置</span>
        <span @click="searchData">搜索</span>
      </div>
    </div>

    <oeui-picker ref="educationPicker" title="学历" :list="listState.educationList" :current="[Number(state.education)]" v-if="listState.educationList.length > 0" @confirm="sendEducation" />
    <oeui-area ref="areaPicker" :title="config.dist_title || '居住地'" :list="listState.areaList" :limit="areaLimit" :current="[state.dist1, state.dist2, state.dist3, state.dist4]" @change="sendDist" v-if="listState.areaList.length > 0"></oeui-area>
    <oeui-area ref="homePicker" :title="config.home_title || '户籍地'" :list="listState.homeList" :limit="areaLimit" :current="[state.home1, state.home2, state.home3, state.home4]" @change="sendHome" v-if="listState.homeList.length > 0"></oeui-area>
    <oeui-picker ref="jobPicker" title="职业" :list="listState.jobList" :current="[Number(state.job)]" v-if="listState.jobList.length > 0" @confirm="sendJob"></oeui-picker>
    <oeui-picker ref="salaryPicker" title="年收入" :list="listState.salaryList" :current="[Number(state.salary)]" v-if="listState.salaryList.length > 0" @confirm="sendSalary"></oeui-picker>
    <oeui-picker ref="lunarPicker" title="生肖" :list="listState.lunarList" :current="[Number(state.lunar)]" v-if="listState.lunarList.length > 0" @confirm="sendLunar"></oeui-picker>
    <oeui-picker ref="astroPicker" title="星座" :list="listState.astroList" :current="[Number(state.astro)]" v-if="listState.astroList.length > 0" @confirm="sendAstro"></oeui-picker>
    <oeui-picker ref="childPicker" title="小孩情况" :list="listState.childList" :current="[Number(state.child)]" v-if="listState.childList.length > 0" @confirm="sendChild"></oeui-picker>
    <oeui-picker ref="housePicker" title="住房情况" :list="listState.houseList" :current="[Number(state.house)]" v-if="listState.houseList.length > 0" @confirm="sendHouse"></oeui-picker>
    <oeui-picker ref="carPicker" title="购车情况" :list="listState.carList" :current="[Number(state.car)]" v-if="listState.carList.length > 0" @confirm="sendCar"></oeui-picker>
  </oeui-popup>
</template>

<script setup>
import { getCurrentInstance, ref, reactive, nextTick, computed } from 'vue'
import { getPickerData, getDistPicker, getHomePicker } from '@/utils/main'
import { useStore } from 'vuex'
import oeuiArea from '@/oeui/area.vue'
import oeuiPicker from '@/oeui/picker.vue'
import oeuiPopup from '@/oeui/popup.vue'
import oeuiSlider from '@/oeui/slider'
const store = useStore()
const config = computed(() => store.state.config)

const emit = defineEmits(['sendSearch'])

const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const age_range = ref({ min: 18, max: 64 })
const height_range = ref({ min: 140, max: 200 })
const weight_range = ref({ min: 40, max: 90 })

const is_one = ref(true)

const areaLimit = ref({
  value: '0',
  text: '不限',
})
const state = reactive({
  userid: '',
  age1: '',
  age2: '',
  height1: '',
  height2: '',
  weight1: '',
  weight2: '',
  gender: 0,
  marry: 0,
  education: 0,
  dist1: 0,
  dist2: 0,
  dist3: 0,
  dist4: 0,
  home1: 0,
  home2: 0,
  home3: 0,
  home4: 0,
  job: 0,
  salary: 0,
  lunar: 0, //生肖
  astro: 0, //星座
  child: 0,
  house: 0,
  car: 0,
  avatar: 0,
  idrz: 0,
})

const stateText = reactive({
  education: '',
  dist1: '',
  dist2: '',
  dist3: '',
  dist4: '',
  home1: '',
  home2: '',
  home3: '',
  home4: '',
  job: '',
  salary: '',
  lunar: '', //生肖
  astro: '', //星座
  child: '',
  house: '',
  car: '',
})
const listState = reactive({
  marryList: [],
  educationList: [],
  areaList: [],
  homeList: [],
  jobList: [],
  salaryList: [],
  lunarList: [],
  astroList: [],
  childList: [],
  houseList: [],
  carList: [],
})
// 拒绝双向选择条bug
const swi = ref(false)

const ageChange = value => {
  age_range.value.min = value.value1
  age_range.value.max = value.value2
}
const heightChange = value => {
  height_range.value.min = value.value1
  height_range.value.max = value.value2
}
const weightChange = value => {
  weight_range.value.min = value.value1
  weight_range.value.max = value.value2
}

const openItemPicker = item => {
  //打开各项 picker
  let text = item.toLowerCase()
  //判断是否已有数据 没有则获取
  if (listState[text + 'List'] && listState[text + 'List'].length == 0 && text != 'area' && text != 'home') {
    getItemData(text, true)
  } else if (text == 'area' && listState.areaList.length == 0) {
    getDistData()
  } else if (text == 'home' && listState.homeList.length == 0) {
    getHomeData()
  } else {
    proxy.$refs[item + 'Picker'].open()
  }
}

const getDistData = () => {
  //获取地区数据
  const p = getDistPicker()
  p.then(res => {
    if (res.ret == 1) {
      listState.areaList = res.result
      nextTick(() => {
        proxy.$refs.areaPicker.open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
}
const getHomeData = () => {
  //获取户籍数据
  const p = getHomePicker()
  p.then(res => {
    if (res.ret == 1) {
      listState.homeList = res.result
      nextTick(() => {
        proxy.$refs.homePicker.open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
}
const getItemData = (item, limit = false) => {
  //获取picker数据
  const p = getPickerData(item, limit)
  p.then(res => {
    if (res.ret == 1) {
      listState[item + 'List'] = res.result
      if (item != 'marry') {
        nextTick(() => {
          proxy.$refs[item + 'Picker'].open()
        })
      }
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
  return p
}
getItemData('marry', true)

const changeGender = val => {
  state.gender = val
}

const changeMarry = val => {
  //选择婚况
  state.marry = val
}
const changeAvatar = val => {
  //选择头像
  state.avatar = val
}
const changeIdrz = val => {
  //选择实名
  state.idrz = val
}

const sendEducation = obj => {
  //保存学历
  state.education = obj[0].value
  stateText.education = obj[0].text
}
const sendDist = obj => {
  //保存地区
  stateText.dist1 = obj[0]?.text ?? ''
  stateText.dist2 = obj[1]?.text ?? ''
  stateText.dist3 = obj[2]?.text ?? ''
  stateText.dist4 = obj[3]?.text ?? ''
  state.dist1 = obj[0]?.value ?? ''
  state.dist2 = obj[1]?.value ?? ''
  state.dist3 = obj[2]?.value ?? ''
  state.dist4 = obj[3]?.value ?? ''
}
const sendHome = obj => {
  //保存户籍地
  stateText.home1 = obj[0]?.text ?? ''
  stateText.home2 = obj[1]?.text ?? ''
  stateText.home3 = obj[2]?.text ?? ''
  stateText.home4 = obj[3]?.text ?? ''
  state.home1 = obj[0]?.value ?? ''
  state.home2 = obj[1]?.value ?? ''
  state.home3 = obj[2]?.value ?? ''
  state.home4 = obj[3]?.value ?? ''
}
const sendJob = obj => {
  stateText.job = obj[0].text
  state.job = obj[0].value
}
const sendSalary = obj => {
  stateText.salary = obj[0].text
  state.salary = obj[0].value
}
const sendLunar = obj => {
  stateText.lunar = obj[0].text
  state.lunar = obj[0].value
}
const sendAstro = obj => {
  //保存星座
  stateText.astro = obj[0].text
  state.astro = obj[0].value
}
const sendChild = obj => {
  //保存有没有小孩
  stateText.child = obj[0].text
  state.child = obj[0].value
}
const sendHouse = obj => {
  //购房情况
  stateText.house = obj[0].text
  state.house = obj[0].value
}
const sendCar = obj => {
  //保存购车情况
  stateText.car = obj[0].text
  state.car = obj[0].value
}

const searchData = () => {
  //点击搜索
  if (age_range.value.min == 18 && age_range.value.max == 64) {
    state.age1 = ''
    state.age2 = ''
  } else {
    state.age1 = age_range.value.min
    state.age2 = age_range.value.max
  }
  if (height_range.value.min == 140 && height_range.value.max == 200) {
    state.height1 = ''
    state.height2 = ''
  } else {
    state.height1 = height_range.value.min
    state.height2 = height_range.value.max
  }

  if (weight_range.value.min == 40 && weight_range.value.max == 90) {
    state.weight1 = ''
    state.weight2 = ''
  } else {
    state.weight1 = weight_range.value.min
    state.weight2 = weight_range.value.max
  }
  proxy.$refs.popSearch.close()
  emit('sendSearch', state)
}
const open = () => {
  proxy.$refs.popSearch.open()
  //if (is_one.value) {
  //  is_one.value = false
  //  swi.value = !swi.value
  //}
}
const close = () => {
  proxy.$refs.popSearch.close()
}

const reset = () => {
  const data = {
    userid: '',
    age1: '',
    age2: '',
    height1: '',
    height2: '',
    weight1: '',
    weight2: '',
    gender: 0,
    marry: 0,
    education: 0,
    dist1: 0,
    dist2: 0,
    dist3: 0,
    dist4: 0,
    home1: 0,
    home2: 0,
    home3: 0,
    home4: 0,
    job: 0,
    salary: 0,
    lunar: 0, //生肖
    astro: 0, //星座
    child: 0,
    house: 0,
    car: 0,
    avatar: 0,
    idrz: 0,
  }
  for (let key in data) {
    state[key] = data[key]
  }
  age_range.value.min = 18
  age_range.value.max = 64
  height_range.value.min = 140
  height_range.value.max = 200
  weight_range.value.min = 40
  weight_range.value.max = 90
  nextTick(() => {
    proxy.$refs.ageRange.initSlider()
    proxy.$refs.heightRange.initSlider()
    proxy.$refs.weightRange.initSlider()
  })
}

defineExpose({ close, open, searchData })
</script>
<style lang="scss" scoped>
.main {
  height: 100vh;
  width: 100%;
  background: #f2f4f5;
  overflow-y: scroll;

  .top {
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 300;
    box-shadow: 0.0267rem -0.0267rem 0.24rem 0px #f3e7e7;

    .back {
      width: 1.1733rem;
      height: 1.1733rem;
      position: absolute;
      right: 0;
      font-size: 0.5333rem;
    }

    .title {
      font-size: 0.4267rem;
      line-height: 0.64rem;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #2a2546;
    }
  }

  .content {
    padding: 0.32rem;
    font-family: PingFang SC-Medium, PingFang SC;

    .name_box {
      background: #fff;
      height: 1.0667rem;
      line-height: 1.0667rem;
      padding: 0 0.32rem;
      border-radius: 0.32rem;
      font-size: 0.3467rem;
      font-weight: normal;
      margin-bottom: 0.32rem;
    }

    .list {
      background: #fff;
      border-radius: 0.32rem;
      padding: 0.32rem;

      .item_range {
        .title {
          height: 0.4rem;
          color: #333;
          margin-bottom: 0.2667rem;

          .name {
            font-weight: bold;
            font-size: 0.3733rem;
          }

          .value {
            font-size: 0.3733rem;
            font-weight: 500;
          }
        }

        .slider {
          padding: 0.2667rem 0;
          padding-bottom: 0.4rem;
        }
      }

      .item_more {
        .title {
          height: 0.4rem;
          color: #333;
          margin-bottom: 0.2667rem;

          .name {
            font-weight: bold;
            font-size: 0.3733rem;
          }
        }

        .data {
          span {
            border: 0.0267rem solid $color_main;
            color: $color_main;
            height: 0.5867rem;
            line-height: 0.5867rem;
            border-radius: 0.5333rem;
            padding: 0 0.3733rem;
            margin-right: 0.4rem;
            font-size: 0.32rem;
            margin-bottom: 0.32rem;
            font-weight: normal;
            float: left;

            &.current {
              background: $color_main;
              color: #fff;
            }
          }
        }
      }

      .item {
        line-height: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .name {
          font-weight: bold;
          font-size: 0.3733rem;
          color: #333;
        }

        div {
          span {
            font-size: 0.32rem;
            color: #ccc;
            font-weight: normal;
          }

          .value {
            color: #000;
          }

          i {
            color: #888;
            margin-left: 0.08rem;
          }
        }
      }
    }
  }

  .btn {
    position: fixed;
    width: 85vw;
    bottom: 0;
    height: 1.6rem;

    span {
      background: $color_main;
      color: #fff;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      padding: 0.2133rem 1.76rem;
      border-radius: 0.48rem;
      cursor: pointer;
    }

    .reset {
      background: #ccc;
      padding: 0.2133rem 0.5333rem;
      margin-right: 0.4rem;
    }
  }
}
</style>

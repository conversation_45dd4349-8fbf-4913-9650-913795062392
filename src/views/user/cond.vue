<!-- @format -->

<template>
  <div class="content_box">
    <div class="item">
      <span class="name">年龄</span>
      <span class="value" v-if="item.age1 > 0 && item.age2 > 0">{{ item.age1 }}-{{ item.age2 }}岁</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">身高</span>
      <span class="value" v-if="item.height1 > 0 && item.height2 > 0">{{ item.height1 }}-{{ item.height2 }}cm</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">体型</span>
      <span class="value" v-if="item.bodystyle_arr.length && Number(item.bodystyle_uptime)">{{ item.bodystyle_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">婚况</span>
      <span class="value" v-if="item.marry_arr.length && Number(item.marry_uptime)">{{ item.marry_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">学历</span>
      <span class="value" v-if="Number(item.education)">{{ item.education_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">年收入</span>
      <span class="value" v-if="Number(item.salary)">{{ item.salary_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">{{ config.dist_title || '居住地' }}</span>
      <span class="value" v-if="item.area1 > 0 && item.cond_details_area_t">
        {{ item.cond_details_area_t }}
      </span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">小孩</span>
      <span class="value" v-if="Number(item.child)">{{ item.child_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">购房</span>
      <span class="value" v-if="Number(item.house)">{{ item.house_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">购车</span>
      <span class="value" v-if="Number(item.car)">{{ item.car_t }}</span>
      <span v-else>不限</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const config = computed(() => store.state.config)
defineProps({
  item: {
    type: Object,
  },
})
</script>

<style lang="scss" scoped>
.content_box {
  background: #fff;
  padding: 0.32rem;
  border-radius: 0.32rem;

  .item {
    height: 1.1733rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: PingFang SC, PingFang SC;
    font-size: 0.3733rem;
    line-height: 0.5333rem;

    span {
      color: #ccc;
    }

    .name {
      font-weight: normal;
      color: #999999;
    }

    .value {
      font-weight: 500;
      color: #31293b;
    }
  }
}
</style>

<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">互选素材</div>
      <span class="more" @click="$router.push('/myactivity')">推广记录</span>
    </div>
    <oeui-list :top="1.1733" @refresh="refresh" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="filter flex_dc">
        <div class="flex_dc" @click="changOrderby">
          <span>{{ orderbyList.find(v => v.val == state.s_orderby).name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_orderby ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changTime">
          <span>{{ timeList[state.s_time].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
        </div>
        <!--<div class="flex_dc" @click="changArea">
          <span v-if="state.s_area1 > 0">{{ area.area1 }}{{ area.area2 }}{{ area.area3 }}</span>
          <span v-else>活动地区</span>
          <i class="iconfont icon-youjiantou-01" :class="is_area ? 'show' : 'hide'"></i>
        </div>-->
      </div>
      <div class="list">
        <template v-if="list.length">
          <list_item @share="shareActivity" :item="item" v-for="item in list" :key="item.actid" />
        </template>
        <template v-else-if="listStatus != 'no_data'">
          <div class="party_skelecton">
            <div class="head"></div>
            <div class="info">
              <p></p>
              <div class="flex flex_ac">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </template>
      </div>
    </oeui-list>
  </div>
  <activity_share ref="activityShare" />
  <search_item ref="searchOrderby" tyep="orderby" :list="orderbyList" :current="state.s_orderby" @changItem="selectOrderby" @close="closeItem"></search_item>
  <search_item ref="searchTime" type="time" :list="timeList" :current="state.s_time" @changItem="selectTime" @close="closeItem"></search_item>

  <oeui-area ref="areaEdit" :limit="areaLimit" @close="closeArea" title="活动地区" :list="areaList" :current="[state.s_area1, state.s_area2, state.s_area3, state.s_area4]" v-if="areaList.length > 0" @change="sendArea"></oeui-area>
</template>

<script>
export default {
  name: 'Activity',
}
</script>
<script setup>
import { ref, getCurrentInstance, reactive, nextTick, onActivated } from 'vue'
import oeuiList from '@/oeui/list.vue'
import list_item from './components/activity_item.vue'
import search_item from '@/components/search_itme.vue'
import oeuiArea from '@/oeui/area.vue'
import activity_share from '@/views/activity/components/activity_share.vue'
import { getActivity } from '@/api/activity.js'
import { getDistPicker } from '@/utils/main.js'
import { isWeiXin, setWechatShare } from '@/utils/jssdk.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const list = ref([])
const page = ref(1)
const pageCount = ref('0')
const listStatus = ref('loading')
const areaLimit = ref({
  value: 0,
  text: '不限',
})
const timeList = [
  { name: '全部时间', val: 0 },
  { name: '今天', val: 1 },
  { name: '昨天', val: 2 },
  { name: '本周', val: 3 },
  { name: '上周', val: 4 },
  { name: '本月', val: 5 },
  { name: '上月', val: 6 },
  { name: '今年', val: 7 },
  { name: '去年', val: 8 },
]
const orderbyList = [
  { name: '综合排序', val: '' },
  { name: '男生佣金从低到高', val: 'rw1_asc' },
  { name: '男生佣金从高到低', val: 'rw1_desc' },
  { name: '女生佣金从低到高', val: 'rw2_asc' },
  { name: '女生佣金从高到低', val: 'rw2_desc' },
]
const areaList = ref([])
const state = reactive({
  s_orderby: '',
  s_time: 0,
  //s_area1: 0,
  //s_area2: 0,
  //s_area3: 0
})

const is_time = ref(false)
const is_orderby = ref(false)
const is_area = ref(false)
const area = reactive({
  area1: '',
  area2: '',
  area3: '',
})

const changOrderby = () => {
  proxy.$refs.searchTime.close()
  is_time.value = false
  is_area.value = false
  nextTick(() => {
    if (is_orderby.value) {
      is_orderby.value = false
      proxy.$refs.searchOrderby.close()
    } else {
      is_orderby.value = true
      proxy.$refs.searchOrderby.open()
    }
  })
}
const selectOrderby = val => {
  is_orderby.value = false
  state.s_orderby = val
  page.value = 1
  getList(true)
}

const changTime = () => {
  proxy.$refs.searchOrderby.close()
  is_orderby.value = false
  is_area.value = false
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}
const closeItem = () => {
  is_time.value = false
  is_orderby.value = false
  is_area.value = false
}
const selectTime = val => {
  is_time.value = false
  state.s_time = val
  page.value = 1
  getList(true)
}

const changArea = () => {
  proxy.$refs.searchOrderby.close()
  proxy.$refs.searchTime.close()
  is_time.value = false
  is_orderby.value = false
  //获取地区数据
  if (areaList.value.length) {
    is_area.value = true
    proxy.$refs['areaEdit'].open()
    return
  }
  getDistPicker().then(res => {
    if (res.ret == 1) {
      areaList.value = res.result
      nextTick(() => {
        is_area.value = true
        proxy.$refs['areaEdit'].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
}
const closeArea = () => {
  is_area.value = false
}

const sendArea = obj => {
  state.s_area1 = (obj[0] && obj[0].value) || 0
  area.area1 = (obj[0] && obj[0].text) || ''
  state.s_area2 = (obj[1] && obj[1].value) || 0
  area.area2 = (obj[1] && obj[1].text) || ''
  state.s_area3 = (obj[2] && obj[2].value) || 0
  area.area3 = (obj[2] && obj[2].text) || ''
  state.s_area4 = (obj[2] && obj[2].value) || 0
  area.area4 = (obj[3] && obj[3].text) || ''
  is_area.value = false
  page.value = 1
  getList(true)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getActivity({
    page: page.value,
    ...state,
  }).then(res => {
    if (res.ret == 1) {
      if (page.value == 1) {
        if (isWeiXin()) setWechatShare(res.result.share_data)
      }
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const shareActivity = id => {
  proxy.$refs.activityShare.open(id)
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}
const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

onActivated(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}

.filter {
  margin: 0 0.4267rem;
  height: 1.0667rem;
  border-radius: 0.32rem;
  background: rgba(255, 255, 255, 0.5);
  margin-bottom: 0.3733rem;
  div {
    flex: 1;
    color: #2a2546;
    text-align: center;
    span {
      margin-right: 0.1333rem;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: normal;
      max-width: 1.8667rem;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .color_c3 {
      color: #c5c3c7;
    }

    .hide {
      transform: rotate(90deg);
    }
    .show {
      transform: rotate(-90deg);
      position: relative;
      top: 0.0267rem;
    }
  }
}
.list {
  padding: 0.1067rem 0.4267rem;
}
</style>

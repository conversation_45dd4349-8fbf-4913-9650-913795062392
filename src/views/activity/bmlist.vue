<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">报名成功记录</div>
    </div>

    <oeui-list :top="1.1733" @refresh="refresh" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="list">
        <list_item :item="item" v-for="item in list" :key="item.bmid" />
      </div>
    </oeui-list>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onActivated } from 'vue'
import { useRoute } from 'vue-router'
import oeuiList from '@/oeui/list.vue'
import list_item from './components/user_item.vue'
import { getActivityBmlist } from '@/api/activity.js'
const { proxy } = getCurrentInstance()

const OEUI = proxy.OEUI

const route = useRoute()
const list = ref([])
const page = ref(1)
const pageCount = ref('0')
const listStatus = ref('loading')

const getList = (flag, callback) => {
  if (page.value == 0) return
  getActivityBmlist({
    page: page.value,
    s_actid: route.query.id,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}
const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

getList()
</script>

<style lang="scss" scoped>
.main {
  position: absolute;
  width: 100vw;
  min-height: 100vh;
  background: #f7f8fa;
  .list {
    padding: 0.1067rem 0.4267rem;
  }
}
</style>

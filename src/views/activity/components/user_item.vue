<!-- @format -->

<template>
  <div class="item_box" v-if="item.bmid && item?.user?.userid" @click="$router.push('/user/detail?id=' + item.user.userid)">
    <div class="tit flex flex_ac flex_jsb">
      <div class="time flex flex_ac">
        <i class="iconfont icon-shizhong"></i>
        <p>报名时间：{{ getTime(item.bmtime) }}</p>
      </div>
      <div class="status color_green" v-if="item.rwflag == 1">已奖励</div>
      <div class="status" v-else-if="item.rwflag == 0">待奖励</div>
      <div class="status color_red" v-else-if="item.rwflag == 2">不奖励</div>
    </div>
    <div class="info flex flex_ac flex_jsb">
      <div class="flex_1 flex flex_ac">
        <div class="head">
          <img v-if="item?.user?.headimg" type="user" v-lazy="item?.user?.headimg_s_url" alt="" />
          <template v-else>
            <img v-if="item?.user.gender == 1" src="@/assets/images/gender_1.png" alt="" />
            <img v-else-if="item?.user.gender == 2" src="@/assets/images/gender_2.png" alt="" />
            <img v-else src="@/assets/images/gender_0.png" alt="" />
          </template>
        </div>
        <div class="flex_1 flex flex_ac">
          <p class="name">{{ item?.user?.username }}</p>
          <i class="iconfont icon-nanbiao-01" v-if="item?.user?.gender == 1"></i>
          <i class="iconfont icon-nvbiao-01" v-else-if="item?.user?.gender == 2"></i>
        </div>
      </div>
      <span class="money" v-if="item.fee > 0">报名费用：¥{{ item.fee }} </span>
      <span class="money" v-else>报名费用：免费</span>

      <!--<span class="money" v-if="item.rwflag == 1">佣金：+{{ item?.rw1 }}</span>
      <span class="money undone" v-else>佣金：{{ item?.rw1 }}</span>-->
    </div>
    <div class="handle flex flex_ac flex_jsb" v-if="item.rw1 > 0 || (unionInfo.unionid == item.leader_id && item.leader_rw > 0)">
      <template v-if="item.rw1 > 0">
        <p v-if="item.rwflag == 1">
          推广佣金：<span class="success">+¥{{ item.rw1 }}</span>
        </p>
        <p v-else>推广佣金：¥{{ item.rw1 }}</p>
      </template>
      <template v-if="unionInfo.unionid == item.leader_id && item.leader_rw > 0">
        <p v-if="item.rwflag == 1">
          团长佣金：<span class="success">+¥{{ item.leader_rw }}</span>
        </p>
        <p v-else>团长佣金：¥{{ item.leader_rw }}</p>
      </template>
    </div>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, computed } from 'vue'

  import { getTime } from '@/utils/hooks.js'
  import { useStore } from 'vuex'
  const { proxy } = getCurrentInstance()
  const store = useStore()

  const unionInfo = computed(() => store.state.unionInfo)

  defineProps({
    item: {
      type: Object
    }
  })
</script>

<style lang="scss" scoped>
  .item_box {
    margin-bottom: 0.32rem;
    cursor: pointer;
    background: #fff;
    border-radius: 0.32rem;
    padding: 0 0.32rem;
    padding-top: 0.32rem;

    .tit {
      .time {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        color: #999999;
        i {
          position: relative;
          top: 0.0267rem;
          margin-right: 0.1067rem;
        }
      }
      .status {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        color: #999999;
      }
    }
    .info {
      margin-top: 0.32rem;
      margin-bottom: 0.32rem;
      .head {
        width: 1.1733rem;
        height: 1.1733rem;
        border-radius: 0.32rem;
        overflow: hidden;
        margin-right: 0.32rem;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .name {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: 500;
        color: #31293b;
        max-width: 2.6667rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      i {
        position: relative;
        top: 0.0267rem;
        margin-left: 0.1333rem;
      }
      .icon-nanbiao-01 {
        color: #0570f1;
      }
      .icon-nvbiao-01 {
        color: #fe6897;
      }
      .money {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: 500;
        color: #666;
      }
    }
    .handle {
      border-top: 0.0267rem solid #f4f6f7;
      padding: 0.32rem 0;
      p {
        flex: 1;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: normal;
        color: #3d3d3d;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        .success {
          color: $color_main;
        }
      }
    }
  }
</style>

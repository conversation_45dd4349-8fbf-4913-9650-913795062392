<!-- @format -->

<template>
  <div class="pf popCardBox" ref="cardBox" style="top: 2000rem">
    <card-public id="popCardBox" ref="popCardBox" :info="info" @save="generateCard"></card-public>
  </div>

  <oe_popup ref="cardContent" :maskClose="false" width="6.4rem" mode="center" background="none">
    <div class="cardContent">
      <div class="content">
        <img :src="cardUrl" v-if="cardUrl" alt="" />
      </div>
      <p class="tips">长按图片保存/分享</p>
      <span class="btn flex_dc flex_v">
        <i @click="closeCard" class="iconfont icon-guanbi1 close"></i>
      </span>
    </div>
  </oe_popup>
</template>

<script setup>
  import cardPublic from '@/views/activity/components/card_public_activity.vue'
  import oe_popup from '@/oeui/popup.vue'
  import html2canvas from 'html2canvas'
  import {  getCurrentInstance, nextTick, watch, ref } from 'vue'

  const props = defineProps({
    info: {
      type: Object
    }
  })
  const { proxy } = getCurrentInstance()
  const OEUI = proxy.OEUI
  const cardUrl = ref('')
  const carid = ref(null)
  const open = id => {
    if (id != carid.value) {
      cardUrl.value = ''
    }
    carid.value = id
    if (cardUrl.value) {
      proxy.$refs.cardContent.open()
    } else {
      proxy.$refs.popCardBox.open(id || props.info.actid)
    }
  }

  //生成卡片
  let generateCard = () => {
    nextTick(() => {
      let imgWidth = proxy.$refs.cardBox.offsetWidth || 375
      let imgHeight = proxy.$refs.cardBox.offsetHeight || 667
      OEUI.loading.show('海报生成中')
      let el = document.querySelector('#popCardBox')
      html2canvas(el, {
        scale: 2,
        allowTaint: true,
        useCORS: true,
        width: imgWidth,
        height: imgHeight,
        imageTimeout: 5000,
        backgroundColor: '#fff' //设置背景颜色
      })
        .then(canvas => {
          OEUI.loading.hide()
          let url = canvas.toDataURL('image/png')
          cardUrl.value = url
          nextTick(() => {
            proxy.$refs.cardContent.open()
          })
        })
        .catch(() => {
          OEUI.loading.hide()
          OEUI.toast('生成推广海报失败')
        })
    })
  }
  const closeCard = () => {
    proxy.$refs.cardContent.close()
  }

  defineExpose({
    open
  })
  watch(
    () => props.info,
    newInfo => {
      if (newInfo) {
        cardUrl.value = ''
      }
    },
    { immediate: true }
  )
</script>
<style lang="scss" scoped>
  .popCardBox {
    width: 10rem;
    height: 17.7867rem;
  }
  .cardContent {
    font-family: PingFang SC, PingFang SC;
    .content {
      width: 6.4rem;
      height:  11.3835rem;
      border-radius: 0.32rem;
      overflow: hidden;
      background: #fff;
      img {
        width: 100%;
        object-fit: cover;
        border-radius: 0.32rem;
      }
    }
    .tips {
      color: #fff;
      text-align: center;
      font-size: 0.3733rem;

      font-weight: normal;
      line-height: 0.5333rem;
      margin-top: 0.32rem;
    }
    .btn {
      span {
        background: #fff;
        font-size: 0.3733rem;
        font-weight: normal;
        color: $color_main;
        line-height: 0.5333rem;
        padding: 0.16rem 0.5067rem;
        border-radius: 0.4267rem;
        cursor: pointer;
        margin-top: 0.64rem;
      }
      .close {
        font-size: 0.5867rem;
        color: #fff;
        cursor: pointer;
        margin-top: 0.64rem;
      }
    }
  }

  .cardMore {
    font-family: PingFang SC, PingFang SC;

    .back {
      position: absolute;
      width: 1.1733rem;
      height: 1.1733rem;
      left: 0;
      top: 0;
      z-index: 300;
      i {
        font-size: 0.64rem;
        color: #666;
      }
    }
    .title {
      text-align: center;
      font-size: 0.48rem;
      font-weight: 600;
      padding-top: 0.5333rem;
    }
    .list {
      padding: 0 0.4267rem;
      margin-top: 0.64rem;
      flex-wrap: wrap;
      height: calc(100vh - 2.1333rem);
      overflow-y: scroll;
      .item {
        border-radius: 0.2667rem;
        background: #fff;
        width: calc(50% - 0.24rem);
        padding: 0.2667rem;
        margin-right: 0.2667rem;
        margin-bottom: 0.32rem;
        box-sizing: border-box;

        .content {
          width: 100%;
          border-radius: 0.2133rem;
          overflow: hidden;
          img {
            width: 100%;
            object-fit: cover;
          }
        }
        .btn {
          margin-top: 0.2667rem;
          font-size: 0.32rem;
          line-height: 1;
          border: 0.0267rem solid #666;
          padding: 0.08rem 0.2133rem;
          color: #666;
          border-radius: 0.2667rem;
          box-sizing: border-box;
        }
        &.current {
          background: #fdecff;
          border: 1px solid #d4a6fb;
          .btn {
            color: #ffffff;
            border: none;
            height: 0.5333rem;
            background: linear-gradient(90deg, #8c62fe 0%, #ce63fd 100%);
          }
        }
      }
      .item:nth-child(2n) {
        margin-right: 0;
      }
    }
  }
</style>

<!-- @format -->

<template>
  <!-- 海报定位 -->
  <div class="pr" style="width: 10rem; height: 17.787rem">
    <img class="pa lt w100" src="@/assets/images/poster/poster2_bg.png" />
    <div class="pr" style="z-index: 1">
      <div class="pa oh l39 r40" style="top: 1.813rem; height: 5.013rem">
        <img class="w100" :src="info.drawimg_base64" v-if="info.drawimg_base64" />
        <img src="@/assets/images/loading_img.png" class="w100" v-else />
      </div>
      <div class="pa fz15 lh15 color_f" style="top: 7.493rem; left: 1.467rem">告别单身一起邂逅你的TA~</div>
      <img class="pa" style="left: 1.067rem; top: 8.347rem; width: 7.6rem" src="@/assets/images/poster/poster2_tit.png" />
      <span class="pa bo_fgs" style="left: 1.12rem; top: 9.467rem; width: 7.733rem"></span>
      <div class="pa lh39 fz15 color_6" style="left: 1.173rem; top: 9.813rem">
        <p>
          <i class="fb color_3">活动人数：</i>
          不限制
        </p>
        <p>
          <i class="fb color_3">活动时间：</i>
          {{ info.starttime > 0 ? getTime(info.starttime) : '--' }}
        </p>
        <p>
          <i class="fb color_3">截止报名时间：</i>
          {{ info.endbmtime > 0 ? getTime(info.endbmtime) : '--' }}
        </p>
      </div>
      <p class="color_f fz13 pa" style="left: 0.907rem; top: 15.093rem">在对的时间开启你的寻爱之旅</p>
      <p class="color_f fz13 pa" style="left: 0.907rem; top: 15.84rem">一起邂逅你的TA~</p>
      <div class="pa img_82 bg_f p5 br5 bsb" style="right: 0.747rem; top: 14.187rem">
        <img class="w100" :src="qrcode_img" v-if="qrcode_img" />
        <img src="@/assets/images/loading_img.png" class="w100" v-else />
      </div>
    </div>
  </div>
</template>
<script setup>
import { getCurrentInstance, ref, nextTick } from 'vue'
import { getActivityQrcode } from '@/api/activity.js'
import { getTime } from '@/utils/hooks.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const emit = defineEmits(['save'])
const props = defineProps({
  info: {
    type: Object,
  },
})

const qrcode_img = ref('')

// 获取模板列表
const getQrcode = id => {
  OEUI.loading.show('模板加载中')
  getActivityQrcode({ id })
    .then(res => {
      OEUI.loading.hide()
      if (res.ret == 1) {
        qrcode_img.value = res.result.data
        nextTick(() => {
          emit('save')
        })
      } else {
        OEUI.toast('获取模板失败')
      }
    })
    .catch(() => {
      OEUI.toast('获取模板失败')
    })
}

const open = id => {
  getQrcode(id || props.info.actid)
}

defineExpose({
  open,
})
</script>
<style lang="scss" scoped>
.color_3 {
  color: #333;
}
.fb {
  font-weight: 600;
}
.color_f {
  color: #fff;
}
.bg_f {
  background: #fff;
}
.l15 {
  left: 0.4rem;
}
.lt {
  left: 0;
  top: 0;
}
.l33 {
  left: 0.88rem;
}
.r33 {
  right: 0.88rem;
}
.fz14 {
  font-size: 0.3733rem;
}
.fz15 {
  font-size: 0.4rem;
}
.color_6 {
  color: #666;
}
.lh39 {
  line-height: 1.04rem;
}
.fz13 {
  font-size: 0.3467rem;
}
.img_82 {
  width: 2.1867rem;
  height: 2.1867rem;
}
.w100 {
  width: 100%;
}
.p5 {
  padding: 0.1333rem;
}
.br5 {
  border-radius: 0.1333rem;
}
.bsb {
  box-sizing: border-box;
}
.l39 {
  left: 1.04rem;
}
.r40 {
  right: 1.0667rem;
}
</style>

<!-- @format -->

<template>
  <div class="main">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="nav"></div>
    <div class="head">
      <img v-if="info.drawimg" v-lazy="info.drawimg_url" alt="" />
      <img v-else v-lazy="info.drawimg_url" alt="" />
    </div>
    <div class="content">
      <div class="info">
        <p class="title">{{ info.title }}</p>
        <div class="monery">
          <div class="data">佣金：女士 ¥{{ info.ps2 }} ｜ 男士 ¥{{ info.ps1 }}</div>
          <div class="price flex flex_ac flex_jsb">
            <div class="flex flex_ac">
              <span>
                女士：
                <template v-if="info.price2 > 0">¥{{ info.price2 }}</template>
                <template v-else>免费</template>
              </span>
              <span>
                男士：
                <template v-if="info.price1 > 0">¥{{ info.price1 }}</template>
                <template v-else>免费</template>
              </span>
            </div>
            <span style="margin-right: 0" @click="proxy.$refs.bmfee_dialog.open()">
              查看全部
              <i class="iconfont icon-youjiantou-01 pr" style="top: 0.0267rem; color: #000"></i>
            </span>
          </div>
        </div>
        <div class="time flex flex_ac flex_jsb">
          <p class="name flex flex_ac">
            <i class="iconfont icon-shizhong"></i>
            活动时间
          </p>
          <p class="value">{{ getWeekText(info.starttime) }} {{ getTime(info.starttime) }}</p>
        </div>
        <div @click="proxy.$refs.term_dialog.open()" class="term flex flex_ac flex_jsb">
          <span class="flex flex_ac name">
            <i class="iconfont icon-baomingjilu"></i>
            互选报名条件
          </span>
          <span class="flex flex_ac">
            查看
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
      </div>
      <div class="bmdata">
        <div class="tit flex flex_ac flex_jsb">
          <div class="flex flex_ac">
            <span>报名人数：</span>
            <p v-if="info.bmnums > 0">
              男
              <em>{{ info.bm_user1 }}</em>
              人&emsp;女
              <em>{{ info.bm_user2 }}</em>
              人
            </p>
          </div>
          <span v-if="false" class="flex_dc" @click="$router.push('/activity/bmlist?id=' + info.actid)">
            查看全部
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="nums flex flex_ac" v-if="info.bmnums > 0">
          <template v-for="(item, index) in info.bmuser" :key="item.bmid">
            <span v-if="index < 7">
              <img v-lazy="item.user?.headimg_s_url" type="user" alt="" />
            </span>
          </template>
          <span v-if="false" @click="$router.push('/party/bmlist?id=' + info.actid)">
            <i class="iconfont icon-more"></i>
          </span>
        </div>
        <p v-else>暂无报名</p>
      </div>
      <div class="intro flex flex_ac flex_jsb">
        <div class="flex flex_ac flex_1 ws">
          <i class="iconfont icon-tishi"></i>
          <span>佣金说明：</span>
          <p class="ws">推广用户报名参与互选后推广用户报名参与互选后推广用户报名参与互选后推广用户报名参与互选后...</p>
        </div>
        <span class="flex_s" @click="showIntro">查看</span>
      </div>
      <div class="detail">
        <p class="tit">互选详情</p>
        <div class="value" v-if="info.content" v-html="info.content"></div>
        <div class="value" v-else>暂无详情</div>
      </div>
    </div>
    <div class="h80"></div>
    <div class="btn flex_dc">
      <span class="flex_1 flex_dc" @click="$router.push('/activity/bmlist?id=' + info.actid)">
        <i class="iconfont icon-baomingjilu"></i>
        报名记录
      </span>
      <span class="flex_1 flex_dc" @click="btnShare">
        <i class="iconfont icon-fenxiangfangshi"></i>
        推广分享
      </span>
    </div>
  </div>

  <oe_popup ref="intro_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">佣金说明</p>
      <p class="tip">
        推广用户报名参与活动/互选后且成功支付相关费用，系统根据对应的平台用户报名费用将对应佣金在活动/互选结束后结算至绑定推广红娘账户。如系统设置用户报名费用为0元，则推广红娘对应获取佣金也为0元。
      </p>
      <div @click="proxy.$refs.intro_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <oe_popup ref="term_dialog" width="100%" background="#F2F4F5">
    <div class="term_dialog pr">
      <div class="h44 flex_dc top">
        <span @click="proxy.$refs.term_dialog.close()" class="back flex_dc iconfont icon-a-fenxiangheibeifen-01"></span>
        <span class="title">互选报名条件</span>
      </div>
      <div class="list">
        <div class="item">
          <span>实名要求：</span>
          <span class="value" v-if="info.cond_idrz == 1">已实名</span>
          <span v-else>不限</span>
        </div>
        <div class="item">
          <span>头像要求：</span>
          <span class="value" v-if="info.cond_avatar == 1">有头像</span>
          <span v-else>不限</span>
        </div>
        <div class="item">
          <span>VIP要求：</span>
          <span class="value" v-if="info.cond_vip == 1">VIP用户</span>
          <span v-else>不限</span>
        </div>
        <div class="item">
          <span>地区要求：</span>
          <span class="value" v-if="info.cond_dist1 > 0">{{ info.cond_dist1_t }} {{ info.cond_dist2_t }} {{ info.cond_dist3_t }} {{ info.cond_dist4_t }}</span>
          <span v-else>不限</span>
        </div>
        <div class="item">
          <span>户籍要求：</span>
          <span class="value" v-if="info.cond_home1 > 0">{{ info.cond_home1_t }} {{ info.cond_home2_t }} {{ info.cond_home3_t }} {{ info.cond_home4_t }}</span>
          <span v-else>不限</span>
        </div>
        <div class="item">
          <span>年龄要求：</span>
          <span class="value" v-if="info.cond_age1 > 0 && info.cond_age2 > 0">{{ info.cond_age1 }} - {{ info.cond_age2 }}岁</span>
          <span v-else>不限</span>
        </div>
        <div class="item">
          <span>婚况要求：</span>
          <span class="value" v-if="info.cond_marry > 0">
            {{ info.cond_marry_t }}
          </span>
          <span v-else>不限</span>
        </div>
        <div class="item">
          <span>学历要求：</span>
          <span class="value" v-if="info.cond_education > 0">
            {{ info.cond_education_t }}
          </span>
          <span v-else>不限</span>
        </div>
        <div class="item">
          <span>购房要求：</span>
          <span class="value" v-if="info.cond_house > 0">
            {{ info.cond_house_t }}
          </span>
          <span v-else>不限</span>
        </div>
        <div class="item">
          <span>购车要求：</span>
          <span class="value" v-if="info.cond_car > 0">
            {{ info.cond_car_t }}
          </span>
          <span v-else>不限</span>
        </div>
      </div>
    </div>
  </oe_popup>

  <oe_popup ref="bmfee_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">报名费用</p>
      <div class="tip" style="padding: 0 0.2133rem">
        <div v-for="item in info.price_list" :key="item.vipid" class="flex flex_ac flex_jsb" style="padding: 0.2133rem 0; border-bottom: 0.0267rem solid #f2f4f5">
          <span style="color: #000">{{ item.vipname }}</span>
          <span style="color: #666">男:{{ item.fee1 > 0 ? '¥' + item.fee1 : '免费' }} &emsp;女:{{ item.fee2 > 0 ? '¥' + item.fee2 : '免费' }}</span>
        </div>
      </div>
      <div @click="proxy.$refs.bmfee_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <activity_share ref="activityShare" />
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import oe_popup from '@/oeui/popup.vue'
import activity_share from '@/views/activity/components/activity_share.vue'
import { getActivityDetail } from '@/api/activity.js'
import { getTime, getWeekText } from '@/utils/hooks.js'
import { isWeiXin, setWechatShare } from '@/utils/jssdk'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const route = useRoute()

const info = ref({})

const getDetail = () => {
  getActivityDetail({
    id: route.query.id,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data.content = setHtmlExp(res.result.data.content)
      info.value = res.result.data
      if (isWeiXin()) setWechatShare(res.result.share_data)
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const setHtmlExp = result => {
  if (result) {
    let exp = new RegExp('&amp;nbsp;', 'g')
    result = result
      .replace(result ? /&(?!#?\w+;)/g : /&/g, '&amp;')
      .replace(/&lt;/g, '<')
      .replace(/<img/g, '<img style="max-width: 100%;height: auto;border-radius: .32rem;" ')
      .replace(/height=/g, '')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&rarr;/g, '-')
      .replace(/&ldquo;/g, '"')
      .replace(/&rdquo;/g, '"')
    return result.replace(exp, '\u3000')
  }
}

const showIntro = () => {
  proxy.$refs.intro_dialog.open()
}

const btnShare = () => {
  proxy.$refs.activityShare.open(route.query.id)
}

onMounted(() => {
  getDetail()
})
</script>

<style lang="scss" scoped>
.main {
  position: relative;
  background: #f2f4f5;
  width: 100%;
  min-height: 100vh;
  .back {
    width: 0.64rem;
    height: 0.64rem;
    border-radius: 0.1067rem;
    background: #00000081;
    color: #fff;
    position: fixed;
    top: 0.2667rem;
    left: 0.4267rem;
    z-index: 300;
  }
  .nav {
    font-size: 0.48rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #2a2546;
    line-height: 1.1733rem;
    text-align: center;
    position: absolute;
    width: 100%;
  }
  .head {
    height: 4.84rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .content {
    padding: 0 0.2667rem;
    margin-top: -0.5333rem;
    position: relative;
    z-index: 100;
    .info {
      background: #fff;
      padding: 0.4267rem 0.4267rem 0.32rem 0.32rem;
      border-radius: 0.32rem;
      .title {
        font-size: 0.48rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        color: #31293b;
        line-height: 0.6667rem;
        margin-bottom: 0.2133rem;
      }
      .monery {
        box-sizing: border-box;
        font-family: PingFang SC-Regular, PingFang SC;
        .data {
          color: #7d68fe;
          font-size: 0.48rem;
          line-height: 0.6667rem;
          font-weight: 500;
        }
        .price {
          margin-top: 0.1067rem;
          font-size: 0.32rem;
          line-height: 0.4533rem;
          font-weight: normal;
          display: flex;
          align-items: center;
          color: #999999;
          span {
            margin-right: 0.5333rem;
          }
        }
      }
      .time {
        margin-top: 0.4267rem;
        font-family: PingFang SC, PingFang SC;
        .name {
          font-size: 0.3733rem;
          font-weight: normal;
          color: #31293b;
          line-height: 0.5333rem;
          i {
            margin-right: 0.2133rem;
            font-weight: 500;
          }
        }
        .value {
          font-size: 0.32rem;
          font-weight: normal;
          color: #666666;
          line-height: 0.4533rem;
        }
      }
      .term {
        margin-top: 0.4267rem;
        background: #f2f4f5;
        padding: 0.32rem;
        border-radius: 0.32rem;
        font-family: PingFang SC, PingFang SC;
        span {
          font-size: 0.3733rem;
          font-weight: normal;
          color: #666666;
          line-height: 0.5333rem;
        }
        .name {
          font-weight: 500;
          color: #31293b;
          i {
            font-size: 0.48rem;
            margin-right: 0.1333rem;
          }
        }
      }
    }
    .bmdata {
      margin-top: 0.32rem;
      background: #fff;
      padding: 0.4267rem;
      border-radius: 0.32rem;
      font-family: PingFang SC, PingFang SC;
      .tit {
        > div {
          span {
            font-size: 0.4267rem;
            font-weight: 500;
            color: #31293b;
            line-height: 0.5867rem;
          }
          p {
            font-size: 0.3733rem;
            font-weight: normal;
            color: #31293b;
            position: relative;
            top: 0.0267rem;
          }
          em {
            color: $color_main;
          }
        }
        > span {
          font-size: 0.3733rem;
          font-weight: normal;
          color: #666666;
          line-height: 0.5333rem;
          i {
            position: relative;
            top: 0.0267rem;
          }
        }
      }
      .nums {
        margin-top: 0.4267rem;
        > span:first-child {
          margin-left: 0;
        }
        span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 1.3333rem;
          height: 1.3333rem;
          border: 0.0667rem solid #fff;
          background: #fff;
          border-radius: 50%;
          overflow: hidden;
          box-sizing: border-box;
          margin-left: -0.2667rem;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          i {
            color: #000;
            font-size: 0.5333rem;
          }
        }
      }
    }
    .intro {
      margin-top: 0.32rem;
      background: #fff;
      border-radius: 0.32rem;
      padding: 0.4267rem 0.32rem;
      font-family: PingFang SC, PingFang SC;
      > div {
        margin-right: 0.4rem;
        font-size: 0.3733rem;
        font-weight: 400;
        line-height: 0.5333rem;
        i {
          color: #000;
          margin-right: 0.2133rem;
        }
        span {
          color: #31293b;
        }
        p {
          color: #666666;
        }
      }

      > span {
        cursor: pointer;
        background: $color_main;
        color: #fff;
        font-size: 0.3733rem;
        font-weight: normal;
        line-height: 0.5333rem;
        padding: 0.0533rem 0.32rem;
        border-radius: 0.32rem;
      }
    }
    .detail {
      margin-top: 0.32rem;
      font-family: PingFang SC, PingFang SC;
      .tit {
        padding: 0.2667rem;
        font-size: 0.48rem;
        font-weight: 500;
        color: #2a2546;
        line-height: 0.6667rem;
      }
    }
  }
  .btn {
    position: fixed;
    width: 100%;
    height: 1.6rem;
    bottom: 0.2667rem;
    z-index: 300;
    font-family: PingFang SC, PingFang SC;
    span {
      i {
        margin-right: 0.2133rem;
        font-size: 0.48rem;
      }
      width: fit-content;
      cursor: pointer;
      background: $color_main;
      font-size: 0.4267rem;
      font-weight: normal;
      color: #ffffff;
      line-height: 0.5867rem;
      padding: 0.2667rem 0;
      border-radius: 0.5867rem;
      margin: 0 0.8533rem;
    }
  }
}

.term_dialog {
  font-family: PingFang SC, PingFang SC;
  .top {
    position: sticky;
    top: 0;
    z-index: 300;
    background: #f2f4f5;
    .back {
      width: 1.1733rem;
      height: 1.1733rem;
      position: absolute;
      left: 0;
      font-size: 0.64rem;
    }
    .title {
      font-size: 0.4267rem;
      line-height: 0.64rem;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #2a2546;
    }
  }
  .list {
    padding: 0.1067rem 0.4267rem;
    height: calc(100vh - 1.3867rem);
    overflow-y: scroll;
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #fff;
      border-radius: 0.32rem;
      padding: 0.4267rem;
      margin-bottom: 0.32rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5333rem;
      .value {
        font-weight: 500;
        color: #31293b;
      }
    }
  }
}
</style>

<style lang="scss">
.detail {
  .value {
    padding: 0 0.2667rem;
    margin-top: 0.32rem;
    font-size: 0.3733rem;
    line-height: 0.64rem;
    color: #2a2546;
    font-weight: normal;
    white-space: normal;
    word-break: break-word;

    img {
      margin: .2667rem 0;
      max-width: 100%;
      object-fit: cover;
      border-radius: 0.32rem;
    }
  }
}
</style>
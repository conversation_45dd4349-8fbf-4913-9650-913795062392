<!-- @format -->

<template>
  <div class="main">
    <!-- <div v-if="unionInfo.enter_name" class="enterprise-box flex flex_ac">
      <img src="@/assets/images/enterprise.svg" alt="" />
      <span>{{ unionInfo.enter_name }}</span>
    </div> -->
    <div class="data flex flex_ac" v-if="unionInfo.unionid > 0">
      <div class="head flex_s">
        <img v-lazy="unionInfo.headimg_url" :key="unionInfo.headimg_url" alt="" type="union" />
      </div>
      <din class="info flex_1 ws">
        <div class="name flex flex_ac flex_jsb">
          <p class="username ws">
            {{ unionInfo.unionname }}
          </p>
          <span @click="$router.push('/edit')" class="edit flex_s flex_dc">
            编辑资料
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="id">编号：{{ unionInfo.unionid }}</div>
        <div class="grade">
          <span @click="$router.push('/level1')" v-if="Number(unionInfo.level1_flag) > 0 && unionInfo.level1 > 0" class="group" :class="'group' + unionInfo.level1">
            <i>
              <img :src="getPgroupUrl('pgroup', unionInfo.level1)" alt="" />
            </i>
            <em>{{ unionInfo.level1_name }}</em>
          </span>

          <span @click="$router.push('/level2')" v-if="Number(unionInfo.level2_flag) > 0 && unionInfo.level2 > 0 && config.union_partner == 2" class="group" :class="'group' + unionInfo.level2">
            <i>
              <img :src="getPgroupUrl('tgroup', unionInfo.level2)" alt="" />
            </i>
            <em>{{ unionInfo.level2_name }}</em>
          </span>

          <span @click="$router.push('/level3')" v-if="Number(unionInfo.level3_flag) > 0 && unionInfo.level3 > 0" class="group" :class="'group' + unionInfo.level3">
            <i>
              <img :src="getPgroupUrl('sgroup', unionInfo.level3)" alt="" />
            </i>
            <em>{{ unionInfo.level3_name }}</em>
          </span>

          <span @click="$router.push('/level4')" v-if="Number(unionInfo.level4_flag) > 0 && unionInfo.level4 > 0" class="group" :class="'group' + unionInfo.level4">
            <i>
              <img :src="getPgroupUrl('cgroup', unionInfo.level4)" alt="" />
            </i>
            <em>{{ unionInfo.level4_name }}</em>
          </span>
        </div>
      </din>
    </div>
    <div class="data_skelecton flex flex_ac" v-else>
      <div class="head"></div>
      <div class="flex_1 flex flex_v">
        <p></p>
        <span></span>
        <div class="flex flex_ac">
          <em></em>
          <em></em>
          <em></em>
        </div>
      </div>
    </div>

    <!-- <template v-if="unionInfo.unionid > 0">
      <div v-if="unionInfo.level1_flag != 1 || unionInfo.level2_flag != 1 || unionInfo.level3_flag != 1" @click="$router.push('/active')" class="more_role">
        <div class="">
          <div class="title">
            <span>
              <img src="@/assets/images/role.png" alt="" />
            </span>
            <p>更多身份</p>
          </div>
          <p class="tip">推广/团长/服务</p>
        </div>
        <span class="btn">申请合作</span>
      </div>
    </template> -->
    <template v-if="unionInfo.unionid > 0">
      <!--推广-->
      <template v-if="unionInfo.level1_flag == 1">
        <p class="tit">推广功能</p>
        <div class="menu">
          <!-- <template v-if="config.union_partner == 2">
            <div class="item" v-if="unionInfo.be_leaderid > 0" @click="openLeader">
              <span class="name">隶属团队</span>
              <div class="event">
                <span>{{ unionInfo.be_leadername }}</span>
                <i class="iconfont icon-you"></i>
              </div>
            </div>
            <div class="item" v-else @click="joinTeam">
              <span class="name">隶属团队</span>
              <div class="event">
                <span style="color: #999">未加入任何团队</span>
                <i class="iconfont icon-you"></i>
              </div>
            </div>
          </template> -->
          <!-- <div class="item" @click="$router.push('/rwreg')">
            <span class="name">拉新奖励</span>
            <div class="event">
              <span>男{{ level.user_money1 }}元&emsp;女{{ level.user_money2 }}元</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div> -->
          <div v-if="config.nav_party == 1" class="item" @click="$router.push('/myparty')">
            <span class="name">活动推广</span>
            <div class="event">
              <span>推广{{ power.party_nums }}场</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
          <div v-if="config.nav_activity == 1" class="item" @click="$router.push('/myactivity')">
            <span class="name">互选推广</span>
            <div class="event">
              <span>推广{{ power.activity_nums }}场</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
          <!-- <div class="item" @click="$router.push('/rwbuy')">
            <span class="name">消费分成</span>
            <div class="event">
              <span>用户消费可获分成</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div> -->
          <div class="item" @click="$router.push('/myuser')">
            <span class="name">名下用户</span>
            <div class="event">
              <span>有效用户 {{ power.user_nums }}人</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
          <div class="item" @click="$router.push('/poster_code')">
            <span class="name">下载推广码</span>
            <div class="event">
              <i class="iconfont icon-erweima" style="font-size: 0.64rem; color: #000"></i>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
        </div>
      </template>
      <!-- 合伙人模式下不显示团长功能 -->
      <!--团长-->
      <template v-if="unionInfo.level2_flag == 1 && config.union_partner == 2">
        <p class="tit">团长功能</p>
        <div class="menu">
          <div class="item" @click="openLeadername">
            <span class="name">团队名称</span>
            <div class="event">
              <span>{{ unionInfo.leader_name }}</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
          <div class="item" @click="$router.push('/teamunion')">
            <span class="name">团队红娘</span>
            <div class="event">
              <span>{{ power.leadunion_nums }}人</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
          <div class="item" @click="$router.push('/teamuser')">
            <span class="name">团队用户</span>
            <div class="event">
              <span>{{ power.leaduser_nums }}人</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
          <div class="item" @click="$router.push('/teamlog')">
            <span class="name">团队记录</span>
            <div class="event">
              <span>入团退团记录</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
        </div>
      </template>
      <!--服务-->
      <template v-if="unionInfo.level3_flag == 1">
        <p class="tit">服务功能</p>
        <div class="menu">
          <div class="item" @click="$router.push('/myaudit')">
            <span class="name">资料审核</span>
            <div class="event">
              <span>待审核{{ power.audituser_nums }}人</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
          <div class="item" @click="$router.push('/myafter')">
            <span class="name">红娘帮约</span>
            <div class="event">
              <span>待帮约{{ power.after_nums }}人</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
          <div class="item" @click="$router.push('/myafteruser')">
            <span class="name">主动推荐</span>
            <div class="event">
              <span>已推荐{{ power.aftermatch_nums }}人</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
        </div>
      </template>
      <!--线下-->
      <template v-if="unionInfo.level4_flag == 1">
        <p class="tit">线下合作功能</p>
        <div class="menu">
          <div class="item" @click="$router.push('/crmincome')">
            <span class="name">门店业绩</span>
            <div class="event">
              <span>用户门店签约可获分成</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
          <div class="item" @click="$router.push('/crmmember')">
            <span class="name">潜在资源</span>
            <div class="event">
              <span>手动录入</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
        </div>
      </template>
      <!-- <p class="tit">其它功能</p>
      <div class="menu">
        <div v-if="config.app.mentor == 1 && unionInfo.is_mentor == 1" class="item" @click="goMmotion">
          <span class="name">导师中心</span>
          <div class="event">
            <span>{{ power.new_metor_order }}条咨询待服务</span>
            <i class="iconfont icon-you"></i>
          </div>
        </div>
        <div v-if="config.app.shop == 1 && unionInfo.is_shoper == 1" class="item" @click="goShop">
          <span class="name">商家中心</span>
          <div class="event">
            <span>待发货{{ power.new_shop_order }}</span>
            <i class="iconfont icon-you"></i>
          </div>
        </div>
        <div class="item" @click="goAccount">
          <span class="name">收款资料</span>
          <div class="event">
            <span>提现收款账号</span>
            <i class="iconfont icon-you"></i>
          </div>
        </div>
        <div class="item" @click="goWithdraw">
          <span class="name">申请提现</span>
          <div class="event">
            <span>提现到个人账户</span>
            <i class="iconfont icon-you"></i>
          </div>
        </div>
        <div class="item" @click="btnCheckUser">
          <span class="name">我要找对象</span>
          <div class="event">
            <span>前往单身相亲平台</span>
            <i class="iconfont icon-you"></i>
          </div>
        </div>
      </div> -->
    </template>
    <div class="menu" v-else>
      <div class="item skelecton" v-for="v in 8" :key="v">
        <span class="name">&emsp;&emsp;&emsp;&emsp;&emsp;</span>
        <div class="event">&emsp;&emsp;&emsp;</div>
      </div>
    </div>
    <template v-if="unionInfo.unionid > 0">
      <!-- <template v-if="config.union_partner == 1">
        <p class="tit">合伙人功能</p>
        <div class="menu">
          <div class="item" @click="$router.push('/myunion')">
            <span class="name">名下红娘</span>
            <div class="event">
              <span>{{ power.child_nums }}人</span>
              <i class="iconfont icon-you"></i>
            </div>
          </div>
        </div>
      </template> -->
      <!-- <template v-if="(config.app?.mentor && config.nav_mentor == 1) || (config.app?.devapp && config.nav_devapp == 1) || (config.app?.shop && config.nav_shop == 1) ">
        <p class="tit">其他功能</p>
        <div class="menu">
          <a v-if="config.app?.mentor && config.nav_mentor == 1" class="item" :href="config.siteurl + 'index.php?m=union&c=mentorcp'">
            <span class="name">导师中心</span>
            <div class="event">
              <span>我是导师</span>
              <i class="iconfont icon-you"></i>
            </div>
          </a>
          <a v-if="config.app?.devapp && config.nav_devapp == 1" class="item" :href="config.siteurl + 'index.php?m=union&c=device'">
            <span class="name">脱单盒子</span>
            <div class="event">
              <span>多样玩法终端</span>
              <i class="iconfont icon-you"></i>
            </div>
          </a>
          <a v-if="config.app?.shop && config.nav_shop == 1" class="item" :href="config.siteurl + 'index.php?m=union&c=shopcp'">
            <span class="name">卖家中心</span>
            <div class="event">
              <span>我是卖家</span>
              <i class="iconfont icon-you"></i>
            </div>
          </a>
        </div>
      </template> -->
      <div class="kefu flex_dc" @click="$router.push('/kefu')">联系客服</div>
    </template>
  </div>

  <tabbars />

  <oe_popup ref="leadername_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">团队名称</p>
      <div class="input">
        <input type="text" v-model="leader_name" placeholder="请输入团队名称" />
        <span v-if="leader_name" @click="resetLeadername" class="clear iconfont icon-guanbi1"></span>
      </div>
      <div style="margin-top: 0.64rem" @click="sendLeadrname" class="event">确认</div>
      <span @click="proxy.$refs.leadername_dialog.close()" class="close iconfont icon-guanbi2"></span>
    </div>
  </oe_popup>

  <!--团队信息-->
  <oe_popup ref="leader_dialog" width="8rem" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="leader_dialog">
      <p class="title">团队信息</p>
      <div class="data">
        <p class="tit">
          <i></i>
          业务数据
        </p>
        <div class="num flex_dc">
          <div class="item">
            <p class="earnings">{{ Number(leader.month_leadpay_amount) >= 10000 ? (Number(leader.month_leadpay_amount) / 10000).toFixed(2) : leader.month_leadpay_amount }}</p>
            <p>本月收益({{ Number(leader.month_leadpay_amount) >= 10000 ? '万' : '元' }})</p>
          </div>
          <div class="item">
            <p class="earnings">{{ Number(leader.leadpay_amount) >= 10000 ? (Number(leader.leadpay_amount) / 10000).toFixed(2) : leader.leadpay_amount }}</p>
            <p>累计收益({{ Number(leader.leadpay_amount) >= 10000 ? '万' : '元' }})</p>
          </div>
        </div>
      </div>
      <div class="info">
        <p class="tit">
          <i></i>
          团长信息
        </p>
        <div class="user" v-if="leader.unionid">
          <span class="head oh">
            <img v-lazy="leader.headimg_url" type="union" alt="" />
          </span>
          <div class="name">
            <div class="flex flex_ac">
              <p class="flex_s">{{ leader.unionname }}</p>
              <span class="group flex_s" :class="'group' + leader.level2" v-if="leader.unionid">
                <i>
                  <img :src="getPgroupUrl('pgroup', leader.level2)" alt="" />
                </i>
                <em>{{ leader.level2_name }}</em>
              </span>
            </div>
            <p class="id">编号：{{ leader.unionid }}</p>
          </div>
        </div>
      </div>
      <div class="contact">
        <div class="flex flex_ac flex_jsb">
          <p class="flex flex_ac">
            <i class="iconfont icon-dianhua2"></i>
            <span>{{ leader.mobile || '--' }}</span>
          </p>
          <span v-if="leader.mobile" class="btn" @click="callMobile(leader.mobile)">拨打</span>
        </div>
        <div class="flex flex_ac flex_jsb">
          <p class="flex flex_ac">
            <i class="iconfont icon-a-chakanweixinbai-01"></i>
            <span>{{ leader.weixin || '--' }}</span>
          </p>
          <span v-if="leader.weixin" class="btn" @click="copy(leader.weixin)">复制</span>
        </div>
      </div>
      <div class="event" @click="proxy.$refs.leader_dialog.close()">知道了</div>
      <div v-if="unionInfo.unionid != unionInfo.be_leaderid" @click="btnQuitTeam" class="out">申请退团</div>
    </div>
  </oe_popup>
  <oe_popup ref="successteam_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">提示</p>
      <p class="tip">您的退团申请已通过，已成功退出【{{ leaderName }}】 团队</p>
      <div @click="proxy.$refs.successteam_dialog.close()" class="event">确认</div>
    </div>
  </oe_popup>
  <oe_popup ref="auditteam_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">提示</p>
      <p class="tip">退团申请已提交,管理员通过审核后,将自动退出团队。</p>
      <div @click="proxy.$refs.auditteam_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>
  <oe_popup ref="failteam_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">提示</p>
      <p class="tip">{{ fail_msg }}</p>
      <div @click="proxy.$refs.failteam_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <!--开启交友-->
  <oe_popup ref="goUserDialog" mode="bottom" roundStyle=".64rem" round="true">
    <div class="goUserDialog">
      <span @click="proxy.$refs.goUserDialog.close()" class="close iconfont icon-cha"></span>
      <p class="title">我要找对象，开启交友模式</p>
      <p class="tips">你当前为推广红娘身份，开启后，将以单身的身份与其他用户互动，同时你推广红娘身份也是存在的，不影响你正常的推广操作。</p>
      <span class="event flex_dc">
        <span @click="btnOpenUser">开启</span>
      </span>
    </div>
  </oe_popup>

  <oe_hint ref="hint_dialog" />

  <!--加入团队流程-->
  <Team ref="Teampage" @success="joinTeamSuccess" @fail="joinTeamFail" />

  <oe_popup ref="teamfail_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="teamfail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">加入团队失败</p>
      <p class="tips">您已加入过团队，不能再加入，如需变更团队，请先退出当前团队。</p>
      <div @click="proxy.$refs.teamfail_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <oe_popup ref="teamsuccess_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="teamsuccess_dialog">
      <div class="tips_img">
        <img src="@/assets/images/chat_tips.png" alt="" />
      </div>
      <p class="tips">恭喜你，成功加入团队 【{{ leader_name_edit }}】</p>
      <div @click="proxy.$refs.teamsuccess_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>
  <notRole ref="notRoleBox" />

  <oe_popup ref="needRz_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，您还未进行实名认证，无法进行提现操作</p>
      <div @click="goIdrz" class="event">去认证</div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="needMobile_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，您还未进行手机号认证，无法进行提现操作</p>
      <div
        @click="
          () => {
            proxy.$refs.needMobile_dialog.close()
            proxy.$refs.noteMobileRz.open()
          }
        "
        class="event"
      >
        去认证
      </div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="needWeixin_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，你还未预留微信相关提现账户信息，完善后方可进行提现操作</p>
      <div
        @click="
          () => {
            proxy.$refs.needWeixin_dialog.close()
            router.push('/account')
          }
        "
        class="event"
      >
        去完善
      </div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="needAlipay_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，你还未预留支付宝相关提现账户信息，完善后方可进行提现操作</p>
      <div
        @click="
          () => {
            proxy.$refs.needAlipay_dialog.close()
            router.push('/account')
          }
        "
        class="event"
      >
        去完善
      </div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="needBank_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，你还未预留银行卡相关提现账户信息，完善后方可进行提现操作</p>
      <div
        @click="
          () => {
            proxy.$refs.needBank_dialog.close()
            router.push('/account')
          }
        "
        class="event"
      >
        去完善
      </div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="noteMobileRz" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="noteCodeRz">
      <p class="title">手机号认证</p>
      <p class="tips">完成手机认证，确保您的账户安全</p>
      <div class="mobile flex flex_ac">
        <span>手机号：</span>
        <input style="width: 2.4rem" type="number" maxlength="11" v-model="mobile" placeholder="输入手机号" />
      </div>
      <div class="code flex flex_ac flex_jsb">
        <div class="flex flex_ac">
          <span class="flex_s" style="color: #666">验证码：</span>
          <input style="width: 2.4rem" type="number" maxlength="6" v-model="mobilecode" placeholder="短信验证码" />
        </div>
        <span class="getcode flex_s" @click="getCode" v-if="codeStatus">获取验证码</span>
        <span class="time flex_s" v-else>({{ codeSecond }}s后重新获取)</span>
      </div>
      <div @click="sendMobilerz" class="event">确定</div>
      <span @click="proxy.$refs.noteMobileRz.close()" class="close iconfont icon-guanbi"></span>
    </div>
  </oe_popup>
  <ver-fication ref="oeVerFicat" @callback="smsCallback"></ver-fication>
</template>

<script>
export default {
  name: 'Cp',
}
</script>
<script setup>
import { ref, getCurrentInstance, nextTick, watch, reactive, computed, onActivated } from 'vue'
import tabbars from '@/components/tabbars.vue'
import oe_hint from '@/components/hint.vue'
import oe_popup from '@/oeui/popup.vue'
import Team from '@/views/index/team.vue'
import notRole from '@/views/index/components/notRole.vue'
import { quitTeam } from '@/api/team.js'
import { getCpTj, checkUser, openUser, getMyLeader, getMyPower } from '@/api/cp.js'
import { initWithdraw, getWithdrawQrcode, submitMobilerz } from '@/api/withdraw.js'
import { editLeaderName } from '@/api/edit.js'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { codeSend } from '@/api/login.js'
import verFication from '@/components/verification.vue'
const store = useStore()
const router = useRouter()

const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const unionInfo = computed(() => store.state.unionInfo)
const config = computed(() => store.state.config)

const getPgroupUrl = (type, val) => {
  return require(`@/assets/images/grade/${type}${val}.png`)
}

const power = reactive({
  user1_regincome: 0,
  user2_regincome: 0,
  party_nums: 0,
  partybm_nums: 0,
  activity_nums: 0,
  activitybm_nums: 0,
  leadunion_nums: 0,
  leaduser_nums: 0,
  audituser_nums: 0,
  after_nums: 0,
  child_nums: 0,
  user_nums: 0,
  aftermatch_nums: 0,
  new_metor_order: 0,
  new_shop_order: 0,
})
const getPower = () => {
  getCpTj().then(res => {
    if (res.ret == 1) {
      power.user1_regincome = res.result.data.user1_regincome
      power.user2_regincome = res.result.data.user2_regincome
      power.party_nums = res.result.data.party_nums
      power.partybm_nums = res.result.data.partybm_nums
      power.activity_nums = res.result.data.activity_nums
      power.activitybm_nums = res.result.data.activitybm_nums
      power.leadunion_nums = res.result.data.leadunion_nums
      power.leaduser_nums = res.result.data.leaduser_nums
      power.audituser_nums = res.result.data.audituser_nums
      power.after_nums = res.result.data.after_nums
      power.user_nums = res.result.data.user_nums
      power.child_nums = res.result.data.child_nums
      power.aftermatch_nums = res.result.data.aftermatch_nums
      power.new_metor_order = res.result.data.new_metor_order
      power.new_shop_order = res.result.data.new_shop_order
    }
  })
}

const level = reactive({
  user_money1: 0,
  user_money2: 0,
})
const getLevel = () => {
  getMyPower({
    type: 1,
  }).then(res => {
    if (res.ret == 1) {
      level.user_money1 = res.result.data.user_money1
      level.user_money2 = res.result.data.user_money2
    }
  })
}
getLevel()

const openLeader = () => {
  getLeader()
}

const leader = ref({})
let leaderName = ref('')
const getLeader = () => {
  getMyLeader().then(res => {
    if (res.ret == 1) {
      leaderName.value = res.result.data.leader_name
      leader.value = res.result.data
      nextTick(() => {
        proxy.$refs.leader_dialog.open()
      })
    } else if (res.ret == 3) {
      store.dispatch('getUserInfo')
    } else {
      OEUI.toast(res.msg || '系统繁忙,请稍后再试!')
    }
  })
}
const copy = data => {
  let elInput = document.createElement('input')
  elInput.value = data
  document.body.appendChild(elInput)
  elInput.select()
  document.execCommand('Copy')
  OEUI.toast({
    text: '复制成功',
  })
  elInput.remove()
}
const callMobile = data => {
  if (!data) return
  const phoneNumber = data
  const link = document.createElement('a')
  link.href = `tel:${phoneNumber}`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const is_quit = ref(true)
const fail_msg = ref('')
const btnQuitTeam = () => {
  if (!is_quit.value) return
  is_quit.value = false

  OEUI.loading.show()
  quitTeam()
    .then(res => {
      OEUI.loading.hide()
      proxy.$refs.leader_dialog.close()
      store.dispatch('getUserInfo')
      if (res.ret == 1) {
        proxy.$refs.successteam_dialog.open()
      } else if (res.ret == 2) {
        proxy.$refs.auditteam_dialog.open()
      } else if (res.ret == 3) {
        proxy.$refs.failteam_dialog.open()
        fail_msg.value = res.msg
      } else {
        OEUI.toast(res.msg || '系统繁忙,请稍后再试!')
      }
      is_quit.value = true
    })
    .catch(() => {
      OEUI.loading.hide()
      is_quit.value = true
      OEUI.toast('系统繁忙,请稍后再试!')
    })
}

//去提现
const idrz_type = ref('')

const goWithdraw = () => {
  initWithdraw().then(res => {
    if (res.ret == 1) {
      if (res.result.need_rz == 1) {
        // 未实名认证
        idrz_type.value = res.result.idrz_type
        proxy.$refs.needRz_dialog.open()
      } else if (res.result.need_wx == 1) {
        // 未关注公众号
        getWithdrawQrcode().then(res => {
          if (res.ret == 1) {
            proxy.$refs.hint_dialog.open({
              title: '提示',
              tips: '尊敬的用户，为方便后续提现操作，请先关注公众号',
              tap: '长按识别二维码关注公众号',
              qrcode: res.result.data,
            })
          } else {
            OEUI.toast({
              text: res.msg || '系统繁忙，请稍后再试',
            })
          }
        })
      } else if (res.result.need_mobilerz == 1) {
        // 需手机认证
        mobile.value = unionInfo.value.mobile
        proxy.$refs.needMobile_dialog.open()
      } else if (res.result.need_wxinfo == 1) {
        // 需完善微信账号
        proxy.$refs.needWeixin_dialog.open()
      } else if (res.result.need_alipay == 1) {
        // 需完善支付宝账号
        proxy.$refs.needAlipay_dialog.open()
      } else if (res.result.need_bank == 1) {
        // 完善银行卡账号
        proxy.$refs.needBank_dialog.open()
      } else {
        router.push('/withdraw')
      }
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

//去实名
const goIdrz = () => {
  proxy.$refs.needRz_dialog.close()
  if (idrz_type.value == 1) {
    // 三要素
    router.push('/idrz')
  } else {
    // 人工核验
    router.push('/idrz2')
  }
}

const mobilecode = ref(null)
const codeSecond = ref(60)
const codeStatus = ref(true)

const mobile = ref('')

let time = null
const getCode = () => {
  //判断是否开启了防刷机制
  if (mobile.value == '') {
    OEUI.toast({
      text: '请填写手机号',
    })
    return
  }
  proxy.$refs.oeVerFicat.start()
}

const sendCode = code => {
  //发送验证码
  OEUI.loading.show()

  codeSend({
    type: 'checkcode',
    mobile: mobile.value,
    brushcode: code || '',
  }).then(res => {
    OEUI.loading.hide()
    if (res.ret == 1) {
      OEUI.toast({ text: '发送验证码成功' })
      codeStatus.value = false
      if (code) {
        proxy.$refs.oeVerFicat.success()
      }
      if (time != null) return
      time = setInterval(() => {
        codeSecond.value--
        if (codeSecond.value == 0) {
          clearInterval(time)
          time = null
          codeStatus.value = true
          codeSecond.value = 60
        }
      }, 1000)
    } else {
      if (code) {
        proxy.$refs.oeVerFicat.error()
      }
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const smsCallback = code => {
  //防刷回调
  sendCode(code)
}

const sendMobilerz = () => {
  if (!mobilecode.value)
    return OEUI.toast({
      text: '请输入短信验证码',
    })
  codeSecond.value = 60
  codeStatus.value = true
  OEUI.loading.show()
  submitMobilerz({
    mobile: mobile.value,
    mobilecode: mobilecode.value,
  })
    .then(res => {
      mobilecode.value = null
      OEUI.loading.hide()
      OEUI.toast({ text: '手机号认证成功' })
      store.commit('setUnionInfo', res.result.info)
      proxy.$refs.noteMobileRz.close()
    })
    .catch(() => {
      OEUI.loading.hide()
      OEUI.toast('系统繁忙，请稍后再试')
    })
}

const goMmotion = () => {
  window.location.href = config.value.siteurl + 'index.php?m=union&c=mentorcp'
}
const goShop = () => {
  window.location.href = config.value.siteurl + 'index.php?m=union&c=shopcp'
}

const goAccount = () => {
  initWithdraw().then(res => {
    if (res.ret == 1) {
      if (res.result.need_rz == 1) {
        proxy.$refs.needRz_dialog.open()
      } else {
        router.push('/account')
      }
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const leader_name = ref('')
const is_send = ref(true)
const openLeadername = () => {
  leader_name.value = unionInfo.value.leader_name
  proxy.$refs.leadername_dialog.open()
}
const resetLeadername = () => {
  leader_name.value = ''
}
const sendLeadrname = () => {
  if (!leader_name.value) return OEUI.toast({ text: '团队名称不能为空!' })
  if (!is_send.value) return
  is_send.value = false
  editLeaderName({
    leader_name: leader_name.value,
  })
    .then(res => {
      if (res.ret == 1) {
        OEUI.toast({ text: '操作成功' })
        store.dispatch('getUserInfo')
        proxy.$refs.leadername_dialog.close()
      } else {
        OEUI.toast({ text: res.msg || '操作失败,请检查!' })
      }
      setTimeout(() => {
        is_send.value = true
      })
    })
    .catch(() => {
      is_send.value = true
      OEUI.toast({ text: '系统繁忙,请稍后再试!' })
    })
}

//加入团队
const joinTeam = () => {
  proxy.$refs.Teampage.open()
}

//加入团队失败
const joinTeamFail = () => {
  proxy.$refs.Teampage.close(() => {
    proxy.$refs.teamfail_dialog.open()
  })
}
//加入团队成功
const leader_name_edit = ref('')
const joinTeamSuccess = val => {
  leader_name_edit.value = val
  proxy.$refs.teamsuccess_dialog.open()
}

//检测是否已绑定交友帐号
const is_check = ref(true)
const btnCheckUser = () => {
  if (!is_check.value) return
  is_check.value = false
  checkUser()
    .then(res => {
      if (res.ret == 1) {
        window.location.href = config.value.siteurl + 'index.php?m=wap&c=cp'
      } else if (res.ret == 2) {
        proxy.$refs.goUserDialog.open()
      } else {
        OEUI.toast(res.msg || '系统繁忙,请稍后再试!')
      }
      setTimeout(() => {
        is_check.value = true
      }, 500)
    })
    .catch(() => {
      is_check.value = true
      OEUI.toast('系统繁忙,请稍后再试!')
    })
}
const is_open = ref(true)
const btnOpenUser = () => {
  if (!is_open.value) return
  is_open.value = false
  openUser()
    .then(res => {
      if (res.ret > 0) proxy.$refs.goUserDialog.close()
      if (res.ret == 1) {
        window.location.href = config.value.siteurl + 'index.php?m=wap&c=cp'
      } else if (res.ret == 2) {
        window.location.href = config.value.siteurl + 'vue/wap/#/reg'
      } else if (res.ret == 3) {
        window.location.href = config.value.siteurl + 'vue/wap/#/reg'
      } else {
        OEUI.toast(res.msg || '系统繁忙,请稍后再试!')
      }
      setTimeout(() => {
        is_open.value = true
      }, 500)
    })
    .catch(() => {
      is_check.value = true
      OEUI.toast('系统繁忙,请稍后再试!')
    })
}
watch(
  () => unionInfo.value,
  newInfo => {
    if (newInfo) {
      unionInfo.value = newInfo
      leader_name.value = newInfo.leader_name

      if (newInfo.enter_flag == 2) {
        router.replace({
          path: '/unitApplyResult',
        })
      }

      if (newInfo.level1_flag != 1) {
        nextTick(() => {
          proxy.$refs.notRoleBox.open(newInfo.level1_flag)
        })
      }
    }
  },
  { immediate: true, deep: true },
)
watch(
  () => config.value,
  newInfo => {
    if (newInfo) {
      config.value = newInfo
    }
  },
  { immediate: true, deep: true },
)

onActivated(() => {
  getPower()
})
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}

.font_din {
  font-family: 'cpDin';
}

.main {
  padding: 0.4267rem;
  box-sizing: border-box;

  .enterprise-box {
    height: 0.96rem;
    border-radius: 0.5867rem;
    border: 0.0267rem solid #ffffff;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.24);
    margin: 0.2133rem 0 0.5333rem 0;
    padding: 0 0.5333rem;

    span {
      color: #7d68fe;
      font-size: 0.3733rem;
      margin-left: 0.16rem;
      font-weight: 400;
    }
  }

  .data {
    margin-top: 0.2133rem;

    .head {
      width: 1.76rem;
      height: 1.76rem;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 0.32rem;

      img {
        border-radius: 50%;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .info {
      width: 100%;

      .username {
        font-size: 0.4267rem;
        line-height: 0.5867rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        color: #31293b;
        max-width: 40vw;
      }

      .id {
        font-size: 0.32rem;
        line-height: 0.4533rem;
        font-weight: normal;
        font-family: PingFang SC, PingFang SC;
        color: #666666;
      }

      .edit {
        font-size: 0.32rem;
        line-height: 0.4533rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #666666;
      }

      .grade {
        padding: 0.1067rem 0;
        padding-left: 0.1333rem;
        display: flex;
        align-items: center;
        overflow-x: auto;
        overflow-y: hidden;

        &::-webkit-scrollbar {
          display: none;
        }
      }
    }
  }

  .data_skelecton {
    margin-top: 0.2133rem;

    .head {
      width: 1.76rem;
      height: 1.76rem;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 0.32rem;
      background: #f7f7f7;
    }

    p {
      height: 0.5333rem;
      background: #f7f7f7;
      width: 75%;
      border-radius: 0.1333rem;
    }

    span {
      height: 0.4533rem;
      background: #f7f7f7;
      width: 2rem;
      margin-top: 0.2133rem;
      border-radius: 0.08rem;
    }

    em {
      margin-top: 0.2133rem;
      height: 0.32rem;
      background: #f7f7f7;
      width: 50px;
      margin-right: 0.4rem;
      border-radius: 0.08rem;
    }
  }

  .more_role {
    margin-top: 0.64rem;
    height: 2.0267rem;
    background: url('~@/assets/images/more_role.png') no-repeat;
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.4267rem;
    font-family: PingFang SC, PingFang SC;

    .title {
      display: flex;
      align-items: center;

      span {
        width: 0.64rem;
        height: 0.64rem;
        border-radius: 50%;
        margin-right: 0.1067rem;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      p {
        font-size: 0.4267rem;
        line-height: 0.5867rem;
        font-weight: 500;
        color: $color_main;
      }
    }

    .tip {
      margin-top: 0.1333rem;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      font-weight: normal;
      color: #999;
    }

    .btn {
      background: #fff;
      font-size: 0.4267rem;
      line-height: 0.5867rem;
      font-weight: normal;
      color: $color_main;
      padding: 0.1333rem 0.3733rem;
      border-radius: 0.4267rem;
      cursor: pointer;
    }
  }

  .more_role_skelecton {
    margin-top: 0.64rem;
    height: 2.0267rem;
    background: #f7f7f7;
    border-radius: 0.32rem;
  }

  .tit {
    margin-top: 0.5333rem;
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #31293b;
    line-height: 0.5867rem;
    padding-left: 0.1067rem;
  }

  .menu {
    margin-top: 0.32rem;
    padding: 0.2133rem 0.4267rem;
    background: #fff;
    border-radius: 0.32rem;

    .item {
      height: 1.28rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .name {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #31293b;
        flex-shrink: 0;
      }

      .event {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.32rem;
        line-height: 0.4533rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #999999;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;

        span {
          max-width: 4rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        i {
          font-size: 0.32rem;
          position: relative;
          //top: 0.0267rem;
          margin-left: 0.1067rem;
          color: #666666;
        }
      }

      &.skelecton {
        .name {
          background: #f1f1f1;
          border-radius: 0.1333rem;
        }

        .event {
          background: #f1f1f1;
          border-radius: 0.1333rem;
        }
      }
    }
  }

  .more {
    margin-top: 0.32rem;
    padding: 0.32rem 0.4267rem;
    background: #fff;
    border-radius: 0.32rem;
    cursor: pointer;

    .tite {
      height: 1.1733rem;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .name {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #31293b;
      }

      .event {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.32rem;
        line-height: 0.4533rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #999999;

        i {
          font-size: 0.32rem;
          position: relative;
          top: 0.0267rem;
          margin-left: 0.1067rem;
          color: #666666;
        }
      }
    }

    .tips {
      margin-top: 0.1067rem;
      margin-bottom: 0.16rem;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      color: #666666;
    }
  }

  .kefu {
    margin-top: 0.32rem;
    height: 1.1733rem;
    background: #fff;
    border-radius: 0.32rem;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #3d3d3d;
  }
}

.leader_dialog {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.64rem;

  .title {
    text-align: center;
    font-size: 0.4267rem;
    font-weight: 500;
    color: #31293b;
    line-height: 0.5867rem;
  }

  .data {
    margin-top: 0.4267rem;

    .tit {
      display: flex;
      align-items: center;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      color: #31293b;
      font-weight: 600;

      i {
        position: relative;
        top: 0.0267rem;
        background: $color_main;
        width: 0.08rem;
        height: 0.2667rem;
        margin-right: 0.1333rem;
      }
    }

    .num {
      margin-top: 0.4267rem;
      position: relative;

      &::after {
        position: absolute;
        content: '';
        width: 0.0267rem;
        height: 0.6667rem;
        background: #f2f4f5;
      }

      .item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        font-size: 0.32rem;
        line-height: 0.5867rem;
        font-weight: normal;
        color: #999;

        .earnings {
          @extend .font_din;
          color: $color_main;
          font-weight: 700;
          font-size: 0.5333rem;
          line-height: 0.5867rem;
        }
      }
    }
  }

  .info {
    margin-top: 0.64rem;
    position: relative;

    .tit {
      display: flex;
      align-items: center;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      color: #31293b;
      font-weight: 600;

      i {
        background: $color_main;
        width: 0.08rem;
        height: 0.2667rem;
        margin-right: 0.1333rem;
      }
    }

    .user {
      margin-top: 0.4267rem;
      display: flex;
      align-items: center;

      .head {
        width: 1.3867rem;
        height: 1.3867rem;
        border-radius: 0.32rem;
        flex-shrink: 0;
        margin-right: 0.32rem;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .name {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        color: #31293b;
        font-weight: normal;

        .id {
          font-size: 0.32rem;
          line-height: 0.4533rem;
          font-weight: normal;
          color: #999;
        }

        .group {
          margin-left: 0.2667rem;
        }
      }
    }
  }

  .contact {
    margin-top: 0.32rem;
    font-size: 0.3733rem;
    line-height: 0.5867rem;
    font-weight: normal;
    color: #31293b;

    > div {
      margin-bottom: 0.2133rem;
    }

    .iconfont {
      color: $color_main;
      margin-right: 0.1333rem;
    }

    .btn {
      color: #fff;
      font-size: 0.3467rem;
      line-height: 0.5867rem;
      background: $color_main;
      padding: 0.0267rem 0.3467rem;
      border-radius: 0.2133rem;
      cursor: pointer;
    }
  }

  .event {
    margin: 0 0.6133rem;
    margin-top: 0.64rem;
    cursor: pointer;
    text-align: center;
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-weight: normal;
    border-radius: 0.5333rem;
    padding: 0.24rem 0;
  }

  .out {
    cursor: pointer;
    text-align: center;
    font-size: 0.3467rem;
    color: #31293b;
    font-weight: normal;
    line-height: 0.5867rem;
    margin-top: 0.32rem;
  }
}

.intro_dialog {
  padding: 0.64rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;

  .title {
    font-size: 0.4267rem;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    text-align: center;
  }

  .tip {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.64rem;
  }

  .tit {
    font-size: 0.3733rem;
    font-weight: normal;
    color: #31293b;
    line-height: 0.5867rem;
    margin-top: 0.32rem;
  }

  .input {
    margin-top: 0.64rem;
    background: #f2f4f5;
    padding: 0.32rem;
    border-radius: 0.64rem;
    position: relative;

    .clear {
      position: absolute;
      right: 0.32rem;
      color: #999;
    }
  }

  .close {
    position: absolute;
    font-size: 0.5333rem;
    right: 0.4rem;
    top: 0.2667rem;
    cursor: pointer;
    z-index: 100;
  }

  .event {
    cursor: pointer;
    margin: 0 0.6133rem;
    margin-top: 0.5333rem;
    font-size: 0.4267rem;
    font-weight: normal;
    color: #ffffff;
    line-height: 0.5867rem;
    background: $color_main;
    text-align: center;
    padding: 0.24rem 0;
    border-radius: 0.5333rem;
  }
}

.goUserDialog {
  font-family: PingFang SC, PingFang SC;
  padding: 0.5867rem 0.4267rem;
  position: relative;

  .close {
    position: absolute;
    right: 0.5333rem;
    top: 0.5333rem;
    font-size: 0.5867rem;
    font-weight: normal;
  }

  .title {
    font-size: 0.48rem;
    font-weight: 500;
    color: #31293b;
    line-height: 0.6667rem;
  }

  .tips {
    margin-top: 0.3467rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #31293b;
    line-height: 0.64rem;
  }

  .event {
    margin-top: 0.64rem;

    span {
      background: $color_main;
      color: #fff;
      font-size: 0.4267rem;
      font-weight: normal;
      color: #ffffff;
      line-height: 0.5867rem;
      padding: 0.24rem 2.7733rem;
      border-radius: 0.5333rem;
      cursor: pointer;
    }
  }
}

.teamfail_dialog {
  border-radius: 0.64rem 0.64rem 0 0;
  padding: 0.64rem 0.48rem;
  text-align: center;

  .tips_img {
    position: absolute;
    left: 50%;
    top: -2rem;
    transform: translateX(-50%);
    width: 3.2rem;
    height: 2.88rem;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .title {
    margin-top: 0.6933rem;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
  }

  .tips {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    color: #666666;
  }

  .event {
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin: 0 0.7733rem;
    margin-top: 0.64rem;
    padding: 0.24rem 0;
  }
}

.teamsuccess_dialog {
  text-align: center;
  padding: 0.64rem 1.2533rem;
  background: url('../../assets/images/dailog_bg.png');
  background-size: cover;
  border-radius: 0.64rem;
  overflow: hidden;

  .tips_img {
    position: absolute;
    left: 50%;
    top: -1.7333rem;
    transform: translateX(-50%);
    width: 4.2667rem;
    height: 2.6133rem;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .tips {
    margin-top: 0.64rem;
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
  }

  .event {
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin-top: 0.64rem;
    padding: 0.24rem 0;
  }
}

.fail_dialog {
  position: relative;

  .close {
    top: 0.2667rem;
    right: 0.4rem;
    font-size: 0.48rem;
  }
}

.noteCodeRz {
  border-radius: 0.64rem 0.64rem 0 0;
  padding: 0.64rem 0.4267rem;

  .title {
    text-align: center;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
  }

  .iconfont {
    margin: 0 auto;
  }

  .tips {
    text-align: center;
    margin-top: 0.5333rem;
    font-size: 0.3733rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    color: #666666;
  }

  .price {
    text-align: center;
    margin-top: 0.1067rem;
    font-size: 0.64rem;
    line-height: 0.9067rem;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #31293b;
  }

  .mobile {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    color: #666666;
    padding: 0.32rem 0;
  }

  .code {
    padding: 0.32rem 0;
    margin-bottom: 0.5333rem;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;

    .getcode {
      color: #0570f1;
    }

    .time {
      color: #999;
    }
  }

  .event {
    text-align: center;
    background: $color_main;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin: 0 0.7733rem;
    padding: 0.24rem 0;
  }

  .close {
    position: absolute;
    font-size: 0.7467rem;
    color: #fff;
    left: 50%;
    transform: translateX(-50%);
    bottom: -1.3333rem;
  }
}
</style>

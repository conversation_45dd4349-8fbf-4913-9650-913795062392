<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" v-if="is_back" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
    </div>
    <div class="content">
      <div class="head">
        <img src="@/assets/images/idrz_bg.png" alt="" />
      </div>
      <div class="data">
        <p class="tip">对以下信息进行实名认证，请确保信息真实有效</p>
        <div class="user">
          <div class="item">
            <span>真实姓名</span>
            <input type="text" maxlength="20" v-model="state.truename" placeholder="填写真实姓名" />
          </div>
          <div class="item">
            <span>身份证号</span>
            <input type="text" maxlength="18" v-model="state.idnumber" placeholder="填写身份证号" />
          </div>
        </div>
        <p class="tips">手机实名必须与上面填写的身份信息一致</p>
        <div class="user">
          <div class="item">
            <span>手机号</span>
            <input type="text" maxlength="11" v-model="state.mobile" placeholder="填写手机号" />
          </div>
          <div class="item pr">
            <span>验证码</span>
            <input type="text" maxlength="6" v-model="state.mobilecode" placeholder="填写手机验证码" />
            <span class="code" @click="getCode" v-if="codeStatus">获取验证码</span>
            <span class="code count" v-else>({{ codeSecond }}s后重新获取)</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="btn flex_dc">
    <span @click="btn_send" class="current">{{ Number(fee) > 0 ? '¥' + fee : '' }} 提交认证</span>
  </div>

  <oe_popup ref="inter_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">温馨提示</p>
      <div class="tip">本认证由GA信息网及运营商提供数据源，<font color="#7D68FE">手机号已实名信息必须与填写的身份证信息一致，否则无法通过认证，请仔细填写。</font></div>
      <div class="tips">
        注：<br />
        实名认证费用由第三方认证平台收取，信息不符将会认证失败，一旦提交，费用无法退还。
      </div>
      <div @click="proxy.$refs.inter_dialog.close()" class="event">我已了解</div>
    </div>
  </oe_popup>

  <oe_popup ref="waitorder_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog pr">
      <p class="title">温馨提示</p>
      <div class="tip">你有一条未完成的实名认证订单，点击“继续完成”按钮，进行实名认证。</div>
      <div @click="waitorderEvent" class="event">继续完成</div>
      <i @click="$router.back()" class="pa close iconfont icon-guanbi2"></i>
    </div>
  </oe_popup>

  <ver-fication ref="oeVerFicat" @callback="smsCallback"></ver-fication>
</template>

<script setup>
  import { ref, getCurrentInstance, computed, onMounted, nextTick, reactive } from 'vue'
  import oe_popup from '@/oeui/popup.vue'
  import verFication from '@/components/verification.vue'
  import { initIdrz, sendIdrz } from '@/api/idrz.js'
  import { codeSend } from '@/api/login.js'
  import { getWeinxinPay, wechatPay } from '@/utils/jssdk.js'
  import { useStore } from 'vuex'
  import { useRouter, useRoute } from 'vue-router'
  const { proxy } = getCurrentInstance()
  const OEUI = proxy.OEUI
  const store = useStore()
  const router = useRouter()
  const route = useRoute()
  const unionInfo = computed(() => store.state.unionInfo)
  const config = computed(() => store.state.config)
  const fee = ref(0)
  const waitorder = ref({})

  const is_back = ref(true)
  if (window.history.length <= 1) {
    is_back.value = false
  } else {
    is_back.value = true
  }

  const state = reactive({
    truename: '',
    idnumber: '',
    mobile: '',
    mobilecode: ''
  })

  const init = () => {
    initIdrz().then(res => {
      if (res.ret == 1) {
        fee.value = res.result.fee
        waitorder.value = res.result.waitorder
        nextTick(() => {
          flowEvent()
        })
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试'
        })
      }
    })
  }

  const flowEvent = () => {
    if (waitorder.value?.rzno) {
      proxy.$refs.waitorder_dialog.open()
    } else {
      proxy.$refs.inter_dialog.open()
    }
  }

  const codeSecond = ref(60)
  const codeStatus = ref(true)

  const smsCallback = code => {
    //防刷回调
    sendCode(code)
  }
  const getCode = () => {
    if (state.mobile == '') {
      OEUI.toast({
        text: '请填写手机号'
      })
      return
    }
    proxy.$refs.oeVerFicat.start()
  }
  let time = null
  const sendCode = code => {
    //发送验证码
    OEUI.loading.show()
    codeSend({
      type: 'idrz',
      mobile: state.mobile,
      brushcode: code || ''
    }).then(res => {
      OEUI.loading.hide()
      if (res.ret == 1) {
        OEUI.toast({ text: '发送验证码成功' })
        codeStatus.value = false
        if (code) {
          proxy.$refs.oeVerFicat.success()
        }
        if (time != null) return
        time = setInterval(() => {
          codeSecond.value--
          if (codeSecond.value == 0) {
            clearInterval(time)
            time = null
            codeStatus.value = true
            codeSecond.value = 60
          }
        }, 1000)
      } else {
        if (code) {
          proxy.$refs.oeVerFicat.error()
        }
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试'
        })
      }
    })
  }

  const is_send = ref(true)
  const btn_send = () => {
    if (!state.truename) {
      return OEUI.toast({
        text: '请填写真实姓名'
      })
    }
    if (!state.idnumber) {
      return OEUI.toast({
        text: '请填写身份证号'
      })
    }
    if (!state.mobile) {
      return OEUI.toast({
        text: '请填写手机号'
      })
    }
    if (!state.mobilecode) {
      return OEUI.toast({
        text: '请填写手机验证码'
      })
    }

    if (!is_send.value) return
    is_send.value = false
    OEUI.loading.show()
    sendIdrz(state)
      .then(res => {
        OEUI.loading.hide()
        if (res.ret == 1) {
          OEUI.toast({
            text: '资料提交成功!'
          })
          setTimeout(() => {
            router.replace('/idrz/result?rzno=' + res.result.rzno)
          }, 500)
        } else if (res.ret == 2) {
          let url = config.value.siteurl + 'index.php?m=wap&c=pay&paynum=' + res.result.paynum + '&forword=' + encodeURIComponent(config.value.siteurl + 'vue/union/#/idrz/result?rzno=' + res.result.rzno)
          if (url) {
            window.location.replace(url)
          }
        } else if (res.ret == 22) {
          getWeinxinPay(res.result.paynum)
            .then(data => {
              if (data.ret == 1) {
                weixinPay(data.result.data, res.result.rzno)
              } else {
                OEUI.toast({
                  text: data.msg || '下单失败，请联系客服!'
                })
              }
            })
            .catch(() => {
              OEUI.toast({
                text: '获取支付参数失败，请稍后再试'
              })
            })
        } else {
          OEUI.toast({
            text: res.msg || '系统繁忙，请稍后再试'
          })
        }
        setTimeout(() => {
          is_send.value = true
        }, 600)
      })
      .catch(() => {
        OEUI.loading.hide()
        is_send.value = true
        OEUI.toast('系统繁忙，请稍后再试')
      })
  }

  const weixinPay = (data, rzno) => {
    try {
      wechatPay(data, status => {
        if (status == 'success') {
          OEUI.toast({
            text: '资料提交成功!'
          })
          setTimeout(() => {
            router.replace('/idrz/result?rzno=' + rzno)
          }, 500)
        } else if (status == 'cancel') {
          OEUI.toast({
            text: '取消支付成功!'
          })
        } else if (status == 'error') {
          OEUI.toast({
            text: '获取支付参数失败，请稍后再试!'
          })
        }
      })
    } catch (error) {
      OEUI.toast({
        text: '获取支付参数失败，请稍后再试!'
      })
    }
  }

  const waitorderEvent = () => {
    proxy.$refs.waitorder_dialog.close()
    proxy.$refs.inter_dialog.close()
    router.replace('/idrz/result?rzno=' + waitorder.value.rzno)
  }

  onMounted(() => {
    init()
  })
</script>

<style lang="scss" scoped>
  .main {
    background: #fff;
    width: 100vw;
    height: 100vh;
    padding-bottom: 2.1333rem;
    .top_nav {
      background: transparent;
    }
    .content {
      font-family: PingFang SC, PingFang SC;
      .head {
        height: 6.32rem;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .data {
        position: relative;
        top: -0.8rem;
        background: #fff;
        padding: 0.4267rem;
        border-radius: 0.4267rem 0.4267rem 0 0;
        .tip {
          font-size: 0.3733rem;
          font-weight: normal;
          color: #31293b;
          line-height: 0.5333rem;
        }
        .user {
          margin-top: 0.5333rem;
          .item {
            display: flex;
            align-items: center;
            background: #f2f4f5;
            border-radius: 0.8533rem;
            margin-bottom: 0.32rem;
            padding: 0.32rem 0.5333rem;
            span {
              font-size: 0.3733rem;
              font-weight: normal;
              color: #666666;
              line-height: 0.5333rem;
              margin-right: 0.5333rem;
              flex-shrink: 0;
            }
            input {
              flex: 1;
              position: relative;
              top: 0.04rem;
              &::placeholder {
                font-size: 0.3733rem;
                font-weight: normal;
                color: #c5c3c7;
              }
            }
            .code {
              position: absolute;
              right: 0;
              font-size: 0.3733rem;
              font-weight: normal;
              color: #0570f1;
              line-height: 0.5333rem;
              &.count {
                color: #999;
              }
            }
          }
        }
        .tips {
          margin-top: 0.4267rem;
          font-size: 0.3733rem;
          font-weight: normal;
          color: #ed1616;
          line-height: 0.5333rem;
        }
      }
    }
  }
  .btn {
    position: fixed;
    width: 100vw;
    height: 2.1333rem;
    bottom: 0;
    span {
      background: #ccc;
      font-size: 0.3733rem;
      font-weight: normal;
      line-height: 0.64rem;
      width: 90vw;
      text-align: center;
      padding: 0.2133rem 0;
      border-radius: 0.5867rem;
      cursor: pointer;
      &.current {
        background: $color_main;
        color: #fff;
      }
    }
  }
  .intro_dialog {
    .close {
      top: 0.2667rem;
      right: 0.4rem;
      font-size: 0.48rem;
    }
    .tip {
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5867rem;
    }
    .tips {
      margin-top: 0.32rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #ed1616;
      line-height: 0.5867rem;
    }
  }
</style>

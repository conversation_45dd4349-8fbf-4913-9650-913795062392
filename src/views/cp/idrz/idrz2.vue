<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <p class="fz16 fb">实名认证</p>
      <span @click="$router.back()" v-if="is_back" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
    </div>
    <div class="h44"></div>
    <div class="content">
      <div class="flex_dc card">
        <div @click="openHeadimgUp('paper1')" class="flex_1 flex_dc" style="margin-right: .2667rem;">
          <img v-if="card.paper1" :src="card.paper1" class="ob_c">
          <img v-else src="@/assets/images/idrz/idr.png" class="ob_c">
        </div>
        <div @click="openHeadimgUp('paper2')" class="flex_1 flex_dc">
          <img v-if="card.paper2" :src="card.paper2" class="ob_c">
          <img v-else src="@/assets/images/idrz/idrs.png" class="ob_c">
        </div>
      </div>
      <div class="flex_dc">
        <p class="flex_dc flex_1" style="margin-right: .2667rem;">上传身份证正面</p>
        <p class="flex_dc flex_1">上传身份证反面</p>
      </div>
      <div class="data">
        <p class="tip">信息确认</p>
        <div class="user">
          <div class="item">
            <span>真实姓名</span>
            <input type="text" maxlength="20" v-model="state.truename" placeholder="填写真实姓名" />
          </div>
          <div class="item">
            <span>身份证号</span>
            <input type="text" maxlength="18" v-model="state.idnumber" placeholder="填写身份证号" />
          </div>
        </div>

      </div>
    </div>
    <div style="background: #f2f4f5;height: .2667rem;"></div>
    <div class="intro">
      <p>为什么要实名认证？</p>
      <div>作为一个真实、严肃的婚恋平台，我们要求红娘必须完成
        身份证实名认证，保证红娘资料真实。
      </div>
      <p>关于隐私安全</p>
      <div>您填写的身份证信息和上传的身份证图片资料，仅供网站审核
        使用，且他人无法看到</div>
    </div>
  </div>
  <div class="btn flex_dc">
    <span @click="btn_send" class="current">提交认证</span>
  </div>



  <upload-headimg ref="uploadBox" @callback="uploadHead"></upload-headimg>
</template>

<script setup>
import { ref, getCurrentInstance, computed, onMounted, nextTick, reactive, watch } from 'vue'
import oe_popup from '@/oeui/popup.vue'
import { initIdrz2, sendIdrz2 } from '@/api/idrz.js'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import uploadHeadimg from '@/components/upload_headimg.vue'
import { uploadImage } from '@/api/edit.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const router = useRouter()
const unionInfo = ref({})
const config = computed(() => store.state.config)

const is_back = ref(true)
if (window.history.length <= 1) {
  is_back.value = false
} else {
  is_back.value = true
}

const state = reactive({
  paper1: '',
  paper2: '',
  truename: '',
  idnumber: ''
})

const card = reactive({
  paper1: '',
  paper2: '',
})

const init = () => {
  initIdrz2().then(res => {
    if (res.ret == 1) {
      let data = res.result.lastorder || {}
      if (data.logid) {
        for (const key in state) {
          state[key] = res.result.lastorder[key]
        }
        for (const key in card) {
          card[key] = res.result.lastorder[key + '_url']
        }
        if (data.status == 0) {
          OEUI.modal({
            text: '您的实名认证申请正在后台审核中，请耐心等待！',
            cancelShow: false,
            confirmText: '知道了',
            confirm: () => {
              router.back()
            }
          })
        }
      }
    } else if (res.ret == 2) {
      OEUI.modal({
        text: '您已通过实名认证，请勿重复实名！',
        cancelShow: false,
        confirmText: '知道了',
        confirm: () => {
          router.back()
        }
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试'
      })
    }
  })
}

const up_type = ref('paper1')
const openHeadimgUp = (val) => {
  up_type.value = val
  //打开图片上传
  proxy.$refs.uploadBox.open(true)

}
const uploadHead = obj => {
  if (!obj.src) return OEUI.toast({ text: '图片获取失败，请检查!' })
  try {
    uploadImage({
      base64img: obj.src,
      module: 'idrz',
    }).then(res => {
      if (res.ret == 1) {
        state[up_type.value] = res.result.drawimg
        card[up_type.value] = res.result.drawimg_url
      } else {
        OEUI.toast({ text: '图片上传失败，请检查!' })
      }
    })
  } catch (error) {
    OEUI.toast({ text: '系统繁忙，请检查!' })
  }
}


const is_send = ref(true)
const btn_send = () => {

  if (!state.paper1) {
    return OEUI.toast({
      text: '请上传身份证正面照片'
    })
  }
  if (!state.paper2) {
    return OEUI.toast({
      text: '请上传身份证反面照片'
    })
  }
  if (!state.truename) {
    return OEUI.toast({
      text: '请填写真实姓名'
    })
  }
  if (!state.idnumber) {
    return OEUI.toast({
      text: '请填写身份证号'
    })
  }


  if (!is_send.value) return
  is_send.value = false
  OEUI.loading.show()
  sendIdrz2(state)
    .then(res => {
      OEUI.loading.hide()
      if (res.ret == 1) {
        OEUI.modal({
          text: '提交成功，请等待管理员联系或留意审核结果通知!',
          cancelShow: false,
          confirmText: '知道了',
          confirm: () => {
            router.back()
          }
        })
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试'
        })
      }
      setTimeout(() => {
        is_send.value = true
      }, 600)
    })
    .catch(() => {
      OEUI.loading.hide()
      is_send.value = true
      OEUI.toast('系统繁忙，请稍后再试')
    })
}



onMounted(() => {
  init()
})

watch(() => store.state.unionInfo, (newInfo) => {
  if (newInfo) {
    state.truename = newInfo.truename
    state.idnumber = newInfo.idnumber
    unionInfo.value = newInfo
  }

}, { deep: true, immediate: true })


</script>

<style lang="scss" scoped>
.main {
  background: #fff;
  width: 100vw;
  height: 100vh;
  padding-bottom: 2.1333rem;
  box-sizing: border-box;

  .top_nav {
    background: #fff;
    box-shadow: 0 .0267rem 0 #f1f1f1;
  }

  .content {
    padding: .2667rem 0.4267rem;
    font-family: PingFang SC, PingFang SC;

    .card {
      &>div {
        background: #f1f1f1;
        height: 2.6667rem;
        border-radius: .32rem;
        overflow: hidden;
        margin-bottom: .1333rem;
      }
    }

    .data {
      margin-top: .5333rem;

      .tip {
        font-size: .48rem;
        font-weight: normal;
        color: #31293b;
        line-height: 0.5333rem;
        text-align: center;
        font-weight: bold;
      }

      .user {
        margin-top: 0.5333rem;

        .item {
          display: flex;
          align-items: center;
          background: #f2f4f5;
          border-radius: 0.8533rem;
          margin-bottom: 0.32rem;
          padding: 0.32rem 0.5333rem;

          span {
            font-size: 0.3733rem;
            font-weight: normal;
            color: #666666;
            line-height: 0.5333rem;
            margin-right: 0.5333rem;
            flex-shrink: 0;
          }

          input {
            flex: 1;
            position: relative;
            top: 0.04rem;

            &::placeholder {
              font-size: 0.3733rem;
              font-weight: normal;
              color: #c5c3c7;
            }
          }

          .code {
            position: absolute;
            right: 0;
            font-size: 0.3733rem;
            font-weight: normal;
            color: #0570f1;
            line-height: 0.5333rem;

            &.count {
              color: #999;
            }
          }
        }
      }

    }
  }

  .intro {
    padding: .2667rem 0.4267rem;

    p {
      color: #fd7559;
      font-size: .3733rem;
      font-weight: 600;
    }

    div {
      margin-top: .1333rem;
      color: #666;
      font-size: .3733rem;
      line-height: .5333rem;
      margin-bottom: .4rem;
    }
  }
}

.btn {
  position: fixed;
  width: 100vw;
  height: 2.1333rem;
  bottom: 0;

  span {
    background: #ccc;
    font-size: 0.3733rem;
    font-weight: normal;
    line-height: 0.64rem;
    width: 90vw;
    text-align: center;
    padding: 0.2133rem 0;
    border-radius: 0.5867rem;
    cursor: pointer;

    &.current {
      background: $color_main;
      color: #fff;
    }
  }
}

.intro_dialog {
  .close {
    top: 0.2667rem;
    right: 0.4rem;
    font-size: 0.48rem;
  }

  .title {
    font-weight: 600;
  }

  .tip {
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.5867rem;
    width: 80%;
    text-align: center;
    margin: 0 auto;
    margin-top: .6667rem;
  }

  .tips {
    margin-top: 0.32rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #ed1616;
    line-height: 0.5867rem;
  }
}
</style>

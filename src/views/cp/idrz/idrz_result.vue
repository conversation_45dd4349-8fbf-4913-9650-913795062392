<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">认证结果</div>
  </div>
  <div class="main">
    <div v-if="status == 'loading'" class="loading">
      <div class="rz_img">
        <img :class="animImg == 1 ? 'show' : 'hide'" src="@/assets/images/fate/f_1.jpg" />
        <img :class="animImg == 2 ? 'show' : 'hide'" src="@/assets/images/fate/f_2.jpg" />
        <img :class="animImg == 3 ? 'show' : 'hide'" src="@/assets/images/fate/f_3.jpg" />
        <img :class="animImg == 4 ? 'show' : 'hide'" src="@/assets/images/fate/f_4.jpg" />
      </div>
      <p class="tips">认证匹配中，请稍等...</p>
    </div>
    <div v-else-if="status == 'success'" class="success">
      <div class="rz_img">
        <img src="@/assets/images/fate/f_6.jpg" />
      </div>
      <p class="tips">恭喜你，实名认证成功！</p>
      <div class="btn" @click="$router.back()">返回上一步</div>
    </div>
    <div v-else class="fail">
      <div class="rz_img">
        <img src="@/assets/images/fate/f_5.jpg" />
      </div>
      <p class="tips">{{ fail_msg }}</p>
      <div class="btn" @click="$router.push('/idrz')">返回重新认证</div>
    </div>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, computed, onMounted } from 'vue'
  import oe_popup from '@/oeui/popup.vue'
  import { useStore } from 'vuex'
  import { useRoute } from 'vue-router'
  import { resultIdrz } from '@/api/idrz.js'

  const { proxy } = getCurrentInstance()
  const OEUI = proxy.OEUI
  const store = useStore()
  const route = useRoute()

  const status = ref('loading')
  const fail_msg = ref('实名认证匹配失败...')
  const animImg = ref(1)
  const timer = ref(null)
  const setTimeAnim = () => {
    let i = animImg.value
    timer.value = setInterval(() => {
      i = i++ > 3 ? 1 : i++
      animImg.value = i
    }, 600)
  }

  const removeTimeAnim = () => {
    clearInterval(timer.value)
    timer.value = null
  }

  const initResult = () => {
    resultIdrz({
      rzno: route.query.rzno
    })
      .then(res => {
        removeTimeAnim()
        if (res.ret == 1) {
          //成功
          store.dispatch('getUserInfo')
          status.value = 'success'
        } else if (res.ret == 2) {
          fail_msg.value = res.msg || '实名认证匹配失败...'
          status.value = 'fail'
          //失败
        } else if (res.ret == 3) {
          fail_msg.value = res.msg || '实名认证匹配失败...'
          status.value = 'fail'
          //失败
        } else {
          OEUI.toast(res.msg || '系统繁忙，请稍后再试')
        }
      })
      .catch(() => {
        removeTimeAnim()
        OEUI.toast('系统繁忙，请稍后再试')
      })
  }

  onMounted(() => {
    initResult()
    setTimeAnim()
  })
</script>

<style lang="scss" scoped>
  .top_nav {
    background: transparent;
  }
  .main {
    font-family: PingFang SC, PingFang SC;
    width: 100vw;
    height: 100vh;
    background: #fff;
    padding: 1.6rem 0.4rem 0;

    .rz_img {
      position: relative;
      width: 45vw;
      height: 5.0667rem;
      margin: 0 auto;
      img {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        transition: all 0.5s;
        &.show {
          opacity: 1;
        }
        &.hide {
          opacity: 0;
        }
      }
    }
    .tips {
      margin-top: 0.5333rem;
      text-align: center;
      font-size: 0.4267rem;
      color: #333;
      font-weight: normal;
    }
    .btn {
      width: 60%;
      line-height: 1.1733rem;
      height: 1.1733rem;
      border-radius: 0.5867rem;
      background: $color_main;
      color: #fff;
      font-size: 0.3733rem;
      text-align: center;
      margin: 1.3333rem auto 0;
    }
  }
</style>

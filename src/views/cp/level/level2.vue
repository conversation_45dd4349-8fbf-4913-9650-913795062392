<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">团长分成等级</div>
  </div>
  <div class="h44"></div>
  <div class="content">
    <div class="data">
      <div class="info flex flex_ac">
        <span class="head flex_s">
          <img v-lazy="unionInfo.headimg_url" type="union" alt="" />
        </span>
        <div class="">
          <div class="base flex flex_ac">
            <span class="name ws">{{ unionInfo.unionname }}</span>
            <span v-if="Number(unionInfo.level2_flag) > 0 && unionInfo.level2 > 0" class="group" :class="'group' + unionInfo.level2">
              <i>
                <img :src="getPgroupUrl('tgroup', unionInfo.level2)" alt="" />
              </i>
              <em>{{ unionInfo.level2_name }}</em>
            </span>
          </div>
          <p class="up_tips">编号:{{ unionInfo.unionid }}</p>
          <!--<p class="up_tips">差3000元业绩或有效用户≥100可升级到中级</p>-->
        </div>
      </div>
      <div class="intro">
        <p>红娘团长设有多个等级，每个等级有不同的分成标准，根据各自名下的有效推广用户数量或团队累计业绩自动升级对应等级或支付对应等级费用开通升级。</p>
        <div class="flex_dc" style="margin-top: 0.1333rem; line-height: 0.5333rem">
          <span @click="is_check = !is_check" class="flex_dc flex_s color_main" style="width: 0.5333rem; height: 0.5333rem">
            <i class="iconfont icon-dui pr" style="font-size: 0.3733rem; top: 0.0267rem" v-if="is_check"></i>
            <i v-else class="iconfont icon-rediobtn_nor" style="font-size: 0.5333rem"></i>
          </span>
          <div style="line-height: 0.5333rem; font-size: 0.32rem">
            请阅读并同意
            <span @click="getTcpHtml" class="color_main" style="text-decoration: underline">《红娘角色升级退款协议》</span>
          </div>
        </div>
      </div>
    </div>
    <div class="item bg_f" :class="unionInfo.level2 == item.grid ? 'bg' : ''" v-for="item in list" :key="item.grid">
      <div class="title flex flex_ac flex_jsb">
        <div class="flex flex_ac">
          <span class="group" :class="'group' + item.grid">
            <i>
              <img :src="getPgroupUrl('tgroup', item.grid)" alt="" />
            </i>
            <em>{{ item.asname }}</em>
          </span>
          <p v-if="item.people > 0 && item.taskflag == 1" class="people">(有效用户数≥{{ item.people }}人)</p>
        </div>
        <span v-if="unionInfo.level2 == item.grid" class="now">你处于此等级</span>
        <template v-else-if="Number(item.grid) > Number(unionInfo.level2) && item.priceflag == 1">
          <span @click="upGrade(item.grid)" v-if="Number(item.price) > 0" class="goup flex flex_ac">
            付费升级 {{ Number(item.price) }}元
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span @click="upGrade(item.grid)" v-else class="goup flex flex_ac">
            免费升级
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </template>
      </div>
      <div class="detail flex flex_v">
        <div class="flex flex_ac flex_jsb">
          <div class="flex flex_ac">
            <span class="icon">
              <img src="@/assets/images/level_tips1.png" alt="" />
            </span>
            <p class="name">用户注册奖励</p>
          </div>
          <div class="price flex flex_ac">
            <span class="flex flex_ac" style="margin-right: 0.1333rem">
              <i class="iconfont icon-nanbiao-01"></i>
              {{ Number(item.user_money1) }}元/人
            </span>
            <span class="flex flex_ac">
              <i class="iconfont icon-nvbiao-01"></i>
              {{ Number(item.user_money2) }}元/人
            </span>
          </div>
        </div>
        <div class="flex flex_ac flex_jsb">
          <div class="flex flex_ac">
            <span class="icon" style="margin-left: -0.0533rem">
              <img src="@/assets/images/level_tips2.png" alt="" />
            </span>
            <p class="name">用户消费分成</p>
          </div>
          <div class="rwfalg flex flex_ac">
            <span v-if="item.rwflag == 1">有分成</span>
            <span v-else class="fail">无分成</span>
            <span @click="showLevelDetail(item.grid)" class="flex flex_ac more">
              查看标准
              <i class="iconfont icon-youjiantou-01"></i>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <oe_popup ref="level_dialog" mode="bottom" roundStyle=".64rem" round="true">
    <div class="level_dialog">
      <span class="close iconfont icon-cha" @click="proxy.$refs.level_dialog.close()"></span>
      <p class="title">消费分成标准</p>
      <div class="item name">
        <p class="ws">用户消费项目</p>
        <div>
          <span>推广奖励(%)</span>
        </div>
      </div>
      <div class="list" v-if="state.length">
        <template v-for="item in state" :key="item.idmark">
          <template v-if="item.idmark == 'viptc' || item.idmark == 'appwxtc' || item.idmark == 'aftertc' || item.idmark == 'note' || item.idmark == 'mentor'">
            <p class="type">{{ item.title }}</p>
            <div class="item" v-for="val in item.data" :key="val.idmark">
              <p class="ws">{{ val.title }}</p>
              <div>
                <span>{{ val.rw || 0 }}%</span>
              </div>
            </div>
          </template>
          <div class="item" v-else>
            <p class="ws">{{ item.title }}</p>
            <div>
              <span>{{ item.rw || 0 }}%</span>
            </div>
          </div>
        </template>
      </div>
    </div>
  </oe_popup>

  <oe_popup ref="success_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="success_dialog">
      <div class="tips_img">
        <img src="@/assets/images/chat_tips.png" alt="" />
      </div>
      <p class="tips">恭喜你，已成功升级为【{{ upgrade_name }}红娘】</p>
      <div @click="proxy.$refs.success_dialog.close()" class="event">确定</div>
    </div>
  </oe_popup>
  <oe_popup ref="tcpTips" mode="center" width="80%" :round="true" :maskClose="false">
    <div class="tcp_tips">
      <h3 class="title">红娘角色升级退款协议</h3>
      <div class="content" v-html="htmlTcp"></div>
      <div class="btn" @click="tcpAgree">同意并遵守协议</div>
    </div>
  </oe_popup>
</template>

<script setup>
import { ref, getCurrentInstance, computed, nextTick } from 'vue'
import oe_popup from '@/oeui/popup.vue'
import { getLevel2, getLevel2Detail, upLevel2Grade } from '@/api/level.js'
import { getWeinxinPay, wechatPay } from '@/utils/jssdk.js'
import { useStore } from 'vuex'
import { setHtmlExp } from '@/utils/main'
import { getXieyi } from '@/api/login.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const unionInfo = computed(() => store.state.unionInfo)
const config = computed(() => store.state.config)
const list = ref([])

const is_check = ref(false)
const htmlTcp = ref('')
const getTcpHtml = () => {
  getXieyi({
    idmark: 'meipotk_xieyi',
  }).then(res => {
    if (res.ret == 1) {
      htmlTcp.value = setHtmlExp(res.result.content)
      nextTick(() => {
        proxy.$refs.tcpTips.open()
      })
    }
  })
}
const tcpAgree = () => {
  is_check.value = true
  proxy.$refs.tcpTips.close()
}

const getPgroupUrl = (type, val) => {
  return require(`@/assets/images/grade/${type}${val}.png`)
}

const enumList = {
  recharge: '用户充值',
  party: '报名线下活动',
  activity: '报名互选活动',
  appwx: '单次交换微信',
  superlove: '超级喜欢',
  after: '单次红娘帮约',
  gift: '赠送礼物',
  chat: '解锁聊天',
  idrz: '实名认证',
  edurz: '学历认证',
  qzform: '加入单身群',
  notein: '存入纸条',
  noteout: '取出纸条',
  chatorder: '留言咨询',
  telorder: '电话咨询',
  course: '购买课程',
  goods: '购买商品',
  union_level1: '开通、升级推广等级比例',
  union_level3: '开通、升级服务等级比例',
  union_level4: '开通、升级线下合作等级比例',
  appwxtc1: '交换微信套餐1',
  appwxtc2: '交换微信套餐2',
  appwxtc3: '交换微信套餐3',
  appwxtc4: '交换微信套餐4',
  appwxtc5: '交换微信套餐5',
  appwxtc6: '交换微信套餐6',
  appwxtc7: '交换微信套餐7',
  appwxtc8: '交换微信套餐8',
  appwxtc9: '交换微信套餐9',
  appwxtc10: '交换微信套餐10',
  aftertc1: '帮约套餐1',
  aftertc2: '帮约套餐2',
  aftertc3: '帮约套餐3',
  aftertc4: '帮约套餐4',
  aftertc5: '帮约套餐5',
  aftertc6: '帮约套餐6',
  aftertc7: '帮约套餐7',
  aftertc8: '帮约套餐8',
  aftertc9: '帮约套餐9',
  aftertc10: '帮约套餐10',
  viptc1: 'VIP套餐1',
  viptc2: 'VIP套餐2',
  viptc3: 'VIP套餐3',
  viptc4: 'VIP套餐4',
  viptc5: 'VIP套餐5',
  viptc6: 'VIP套餐6',
  viptc7: 'VIP套餐7',
  viptc8: 'VIP套餐8',
  viptc9: 'VIP套餐9',
  viptc10: 'VIP套餐10',
}

const getList = (flag, callback) => {
  getLevel2().then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      list.value = res.result.data
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

//查看分成

const state = ref([])
const is_show = ref(true)
const showLevelDetail = id => {
  if (!is_show.value) return
  is_show.value = false
  getLevel2Detail({
    id,
  })
    .then(res => {
      if (res.ret == 1) {
        res.result.data.power_data = res.result.data.power_data || []
        state.value = res.result.data.power_data
        proxy.$refs.level_dialog.open()
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试',
        })
      }
      setTimeout(() => {
        is_show.value = true
      }, 500)
    })
    .catch(() => {
      is_show.value = true
      OEUI.toast('系统繁忙,请稍后再试!')
    })
}

//下单升级
const pay_url = ref(null)
const upgrade_name = ref('')
const is_up = ref(true)
const upGrade = id => {
  if (!is_check.value) {
    return OEUI.toast('请勾选并阅读红娘角色开通/升级退款协议')
  }
  if (!is_up.value) return
  is_up.value = false
  OEUI.loading.show()
  upLevel2Grade({ grid: id })
    .then(res => {
      OEUI.loading.hide()
      if (res.ret == 1) {
        store.dispatch('getUserInfo').then(res => {
          if (res.ret == 1) {
            upgrade_name.value = res.result.info.level2_name
            proxy.$refs.success_dialog.open()
          }
        })
      } else if (res.ret == 2) {
        let url = config.value.siteurl + 'index.php?m=wap&c=pay&paynum=' + res.result + '&forward=' + encodeURIComponent(config.value.siteurl + 'vue/union/#/level2')
        if (url) {
          window.location.href = url
        }
      } else if (res.ret == 22) {
        getWeinxinPay(res.result)
          .then(data => {
            if (data.ret == 1) {
              pay_url.value = config.value.siteurl + 'index.php?m=wap&c=pay&paynum=' + data.result.paynum + '&forward=' + encodeURIComponent(config.value.siteurl + 'vue/union/#/level2')
              weixinPay(data.result.data)
            } else {
              OEUI.toast({
                text: data.msg || '下单失败，请联系客服!',
              })
            }
          })
          .catch(() => {
            OEUI.toast({
              text: '获取支付参数失败，请稍后再试',
            })
          })
      } else {
        OEUI.toast({
          text: res.msg || '下单失败，请联系客服!',
        })
      }
      setTimeout(() => {
        is_up.value = true
      }, 500)
    })
    .catch(() => {
      OEUI.loading.hide()
      is_up.value = true
      OEUI.toast('系统繁忙,请稍后再试!')
    })
}

const weixinPay = data => {
  try {
    wechatPay(data, status => {
      if (status == 'success') {
        store.dispatch('getUserInfo').then(res => {
          if (res.ret == 1) {
            upgrade_name.value = res.result.info.level2_name
            proxy.$refs.success_dialog.open()
          }
        })
      } else if (status == 'cancel') {
        OEUI.toast({
          text: '取消支付成功!',
        })
      } else if (status == 'error') {
        OEUI.toast({
          text: '下单成功,即将跳转到收银台!',
        })
        setTimeout(() => {
          if (pay_url.value) window.location.href = pay_url.value
        }, 200)
      }
    })
  } catch (error) {
    OEUI.toast({
      text: '获取支付参数失败，请稍后再试!',
    })
  }
}

getList(true)
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.content {
  font-family: PingFang SC, PingFang SC;
  margin-top: 0.0267rem;
  padding: 0 0.4267rem;
  padding-bottom: 0.5333rem;
  .data {
    background: #fff;
    border-radius: 0.32rem;
    overflow: hidden;
    margin-bottom: 0.4267rem;
    box-sizing: border-box;
    border: 0.0267rem solid #fff;
    .info {
      padding: 0.32rem 0.32rem 0.2133rem 0.32rem;
      .head {
        width: 1.3867rem;
        height: 1.3867rem;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 0.2133rem;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .base {
        .name {
          font-size: 0.4267rem;
          font-weight: 500;
          color: #31293b;
          line-height: 0.5867rem;
          max-width: 45vw;
        }
        .group {
          margin-left: 0.32rem;
        }
      }
      .up_tips {
        margin-top: 0.1333rem;
        font-size: 0.32rem;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
    }
    .intro {
      background: url('../../../assets/images/level_bg.png') no-repeat;
      background-size: cover;
      padding: 0.32rem 0.4rem 0.4rem 0.4rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.64rem;
    }
  }
  .item {
    border-radius: 0.32rem;
    padding: 0.4267rem;
    margin-bottom: 0.32rem;
    border: 0.0267rem solid #ffffff;
    box-sizing: border-box;
    &.bg {
      background: #eeebff;
    }
    .title {
      .group {
        margin-left: 0.1067rem;
      }
      .people {
        font-size: 0.32rem;
        font-weight: normal;
        color: #666666;
        line-height: 0.4533rem;
      }
      .now {
        font-size: 0.3467rem;
        font-weight: normal;
        color: $color_main;
        line-height: 0.48rem;
      }
      .goup {
        background: $color_main;
        font-size: 0.32rem;
        line-height: 0.4267rem;
        font-weight: normal;
        color: #fff;
        line-height: 0.48rem;
        padding: 0.08rem 0.2133rem;
        border-radius: 0.8rem;

        i {
          margin-left: 0.1067rem;
        }
      }
    }
    .detail {
      margin-top: 0.4267rem;

      > div {
        .icon {
          width: 0.8533rem;
          height: 0.8533rem;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 0.08rem;
          position: relative;
          top: 0.0267rem;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .name {
          font-size: 0.3733rem;
          font-weight: 500;
          color: #31293b;
          line-height: 0.64rem;
        }
        .price {
          font-size: 0.32rem;
          font-weight: 500;
          color: #666666;
          line-height: 0.64rem;

          i {
            margin-right: 0.0533rem;
          }

          .icon-nanbiao-01 {
            color: #0570f1;
          }

          .icon-nvbiao-01 {
            color: #fe6897;
          }
        }
        .rwfalg {
          span {
            font-size: 0.32rem;
            font-weight: normal;
            color: $color_main;
            line-height: 0.64rem;
          }

          .fail {
            color: #ccc;
          }

          .more {
            color: #999999;
            margin-left: 0.5333rem;
          }
        }
      }
    }
  }
}

.level_dialog {
  padding-bottom: 0.32rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;
  max-height: 60vh;
  overflow-y: auto;
  .close {
    position: absolute;
    right: 0.5333rem;
    top: 0.6133rem;
    font-size: 0.5333rem;
  }
  .title {
    margin-top: 0.32rem;
    font-size: 0.48rem;
    font-weight: 600;
    color: #31293b;
    line-height: 0.6667rem;
    padding: 0.2667rem 0.4267rem;
  }
  .name {
    border-radius: 0.64rem 0.64rem 0 0;
    position: sticky;
    top: 0;
    background: #fff;
    width: calc(100% - 0.8533rem);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.32rem 0.4267rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.5333rem;
    font-weight: 600 !important;
    color: #31293b !important;
    p {
      max-width: 3.2rem;
    }
    span {
      font-weight: 600 !important;
      color: #31293b !important;
      margin-left: 0.8rem;
      width: 2.1333rem;
      text-align: center;
    }
  }
  .list {
    padding: 0 0.4267rem;
    .type {
      color: #f40;
      font-size: 0.4267rem;
      padding: 0.2133rem 0;
    }
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.32rem 0;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5333rem;
      p {
        max-width: 3.2rem;
      }
      span {
        color: #666666;
        margin-left: 0.8rem;
        width: 2.1333rem;
        text-align: center;
      }
      &.name {
        font-weight: 600 !important;
        color: #31293b !important;
        span {
          font-weight: 600 !important;
          color: #31293b !important;
        }
      }
    }
  }
}
.success_dialog {
  text-align: center;
  padding: 0.64rem 1.2533rem;
  background: url('../../../assets/images/dailog_bg.png');
  background-size: cover;
  border-radius: 0.64rem;
  overflow: hidden;

  .tips_img {
    position: absolute;
    left: 50%;
    top: -1.7333rem;
    transform: translateX(-50%);
    width: 4.2667rem;
    height: 2.6133rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .tips {
    margin-top: 0.64rem;
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
  }
  .event {
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin-top: 0.64rem;
    padding: 0.24rem 0;
  }
}
.tcp_tips {
  .title {
    text-align: center;
    font-size: 0.48rem;
    font-weight: 600;
    padding-top: 0.67rem;
    padding-bottom: 0;
    color: #333333;
  }

  .content {
    font-size: 0.373rem;
    color: #333333;
    padding: 0.533rem 0.533rem 0;
    max-height: 7rem;
    overflow-x: hidden;
    overflow-y: scroll;

    span {
      display: inline-block;
    }
  }

  .btn {
    margin: 0.533rem auto 0.667rem;
    width: 80%;
    height: 1.2rem;
    line-height: 1.2rem;
    color: #ffffff;
    background: $color_main;
    border-radius: 1rem;
    text-align: center;
    font-size: 0.4rem;
    cursor: pointer;
  }
}
</style>

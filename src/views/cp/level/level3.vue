<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">服务分成等级</div>
  </div>
  <div class="h44"></div>
  <div class="content">
    <div class="data">
      <div class="info flex flex_ac">
        <span class="head flex_s">
          <img v-lazy="unionInfo.headimg_url" type="union" alt="" />
        </span>
        <div class="">
          <div class="base flex flex_ac">
            <span class="name ws">{{ unionInfo.unionname }}</span>
            <span v-if="Number(unionInfo.level3_flag) > 0 && unionInfo.level3 > 0" class="group" :class="'group' + unionInfo.level3">
              <i>
                <img :src="getPgroupUrl('sgroup', unionInfo.level3)" alt="" />
              </i>
              <em>{{ unionInfo.level3_name }}</em>
            </span>
          </div>
          <p class="up_tips">编号:{{ unionInfo.unionid }}</p>
          <!--<p class="up_tips">差3000元业绩或有效用户≥100可升级到中级</p>-->
        </div>
      </div>
      <div class="intro">
        <p>服务红娘设有多个等级，每个等级有不同的分成标准，根据累计帮约成功次数自定升级对应等级或支付对应等级费用开通升级。</p>
        <div class="flex_dc" style="margin-top: 0.1333rem; line-height: 0.5333rem">
          <span @click="is_check = !is_check" class="flex_dc flex_s color_main" style="width: 0.5333rem; height: 0.5333rem">
            <i class="iconfont icon-dui pr" style="font-size: 0.3733rem; top: 0.0267rem" v-if="is_check"></i>
            <i v-else class="iconfont icon-rediobtn_nor" style="font-size: 0.5333rem"></i>
          </span>
          <div style="line-height: 0.5333rem; font-size: 0.32rem">
            请阅读并同意
            <span @click="getTcpHtml" class="color_main" style="text-decoration: underline">《红娘角色升级退款协议》</span>
          </div>
        </div>
      </div>
    </div>
    <div class="item bg_f" :class="unionInfo.level3 == item.grid ? 'bg' : ''" v-for="item in list" :key="item.grid">
      <div class="title flex flex_ac flex_jsb">
        <div class="flex flex_ac">
          <span class="group" :class="'group' + item.grid">
            <i>
              <img :src="getPgroupUrl('sgroup', item.grid)" alt="" />
            </i>
            <em>{{ item.asname }}</em>
          </span>
          <p v-if="item.people > 0 && item.taskflag == 1" class="people">(帮约成功数≥{{ item.people }}人)</p>
        </div>
        <span v-if="unionInfo.level3 == item.grid" class="now">你处于此等级</span>
        <template v-else-if="Number(item.grid) > Number(unionInfo.level3) && item.priceflag == 1">
          <span @click="upGrade(item.grid)" v-if="Number(item.price) > 0" class="goup flex flex_ac">
            付费升级 {{ Number(item.price) }}元
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span @click="upGrade(item.grid)" v-else class="goup flex flex_ac">
            免费升级
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </template>
      </div>
      <div class="detail flex flex_v" v-if="item.rwflag == 1">
        <div class="flex flex_ac flex_jsb">
          <div class="flex flex_ac">
            <span class="icon">
              <img src="@/assets/images/level3_tips1.png" alt="" />
            </span>
            <span class="name">资料审核</span>
          </div>
          <p class="flex flex_ac price">
            <span>
              <em>{{ Number(item.audit_money) }}</em>
              元/人
            </span>
          </p>
        </div>
        <div class="flex flex_ac flex_jsb">
          <div class="flex flex_ac">
            <span class="icon">
              <img src="@/assets/images/level3_tips2.png" alt="" />
            </span>
            <span class="name">帮约成功</span>
          </div>
          <p class="flex flex_ac price">
            <span>
              <em>{{ Number(item.after_money) }}</em>
              元/人
            </span>
          </p>
        </div>
      </div>
    </div>
  </div>
  <oe_popup ref="success_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="success_dialog">
      <div class="tips_img">
        <img src="@/assets/images/chat_tips.png" alt="" />
      </div>
      <p class="tips">恭喜你，已成功升级为【{{ upgrade_name }}红娘】</p>
      <div @click="proxy.$refs.success_dialog.close()" class="event">确定</div>
    </div>
  </oe_popup>
  <oe_popup ref="tcpTips" mode="center" width="80%" :round="true" :maskClose="false">
    <div class="tcp_tips">
      <h3 class="title">红娘角色升级退款协议</h3>
      <div class="content" v-html="htmlTcp"></div>
      <div class="btn" @click="tcpAgree">同意并遵守协议</div>
    </div>
  </oe_popup>
</template>

<script setup>
import { ref, getCurrentInstance, computed, nextTick } from 'vue'
import oe_popup from '@/oeui/popup.vue'
import { getLevel3, upLevel3Grade } from '@/api/level.js'
import { getWeinxinPay, wechatPay } from '@/utils/jssdk.js'
import { useStore } from 'vuex'
import { setHtmlExp } from '@/utils/main'
import { getXieyi } from '@/api/login.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const unionInfo = computed(() => store.state.unionInfo)
const config = computed(() => store.state.config)
const list = ref([])

const is_check = ref(false)
const htmlTcp = ref('')
const getTcpHtml = () => {
  getXieyi({
    idmark: 'meipotk_xieyi',
  }).then(res => {
    if (res.ret == 1) {
      htmlTcp.value = setHtmlExp(res.result.content)
      nextTick(() => {
        proxy.$refs.tcpTips.open()
      })
    }
  })
}
const tcpAgree = () => {
  is_check.value = true
  proxy.$refs.tcpTips.close()
}

const getList = (flag, callback) => {
  getLevel3().then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      list.value = res.result.data
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const getPgroupUrl = (type, val) => {
  return require(`@/assets/images/grade/${type}${val}.png`)
}

//下单升级
const pay_url = ref(null)
const upgrade_name = ref('')
const is_up = ref(true)
const upGrade = id => {
  if (!is_check.value) {
    return OEUI.toast('请勾选并阅读红娘角色开通/升级退款协议')
  }
  if (!is_up.value) return
  is_up.value = false
  OEUI.loading.show()
  upLevel3Grade({ grid: id })
    .then(res => {
      OEUI.loading.hide()
      if (res.ret == 1) {
        store.dispatch('getUserInfo').then(res => {
          if (res.ret == 1) {
            upgrade_name.value = res.result.info.level3_name
            proxy.$refs.success_dialog.open()
          }
        })
      } else if (res.ret == 2) {
        let url = config.value.siteurl + 'index.php?m=wap&c=pay&paynum=' + res.result + '&forward=' + encodeURIComponent(config.value.siteurl + 'vue/union/#/level3')
        if (url) {
          window.location.href = url
        }
      } else if (res.ret == 22) {
        getWeinxinPay(res.result)
          .then(data => {
            if (data.ret == 1) {
              pay_url.value = config.value.siteurl + 'index.php?m=wap&c=pay&paynum=' + data.result.paynum + '&forward=' + encodeURIComponent(config.value.siteurl + 'vue/union/#/level3')
              weixinPay(data.result.data)
            } else {
              OEUI.toast({
                text: data.msg || '下单失败，请联系客服!',
              })
            }
          })
          .catch(() => {
            OEUI.toast({
              text: '获取支付参数失败，请稍后再试',
            })
          })
      } else {
        OEUI.toast({
          text: res.msg || '下单失败，请联系客服!',
        })
      }
      setTimeout(() => {
        is_up.value = true
      }, 500)
    })
    .catch(() => {
      OEUI.loading.hide()
      is_up.value = true
      OEUI.toast('系统繁忙,请稍后再试!')
    })
}

const weixinPay = data => {
  try {
    wechatPay(data, status => {
      if (status == 'success') {
        store.dispatch('getUserInfo').then(res => {
          if (res.ret == 1) {
            upgrade_name.value = res.result.info.level3_name
            proxy.$refs.success_dialog.open()
          }
        })
      } else if (status == 'cancel') {
        OEUI.toast({
          text: '取消支付成功!',
        })
      } else if (status == 'error') {
        OEUI.toast({
          text: '下单成功,即将跳转到收银台!',
        })
        setTimeout(() => {
          if (pay_url.value) window.location.href = pay_url.value
        }, 200)
      }
    })
  } catch (error) {
    OEUI.toast({
      text: '获取支付参数失败，请稍后再试!',
    })
  }
}
getList()
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.content {
  font-family: PingFang SC, PingFang SC;
  margin-top: 0.0267rem;
  padding: 0 0.4267rem;
  padding-bottom: 0.5333rem;
  .data {
    background: #fff;
    border-radius: 0.32rem;
    overflow: hidden;
    margin-bottom: 0.4267rem;
    box-sizing: border-box;
    border: 0.0267rem solid #fff;
    .info {
      padding: 0.32rem 0.32rem 0.2133rem 0.32rem;
      .head {
        width: 1.3867rem;
        height: 1.3867rem;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 0.2133rem;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .base {
        .name {
          font-size: 0.4267rem;
          font-weight: 500;
          color: #31293b;
          line-height: 0.5867rem;
          max-width: 45vw;
        }
        .group {
          margin-left: 0.32rem;
        }
      }
      .up_tips {
        margin-top: 0.1333rem;
        font-size: 0.32rem;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
    }
    .intro {
      background: url('../../../assets/images/level_bg.png') no-repeat;
      background-size: cover;
      padding: 0.32rem 0.4rem 0.4rem 0.4rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.64rem;
    }
  }
  .item {
    border-radius: 0.32rem;
    padding: 0.4267rem;
    margin-bottom: 0.32rem;
    border: 0.0267rem solid #ffffff;
    box-sizing: border-box;
    &.bg {
      background: #eeebff;
    }
    .title {
      .group {
        margin-left: 0.1067rem;
      }
      .people {
        font-size: 0.32rem;
        font-weight: normal;
        color: #666666;
        line-height: 0.4533rem;
      }
      .now {
        font-size: 0.3467rem;
        font-weight: normal;
        color: $color_main;
        line-height: 0.48rem;
      }
      .goup {
        background: $color_main;
        font-size: 0.32rem;
        line-height: 0.4267rem;
        font-weight: normal;
        color: #fff;
        line-height: 0.48rem;
        padding: 0.08rem 0.2133rem;
        border-radius: 0.8rem;

        i {
          margin-left: 0.1067rem;
        }
      }
    }
    .detail {
      margin-top: 0.4267rem;

      > div {
        .icon {
          width: 0.8533rem;
          height: 0.8533rem;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 0.08rem;
          position: relative;
          top: 0.0267rem;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .name {
          font-size: 0.3733rem;
          font-weight: 500;
          color: #31293b;
          line-height: 0.64rem;
        }
        .price {
          font-size: 0.32rem;
          font-weight: 500;
          color: #666666;
          line-height: 0.64rem;
        }
      }
    }
  }
}
.success_dialog {
  text-align: center;
  padding: 0.64rem 1.2533rem;
  background: url('../../../assets/images/dailog_bg.png');
  background-size: cover;
  border-radius: 0.64rem;
  overflow: hidden;

  .tips_img {
    position: absolute;
    left: 50%;
    top: -1.7333rem;
    transform: translateX(-50%);
    width: 4.2667rem;
    height: 2.6133rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .tips {
    margin-top: 0.64rem;
    font-size: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
  }
  .event {
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin-top: 0.64rem;
    padding: 0.24rem 0;
  }
}
.tcp_tips {
  .title {
    text-align: center;
    font-size: 0.48rem;
    font-weight: 600;
    padding-top: 0.67rem;
    padding-bottom: 0;
    color: #333333;
  }

  .content {
    font-size: 0.373rem;
    color: #333333;
    padding: 0.533rem 0.533rem 0;
    max-height: 7rem;
    overflow-x: hidden;
    overflow-y: scroll;

    span {
      display: inline-block;
    }
  }

  .btn {
    margin: 0.533rem auto 0.667rem;
    width: 80%;
    height: 1.2rem;
    line-height: 1.2rem;
    color: #ffffff;
    background: $color_main;
    border-radius: 1rem;
    text-align: center;
    font-size: 0.4rem;
    cursor: pointer;
  }
}
</style>

<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">门店业绩</div>
  </div>
  <div class="h44"></div>
  <div class="main">
    <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="filter flex_dc">
        <div class="flex_dc" @click="changSettle">
          <span>{{ settleList[state.s_settle].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_settle ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changOrderby">
          <span>{{ orderbyList.find(v => v.val == state.s_orderby).name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_orderby ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changTime">
          <span>{{ timeList[state.s_time].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
        </div>
      </div>
      <div class="flex flex_jsb flex_1 data" style="padding: 0 0.4267rem">
        <div class="flex_dc flex_v">
          <p>{{ rwshop_data.total_orders }}</p>
          <span class="fn_i">签单合同</span>
        </div>
        <div class="flex_dc flex_v">
          <p>{{ Number(rwshop_data.crmfinance_amount) >= 10000 ? (Number(rwshop_data.crmfinance_amount) / 10000).toFixed(2) : rwshop_data.crmfinance_amount }}</p>
          <span class="fn_i">累计收款({{ Number(rwshop_data.crmfinance_amount) >= 10000 ? '万' : '元' }})</span>
        </div>
        <div class="flex_dc flex_v">
          <p>{{ Number(rwshop_data.crmorder_income) >= 10000 ? (Number(rwshop_data.crmorder_income) / 10000).toFixed(2) : rwshop_data.crmorder_income }}</p>
          <span class="fn_i">累计分成({{ Number(rwshop_data.crmorder_income) >= 10000 ? '万' : '元' }})</span>
        </div>
      </div>
      <div class="list">
        <template v-for="item in list" :key="item.userid">
          <list_item :item="item" />
        </template>
      </div>
    </oeui-list>
  </div>

  <search_item ref="searchSettle" type="settle" :list="settleList" :current="state.s_settle" @changItem="selectSettle" @close="closeItem"></search_item>

  <search_item ref="searchTime" type="time" :list="timeList" :current="state.s_time" @changItem="selectTime" @close="closeItem"></search_item>

  <search_item ref="searchOrderby" type="orderby" :list="orderbyList" :current="state.s_orderby" @changItem="selectOrderby" @close="closeItem"></search_item>
</template>

<script>
export default {
  name: 'Crmincome',
}
</script>

<script setup>
import { ref, getCurrentInstance, reactive, onMounted, nextTick } from 'vue'
import oeuiList from '@/oeui/list.vue'
import search_item from '@/components/search_itme.vue'
import list_item from '@/views/income/components/crmfinance_item.vue'
import { getCrmfinance } from '@/api/finance.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')

const timeList = [
  { name: '全部时间', val: 0 },
  { name: '今天', val: 1 },
  { name: '昨天', val: 2 },
  { name: '本周', val: 3 },
  { name: '上周', val: 4 },
  { name: '本月', val: 5 },
  { name: '上月', val: 6 },
  { name: '今年', val: 7 },
  { name: '去年', val: 8 },
]
const orderbyList = [
  { name: '时间降序', val: 'id_desc' },
  { name: '时间升序', val: 'id_asc' },
  { name: '金额从高到低', val: 'amount_desc' },
  { name: '金额从低到高', val: 'amount_asc' },
]

const settleList = [
  { name: '全部状态', val: 0 },
  { name: '已结算', val: 1 },
  { name: '不结算', val: 2 },
  { name: '待结算', val: 3 },
]

const rwshop_data = reactive({
  total_orders: 0,
  crmfinance_amount: 0,
  crmorder_income: 0,
})

const state = reactive({
  s_time: 0,
  s_orderby: 'id_desc',
  s_settle: 0,
})
const is_time = ref(false)
const is_orderby = ref(false)
const is_settle = ref(false)

onMounted(() => {
  getList()
})

const changSettle = () => {
  proxy.$refs.searchTime.close()
  proxy.$refs.searchOrderby.close()
  is_time.value = false
  is_orderby.value = false
  nextTick(() => {
    if (is_settle.value) {
      is_settle.value = false
      proxy.$refs.searchSettle.close()
    } else {
      is_settle.value = true
      proxy.$refs.searchSettle.open()
    }
  })
}
const changOrderby = () => {
  proxy.$refs.searchSettle.close()
  proxy.$refs.searchTime.close()
  is_time.value = false
  is_settle.value = false
  nextTick(() => {
    if (is_orderby.value) {
      is_orderby.value = false
      proxy.$refs.searchOrderby.close()
    } else {
      is_orderby.value = true
      proxy.$refs.searchOrderby.open()
    }
  })
}
const changTime = () => {
  proxy.$refs.searchSettle.close()
  proxy.$refs.searchOrderby.close()
  is_orderby.value = false
  is_settle.value = false
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}

const selectSettle = val => {
  is_settle.value = false
  state.s_settle = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}
const selectOrderby = val => {
  is_orderby.value = false
  state.s_orderby = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}
const selectTime = val => {
  is_time.value = false
  state.s_time = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const closeItem = () => {
  is_time.value = false
  is_orderby.value = false
  is_settle.value = false
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getCrmfinance({
    page: page.value,
    ...state,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (page.value == 1) {
        rwshop_data.total_orders = res.result.total_orders
        rwshop_data.crmfinance_amount = res.result.crmfinance_amount
        rwshop_data.crmorder_income = res.result.crmorder_income
      }

      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}
.font_din {
  font-family: 'cpDin';
}
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.main {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.0533rem 0.4267rem;
  .filter {
    height: 1.0667rem;
    border-radius: 0.32rem;
    background: rgba(255, 255, 255, 0.5);
    div {
      flex: 1;
      color: #2a2546;
      text-align: center;
      span {
        margin-right: 0.1333rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }
      .color_c3 {
        color: #c5c3c7;
      }

      .hide {
        transform: rotate(90deg);
      }
      .show {
        transform: rotate(-90deg);
        position: relative;
        top: 0.0267rem;
      }
    }
  }
  .data {
    margin-top: 0.9067rem;
    div {
      p {
        font-size: 0.64rem;
        font-weight: 700;
        color: #31293b;
        line-height: 0.7733rem;
        @extend .font_din;
      }
      span {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
    }
  }

  .list {
    margin-top: 0.64rem;
    .item_box {
      cursor: pointer;
      background: #fff;
      border-radius: 0.32rem;
      margin-bottom: 0.32rem;
      .tit {
        padding-top: 0.2667rem;
        padding: 0.2667rem 0.32rem 0 0.32rem;
        .time {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          color: #999999;
          i {
            position: relative;
            top: 0.0267rem;
            margin-right: 0.1067rem;
          }
        }
        .status {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          color: #999999;
        }
      }
      .info {
        border-bottom: 0.0267rem solid #f4f6f7;
        padding: 0.32rem;
        .head {
          width: 1.1733rem;
          height: 1.1733rem;
          border-radius: 0.2133rem;
          overflow: hidden;
          margin-right: 0.32rem;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .name {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-weight: 500;
          color: #31293b;
        }
        i {
          position: relative;
          top: 0.0267rem;
          margin-left: 0.1333rem;
        }
        .icon-nanbiao-01 {
          color: #0570f1;
        }
        .icon-nvbiao-01 {
          color: #fe6897;
        }
        .money {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-weight: 500;
          color: #7d68fe;
        }
      }
    }
  }
}
</style>

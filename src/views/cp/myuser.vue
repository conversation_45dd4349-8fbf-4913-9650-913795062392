<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="back" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">单位用户</div>
  </div>
  <div class="h44"></div>
  <div class="main">
    <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="filter flex_dc">
        <div class="flex_dc" @click="changFlag">
          <span>{{ flagList[state.s_flag].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_flag ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changTime">
          <span>{{ timeList[state.s_time].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
        </div>

        <div class="flex_dc" @click="changOrderby">
          <span>{{ state.s_orderby == 'id_desc' ? '时间降序' : '时间升序' }}</span>
          <span class="flex_dc flex_v">
            <i :class="state.s_orderby == 'id_desc' ? 'color_c3' : ''" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; transform: rotate(180deg); top: 0.08rem"></i>
            <i :class="state.s_orderby == 'id_desc' ? '' : 'color_c3'" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; bottom: 0.0533rem"></i>
          </span>
        </div>
      </div>
      <div class="flex flex_jsb flex_1 data" style="padding: 0 0.4267rem">
        <div class="flex_dc flex_v">
          <p>{{ data.total }}</p>
          <span class="fn_i">总人数</span>
        </div>
        <div class="flex_dc flex_v">
          <p>{{ data.pass_total }}</p>
          <span class="fn_i">有效人数</span>
        </div>
        <div class="flex_dc flex_v">
          <p>{{ data.pass_user1 }}</p>
          <span class="fn_i">男用户</span>
        </div>
        <div class="flex_dc flex_v">
          <p>{{ data.pass_user2 }}</p>
          <span class="fn_i">女用户</span>
        </div>
      </div>
      <div class="tip">推广的用户成功注册后将与你绑定推广关系并纳入名下用户，用户资料审核通过你将获得推广拉新奖励，用户在平台消费你将获得消费分成</div>
      <div class="search flex flex_ac flex_jsb">
        <i class="iconfont icon-sousuo"></i>
        <input type="text" v-model="state.s_name" class="flex_1" placeholder="请输入用户昵称或编号" />
        <span class="btn" @click="searchName">搜索</span>
      </div>
      <div class="list" v-if="list.length">
        <template v-for="item in list" :key="item.userid">
          <div class="item_box" @click="$router.push('/user/detail?id=' + item.userid)" v-if="item.userid">
            <div class="tit flex flex_ac flex_jsb">
              <div class="time flex flex_ac">
                <i class="iconfont icon-shizhong"></i>
                <p>注册时间：{{ getTime(item.addtime) }}</p>
              </div>
              <div class="status color_green" v-if="item.enterflag == 1">审核通过</div>
              <div class="status color_red" v-else-if="item.enterflag == 2">审核失败</div>
              <div class="status" v-else>待审核</div>
            </div>
            <div class="info_item">
              <div class="info_box">
                <div class="head">
                  <img v-if="item.headimg" type="user" v-lazy="item.headimg_m1_url" alt="" />
                  <template v-else>
                    <img v-if="item.gender == 1" src="@/assets/images/gender_1.png" alt="" />
                    <img v-else-if="item.gender == 2" src="@/assets/images/gender_2.png" alt="" />
                    <img v-else src="@/assets/images/gender_0.png" alt="" />
                  </template>
                </div>
                <div class="info">
                  <p class="job">
                    {{ item.age }}岁
                    <template v-if="item.job > 0">/{{ item.job_t }}</template>
                  </p>
                  <p class="name">
                    昵称: &nbsp;
                    <em>{{ item.username }}</em>
                    &emsp; (编号:&nbsp;{{ item.userid }})
                  </p>
                  <p class="base" v-if="item.marry > 0 || item.education > 0 || item.height > 0">
                    <template v-if="item.marry > 0">{{ item.marry_t }}</template>
                    <template v-if="item.marry > 0">·</template>
                    <template v-if="item.education > 0">{{ item.education_t }}</template>
                    <template v-if="item.education > 0">·</template>
                    <template v-if="item.height > 0">{{ item.height }}cm</template>
                  </p>
                </div>
              </div>
              <div class="operate flex flex_ac flex_jsb" v-if="unionInfo.enter_name">
                <div class="flex flex_ac" @click.stop="editUser(item.userid)">
                  <img class="edit" src="@/assets/images/edit_to.png" />
                  <span style="color: #7d68fe">编辑资料</span>
                </div>
                <template v-if="item.enterflag == 0">
                  <div class="flex flex_ac">
                    <span @click.stop="auditPass(item.userid)" style="color: #7d68fe; margin-right: 0.5867rem">通过</span>
                    <span @click.stop="auditFail(item.userid)" style="color: #ff6666">不通过</span>
                  </div>
                </template>
                <template v-if="item.enterflag == 1">
                  <div class="flex flex_ac">
                    <span @click.stop="unBindenter(item.userid)" style="color: #ff6666">解除单位关系</span>
                  </div>
                </template>
              </div>
            </div>
            <!-- <div class="flex_dc view" v-if="item.wx_power > 0">
              <span v-if="item.wx_power == 1" @click.stop="viewContact(item.userid)">查看联系方式</span>
              <span class="end" v-else @click.stop="viewContact(item.userid)">已查看联系方式</span>
            </div> -->
          </div>
        </template>
      </div>
    </oeui-list>
  </div>

  <search_item ref="searchTime" type="time" :list="timeList" :current="state.s_time" @changItem="selectTime" @close="closeItem"></search_item>

  <search_item ref="searchFlag" type="flag" :list="flagList" :current="state.s_flag" @changItem="selectFlag" @close="closeItem"></search_item>

  <oe_popup ref="user_dialog" width="7.84rem" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="user_dialog" v-if="info.userid">
      <div class="head flex_dc" v-if="info.headimg_url">
        <span>
          <img v-lazy="info.headimg_url" type="union" />
        </span>
      </div>
      <div class="name flex_dc">
        <span class="unionname">{{ info.username }}</span>
        <span class="useiid">(编号:{{ info.userid }})</span>
      </div>
      <div class="data">
        <div v-if="info.mobile" class="item flex flex_ac flex_jsb">
          <p>
            电话:
            <em>{{ info.mobile || '--' }}</em>
          </p>
          <span @click="copy(info.mobile)">复制</span>
        </div>
        <div v-if="info.weixin" class="item flex flex_ac flex_jsb" style="margin-top: 0.24rem">
          <p>
            微信:
            <em>{{ info.weixin || '--' }}</em>
          </p>
          <span @click="copy(info.weixin)">复制</span>
        </div>
        <div v-if="info.qq" class="item flex flex_ac flex_jsb" style="margin-top: 0.24rem">
          <p>
            QQ:
            <em>{{ info.qq || '--' }}</em>
          </p>
          <span @click="copy(info.qq)">复制</span>
        </div>
        <div v-if="info.email" class="item flex flex_ac flex_jsb" style="margin-top: 0.24rem">
          <p>
            邮箱:
            <em>{{ info.email || '--' }}</em>
          </p>
          <span @click="copy(info.email)">复制</span>
        </div>
      </div>
      <div @click="proxy.$refs.user_dialog.close()" class="event">关闭</div>
    </div>
  </oe_popup>
</template>

<script>
export default {
  name: 'Myuser',
}
</script>

<script setup>
import { ref, getCurrentInstance, reactive, onMounted, nextTick, computed, watch } from 'vue'
import { useStore } from 'vuex'
import oeuiList from '@/oeui/list.vue'
import oe_popup from '@/oeui/popup.vue'
import search_item from '@/components/search_itme.vue'
// getUserContact
import { getMyuser, auditenter, unbindenter } from '@/api/union.js'
import { getTime } from '@/utils/hooks.js'
import { useRouter } from 'vue-router'
const { proxy } = getCurrentInstance()
const router = useRouter()

const OEUI = proxy.OEUI

const store = useStore()
const unionInfo = computed(() => store.state.unionInfo)

watch(
  () => unionInfo.value,
  newInfo => {
    if (newInfo) {
      unionInfo.value = newInfo
    }
  },
  { immediate: true, deep: true },
)

const back = () => {
  if (window.history.length <= 1) {
    router.push('/home')
    return
  } else if (!window.history.state.back) {
    router.replace({ path: '/home' })
  } else {
    router.back()
  }
}

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')

const flagList = [
  { name: '全部状态', val: 0 },
  { name: '已通过', val: 1 },
  { name: '未通过', val: 2 },
  { name: '待审核', val: 3 },
]

const timeList = [
  { name: '全部时间', val: 0 },
  { name: '今天', val: 1 },
  { name: '昨天', val: 2 },
  { name: '本周', val: 3 },
  { name: '上周', val: 4 },
  { name: '本月', val: 5 },
  { name: '上月', val: 6 },
  { name: '今年', val: 7 },
  { name: '去年', val: 8 },
]

const data = reactive({
  pass_total: 0,
  pass_user1: 0,
  pass_user2: 0,
  total: 0,
})

const state = reactive({
  s_time: 0,
  s_orderby: 'id_desc',
  s_name: '',
  s_flag: 0,
})
const is_time = ref(false)
const is_flag = ref(false)

onMounted(() => {
  getList()
})

const editUser = id => {
  router.push('/user/userEdit?id=' + id)
}

// const viewContact = id => {
//   getContact(id)
// }
const info = ref({})
// const getContact = id => {
//   info.value = {}
//   getUserContact({ id }).then(res => {
//     if (res.ret == 1) {
//       list.value.forEach(v => {
//         if (v.userid == id) {
//           v.wx_power = 2
//         }
//       })
//       info.value = res.result.data
//       nextTick(() => {
//         proxy.$refs.user_dialog.open()
//       })
//     } else {
//       OEUI.toast({
//         text: res.msg || '系统繁忙，请稍后再试',
//       })
//     }
//   })
// }
const copy = data => {
  let elInput = document.createElement('input')
  elInput.value = data
  document.body.appendChild(elInput)
  elInput.select()
  document.execCommand('Copy')
  OEUI.toast({
    text: '复制成功',
  })
  elInput.remove()
}

const searchName = () => {
  //if (!state.s_username.length) return
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const changFlag = () => {
  proxy.$refs.searchTime.close()
  is_time.value = false
  nextTick(() => {
    if (is_flag.value) {
      is_flag.value = false
      proxy.$refs.searchFlag.close()
    } else {
      is_flag.value = true
      proxy.$refs.searchFlag.open()
    }
  })
}

const closeItem = () => {
  is_flag.value = false
  is_time.value = false
}

const selectFlag = val => {
  is_flag.value = false
  state.s_flag = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const changTime = () => {
  proxy.$refs.searchFlag.close()
  is_flag.value = false
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}

const selectTime = val => {
  is_time.value = false
  state.s_time = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}
const changOrderby = () => {
  if (state.s_orderby == 'id_desc') {
    state.s_orderby = 'id_asc'
  } else {
    state.s_orderby = 'id_desc'
  }
  page.value = 1
  //list.value = []
  //listStatus.value = 'loading'
  getList(true)
}

const unBindenter = id => {
  OEUI.modal({
    text: '确定要解除该用户绑定的单位关系吗？该操作不可逆，请谨慎操作！',
    confirmText: '解除绑定',
    confirm: () => {
      unbindenter({ userid: id }).then(res => {
        if (res.ret == 1) {
          OEUI.toast({
            text: '操作成功',
          })
          searchName()
        } else {
          OEUI.toast({
            text: res.msg || '系统繁忙，请稍后再试',
          })
        }
      })
    },
  })
}

const auditPass = id => {
  OEUI.modal({
    text: '确定该用户属于您的单位吗？该操作不可逆，请谨慎操作！',
    confirmText: '确认通过',
    confirm: () => {
      auditenter({ userid: id, enterflag: 1 }).then(res => {
        if (res.ret == 1) {
          OEUI.toast({
            text: '操作成功',
          })
          searchName()
        } else {
          OEUI.toast({
            text: res.msg || '系统繁忙，请稍后再试',
          })
        }
      })
    },
  })
}

const auditFail = id => {
  OEUI.modal({
    text: '确定该用户<span style="color: #ff6666;">不是</span>您单位的吗？该操作不可逆，请谨慎操作！',
    confirmText: '拒绝通过',
    isHtml: true,
    confirm: () => {
      auditenter({ userid: id, enterflag: 2 }).then(res => {
        if (res.ret == 1) {
          OEUI.toast({
            text: '操作成功',
          })
          searchName()
        } else {
          OEUI.toast({
            text: res.msg || '系统繁忙，请稍后再试',
          })
        }
      })
    },
  })
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getMyuser({
    page: page.value,
    ...state,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (page.value == 1) {
        data.total = res.result.total || 0
        data.pass_total = res.result.pass_total || 0
        data.pass_user1 = res.result.pass_user1 || 0
        data.pass_user2 = res.result.pass_user2 || 0
      }

      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}

.font_din {
  font-family: 'cpDin';
}

.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}

.main {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.0533rem 0.4267rem;

  .filter {
    height: 1.0667rem;
    border-radius: 0.32rem;
    background: rgba(255, 255, 255, 0.5);

    div {
      flex: 1;
      color: #2a2546;
      text-align: center;

      span {
        margin-right: 0.1333rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        max-width: 1.8667rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .color_c3 {
        color: #c5c3c7;
      }

      .hide {
        transform: rotate(90deg);
      }

      .show {
        transform: rotate(-90deg);
        position: relative;
        top: 0.0267rem;
      }
    }
  }

  .data {
    margin-top: 0.9067rem;

    div {
      p {
        font-size: 0.64rem;
        font-weight: 700;
        color: #31293b;
        line-height: 0.7733rem;
        @extend .font_din;
      }

      span {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
    }
  }

  .tip {
    margin-top: 0.4267rem;
    border-radius: 0.32rem;
    background: #ececf9;
    padding: 0.32rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.6133rem;
  }

  .search {
    margin-top: 0.3733rem;
    background: #fff;
    padding: 0.1067rem 0.2133rem;
    border-radius: 0.32rem;

    i {
      padding-left: 0.1067rem;
      color: #000;
      font-size: 0.48rem;
    }

    input {
      padding: 0 0.2133rem;
    }

    .btn {
      background: #7d68fe;
      color: #fff;
      font-size: 0.3467rem;
      line-height: 0.48rem;
      font-weight: normal;
      padding: 0.1867rem 0.4267rem;
      border-radius: 0.4267rem;
    }
  }

  .list {
    margin-top: 0.3733rem;

    .item_box {
      cursor: pointer;
      background: #fff;
      border-radius: 0.32rem;
      margin-bottom: 0.32rem;

      .tit {
        padding: 0.2667rem 0.32rem 0 0.32rem;
        padding-top: 0.32rem;

        .time {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          color: #999999;

          i {
            position: relative;
            //top: 0.0267rem;
            margin-right: 0.1067rem;
          }
        }

        .status {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          color: #ff6666;
        }
      }

      .info_item {
        padding: 0.32rem;
        box-sizing: border-box;
        display: flex;
        align-content: center;
        flex-direction: column;
      }

      .info_box {
        box-sizing: border-box;
        display: flex;
        align-content: center;

        .head {
          width: 2.0533rem;
          height: 2.0533rem;
          border-radius: 0.2133rem;
          overflow: hidden;
          margin-right: 0.2133rem;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .job {
            font-size: 0.48rem;
            line-height: 0.6667rem;
            color: #3d3d3d;
            font-weight: 500;
          }

          .name {
            margin-top: 0.1067rem;
            font-size: 0.32rem;
            font-family: PingFang SC, PingFang SC;
            font-weight: normal;
            color: #999999;
            line-height: 0.4533rem;
            display: flex;
            align-items: center;

            em {
              max-width: 2.6667rem;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }

          .base {
            margin-top: 0.2133rem;
            font-size: 0.3467rem;
            line-height: 0.48rem;
            color: #666;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: normal;
          }
        }
      }

      .operate {
        margin-top: 0.32rem;
        font-size: 0.3733rem;

        .edit {
          width: 0.64rem;
          height: 0.64rem;
          display: block;
          margin-right: 0.1067rem;
        }
      }

      .view {
        padding-bottom: 0.2667rem;

        span {
          color: $color_main;
          border: 0.0267rem solid $color_main;
          border-radius: 0.5333rem;
          padding: 0.1333rem 0.5333rem;
          cursor: pointer;
        }

        .end {
          color: #999;
          border-color: #999;
        }
      }
    }
  }
}

.user_dialog {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.64rem;
  background: url('~@/assets/images/comm_bg.png') no-repeat;
  background-size: cover;
  overflow: hidden;
  border-radius: 0.64rem;

  .head {
    span {
      flex-shrink: 0;
      width: 2.0267rem;
      height: 2.0267rem;
      border-radius: 1.3333rem;
      overflow: hidden;
      border: 0.0533rem solid #fff;
      box-sizing: border-box;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .name {
    margin-top: 0.16rem;

    .unionname {
      font-size: 0.4267rem;
      font-weight: normal;
      color: #000;
      line-height: 0.5867rem;
    }

    .useiid {
      position: relative;
      top: 0.0533rem;
      margin-left: 0.1333rem;
      color: #666;
    }
  }

  .data {
    margin: 0.5333rem auto;

    .item {
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5867rem;

      em {
        color: #31293b;
      }

      span {
        color: $color_main;
        border: 0.0267rem solid $color_main;
        border-radius: 0.16rem;
        padding: 0 0.32rem;
      }
    }
  }

  .event {
    cursor: pointer;
    text-align: center;
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-weight: normal;
    border-radius: 0.5333rem;
    padding: 0.24rem 0;
    margin: 0 0.6133rem;
  }

  .out {
    cursor: pointer;
    text-align: center;
    font-size: 0.4267rem;
    font-weight: normal;
    color: $color_main;
    border: 0.0267rem solid $color_main;
    line-height: 0.5867rem;
    padding: 0.24rem 0;
    margin: 0 0.6133rem;
    border-radius: 0.5333rem;
    margin-top: 0.32rem;
  }
}
</style>

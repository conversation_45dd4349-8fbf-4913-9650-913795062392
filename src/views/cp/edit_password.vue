<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">修改密码</div>
    </div>
    <div class="h44"></div>
    <div class="menu">
      <div class="item">
        <span class="name">新&nbsp;密&nbsp;码</span>
        <div class="event">
          <input type="password" v-model="state.pwd" maxlength="16" placeholder="请设置6-16个字符的密码" />
          <i v-if="state.pwd" @click="state.pwd = ''" class="icon iconfont icon-guanbi1"></i>
        </div>
      </div>
      <div class="item">
        <span class="name">确认密码</span>
        <div class="event">
          <input type="password" v-model="state.confirm_pwd" maxlength="16" placeholder="再次输入密码" />
        </div>
      </div>
    </div>
  </div>
  <div class="btn flex_dc">
    <span @click="send">保存</span>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, reactive } from 'vue'
  import { editPassword } from '@/api/edit.js'

  import { useRouter } from 'vue-router'
  const { proxy } = getCurrentInstance()
  const OEUI = proxy.OEUI

  const router = useRouter()
  const state = reactive({
    pwd: '',
    confirm_pwd: ''
  })
  const is_send = ref(true)
  const send = () => {
    if (!state.pwd) return OEUI.toast({ text: '请输入新密码' })
    if (!state.confirm_pwd) return OEUI.toast({ text: '请再次确认密码' })
    if (state.pwd != state.confirm_pwd) return OEUI.toast({ text: '两次密码输入不一致' })
    if (!is_send.value) return
    is_send.value = false
    editPassword({
      pwd: state.pwd,
      confirm_pwd: state.confirm_pwd
    })
      .then(res => {
        if (res.ret == 1) {
          OEUI.toast({ text: '修改成功' })
          setTimeout(() => {
            router.back()
          }, 500)
        } else {
          OEUI.toast({ text: '保存失败，请检查!' })
        }
        is_send.value = true
      })
      .catch(() => {
        is_send.value = true
      })
  }
</script>

<style lang="scss" scoped>
  .main {
    position: absolute;
    width: 100vw;
    min-height: 100vh;
    background: #f7f8fa;
    .menu {
      padding: 0 0.4267rem;
      .item {
        height: 1.28rem;
        margin: 0.0533rem 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        border-bottom: 0.0267rem solid #f2f3f5;
        .name {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: normal;
          color: #31293b;
          flex-shrink: 0;
          margin-right: 0.5333rem;
        }
        .event {
          flex: 1;
          display: flex;
          align-items: center;
          font-size: 0.32rem;
          line-height: 0.4533rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: normal;
          color: #999999;
          position: relative;
          .icon {
            color: #999;
            cursor: pointer;
          }
        }
      }
    }
  }
  .btn {
    position: fixed;
    bottom: 0.5333rem;
    width: 100%;
    span {
      cursor: pointer;
      background: $color_main;
      color: #fff;
      width: 60vw;
      text-align: center;
      border-radius: 0.5333rem;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      padding: 0.2667rem 0;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
    }
  }
</style>

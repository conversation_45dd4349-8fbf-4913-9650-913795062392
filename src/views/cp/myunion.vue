<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="back" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">名下红娘</div>
  </div>
  <div class="h44"></div>
  <div class="main">
    <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="filter flex_dc">
        <div class="flex_dc" @click="changTime">
          <span>{{ timeList[state.s_time].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changOrderby">
          <span>{{ state.s_orderby == 'id_desc' ? '时间降序' : '时间升序' }}</span>
          <span class="flex_dc flex_v">
            <i :class="state.s_orderby == 'id_desc' ? 'color_c3' : ''" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; transform: rotate(180deg); top: 0.08rem"></i>
            <i :class="state.s_orderby == 'id_desc' ? '' : 'color_c3'" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; bottom: 0.0533rem"></i>
          </span>
        </div>
      </div>
      <div class="search flex flex_ac flex_jsb">
        <i class="iconfont icon-sousuo"></i>
        <input type="text" v-model="state.s_name" class="flex_1" placeholder="请输入昵称或编号" />
        <span class="btn" @click="searchName">搜索</span>
      </div>
      <div class="list" v-if="list.length">
        <template v-for="item in list" :key="item.unionid">
          <div class="item_box">
            <div class="info">
              <div class="head">
                <img v-if="item.headimg" v-lazy="item.headimg_url" type="union" />
                <img v-else v-lazy="item.headimg_url" type="union" />
              </div>
              <div class="content flex_1">
                <div class="name flex flex_ac flex_jsb">
                  <div class="flex flex_ac">
                    <span class="unionname">{{ item.unionname }}</span>
                  </div>
                </div>
                <div class="id flex flex_ac flex_jsb">
                  <span>编号：{{ item.unionid }}</span>
                  <span class="time">加入时间：{{ getTime(item.addtime, true) }}</span>
                </div>
                <div class="count flex flex_ac flex_jsb">
                  <span class="fles_1 flx_s">
                    下属红娘：
                    <em>{{ item.childs }}</em>
                    人
                  </span>
                  <span class="fles_1 flx_s">
                    下属用户：
                    <em>{{ item.users }}</em>
                    人
                  </span>
                </div>
              </div>
            </div>
            <div class="flex_dc view" v-if="config.union1_maker == 1">
              <span @click.stop="viewContact(item.unionid)">查看联系方式</span>
            </div>
          </div>
        </template>
      </div>
    </oeui-list>
  </div>

  <search_item ref="searchTime" type="time" :list="timeList" :current="state.s_time" @changItem="selectTime" @close="closeItem"></search_item>

  <oe_popup ref="user_dialog" width="7.84rem" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="user_dialog" v-if="info.unionid">
      <div class="head flex_dc">
        <span>
          <img v-lazy="info.headimgurl" type="union" />
        </span>
      </div>
      <div class="name flex_dc">
        <span class="unionname">{{ info.unionname }}</span>
        <span class="useiid">(编号:{{ info.unionid }})</span>
      </div>
      <div class="data">
        <div v-if="config.union1_maker_idrz == 1" class="item flex flex_ac flex_jsb">
          <p>
            姓名:
            <em v-if="info.idrz == 1">{{ info.truename || '--' }}</em>
            <em v-else style="color: #666">未实名</em>
          </p>
        </div>
        <div v-if="config.union1_maker_mobile == 1" class="item flex flex_ac flex_jsb" style="margin-top: 0.24rem">
          <p>
            电话:
            <em>{{ info.mobile || '--' }}</em>
          </p>
          <span v-if="info.mobile" @click="copy(info.mobile)">复制</span>
        </div>
        <div v-if="config.union1_maker_weixin == 1" class="item flex flex_ac flex_jsb" style="margin-top: 0.24rem">
          <p>
            微信:
            <em>{{ info.weixin || '--' }}</em>
          </p>
          <span v-if="info.weixin" @click="copy(info.weixin)">复制</span>
        </div>
      </div>
      <div @click="proxy.$refs.user_dialog.close()" class="event">关闭</div>
    </div>
  </oe_popup>
</template>

<script>
export default {
  name: 'Myunion',
}
</script>

<script setup>
import { ref, getCurrentInstance, reactive, onMounted, nextTick, computed } from 'vue'
import oeuiList from '@/oeui/list.vue'
import oe_popup from '@/oeui/popup.vue'
import search_item from '@/components/search_itme.vue'
import { getMyunion, getUnionContact } from '@/api/union.js'
import { getTime } from '@/utils/hooks.js'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
const { proxy } = getCurrentInstance()
const router = useRouter()
const store = useStore()
const OEUI = proxy.OEUI
const config = computed(() => store.state.config)
console.log(store.state);
console.log(config);
const back = () => {
  if (window.history.length <= 1) {
    router.push('/home')
    return
  } else if (!window.history.state.back) {
    router.replace({ path: '/home' })
  } else {
    router.back()
  }
}

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')

const timeList = [
  { name: '全部时间', val: 0 },
  { name: '今天', val: 1 },
  { name: '昨天', val: 2 },
  { name: '本周', val: 3 },
  { name: '上周', val: 4 },
  { name: '本月', val: 5 },
  { name: '上月', val: 6 },
  { name: '今年', val: 7 },
  { name: '去年', val: 8 },
]

const state = reactive({
  s_time: 0,
  s_orderby: 'id_desc',
  s_name: '',
})
const is_time = ref(false)

const getPgroupUrl = (type, val) => {
  return require(`@/assets/images/grade/${type}${val}.png`)
}

onMounted(() => {
  getList()
})

const searchName = () => {
  //if (!state.s_username.length) return
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const closeItem = () => {
  is_time.value = false
}

const changTime = () => {
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}

const selectTime = val => {
  is_time.value = false
  state.s_time = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}
const changOrderby = () => {
  if (state.s_orderby == 'id_desc') {
    state.s_orderby = 'id_asc'
  } else {
    state.s_orderby = 'id_desc'
  }
  page.value = 1
  //list.value = []
  //listStatus.value = 'loading'
  getList(true)
}

const refresh = done => {
  page.value = 1
  listStatus.value = 'loading'
  list.value = []
  getList(true, done)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getMyunion({
    page: page.value,
    ...state,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

const info = ref({})
const is_view = ref(true)
const viewContact = unionid => {
  info.value = {}
  if (!is_view.value) return
  is_view.value = false
  getUnionContact({ unionid })
    .then(res => {
      if (res.ret == 1) {
        info.value = res.result.union_info
        nextTick(() => {
          proxy.$refs.user_dialog.open()
        })
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试',
        })
      }
    })
    .finally(() => {
      setTimeout(() => {
        is_view.value = true
      }, 500)
    })
}
const copy = data => {
  let elInput = document.createElement('input')
  elInput.value = data
  document.body.appendChild(elInput)
  elInput.select()
  document.execCommand('Copy')
  OEUI.toast({
    text: '复制成功',
  })
  elInput.remove()
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}
.font_din {
  font-family: 'cpDin';
}
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.main {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.0533rem 0.4267rem;
  .filter {
    height: 1.0667rem;
    border-radius: 0.32rem;
    background: rgba(255, 255, 255, 0.5);
    div {
      flex: 1;
      color: #2a2546;
      text-align: center;
      span {
        margin-right: 0.1333rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        max-width: 1.8667rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .color_c3 {
        color: #c5c3c7;
      }

      .hide {
        transform: rotate(90deg);
      }
      .show {
        transform: rotate(-90deg);
        position: relative;
        top: 0.0267rem;
      }
    }
  }
  .data {
    margin-top: 0.9067rem;
    div {
      p {
        font-size: 0.64rem;
        font-weight: 700;
        color: #31293b;
        line-height: 0.7733rem;
        @extend .font_din;
      }
      span {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
    }
  }
  .tip {
    margin-top: 0.4267rem;
    border-radius: 0.32rem;
    background: #ececf9;
    padding: 0.32rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.6133rem;
  }
  .search {
    margin-top: 0.3733rem;
    background: #fff;
    padding: 0.1067rem;
    border-radius: 0.32rem;
    i {
      padding-left: 0.1067rem;
      color: #000;
      font-size: 0.48rem;
    }
    input {
      padding: 0 0.2133rem;
    }
    .btn {
      background: #7d68fe;
      color: #fff;
      font-size: 0.3467rem;
      line-height: 0.48rem;
      font-weight: normal;
      padding: 0.1867rem 0.4267rem;
      border-radius: 0.4267rem;
    }
  }
  .list {
    margin-top: 0.3733rem;
    .item_box {
      background: #fff;
      margin-bottom: 0.32rem;
      border-radius: 0.32rem;
      box-shadow: 0px 0.0533rem 0.2133rem 0px rgba(58, 33, 209, 0.12);
      .info {
        display: flex;
        align-items: center;
        padding: 0.32rem;
        &.bob {
          border-bottom: 0.0267rem solid #f2f4f5;
        }
        .head {
          width: 2.0533rem;
          height: 2.0533rem;
          border-radius: 0.32rem;
          overflow: hidden;
          flex-shrink: 0;
          margin-right: 0.2133rem;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .content {
          .name {
            .unionname {
              max-width: 4rem;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              font-size: 0.48rem;
              font-weight: normal;
              color: #3d3d3d;
              line-height: 0.6667rem;
            }
            .group {
              margin-left: 0.2667rem;
            }
            .status {
              font-size: 0.3467rem;
              font-weight: normal;
              color: #15ce7b;
              line-height: 0.48rem;
              &.fail {
                color: #ed1616;
              }
              &.audit {
                color: #999;
              }
            }
          }
          .id {
            margin-top: 0.1067rem;
            font-size: 0.32rem;
            font-weight: normal;
            color: #666666;
            line-height: 0.4533rem;
            .time {
              color: #999999;
            }
          }
          .count {
            margin-top: 0.2133rem;
            font-size: 0.3467rem;
            font-weight: normal;
            color: #666666;
            line-height: 0.48rem;

            span {
              display: flex;
              align-items: center;
              text-align: left;
              flex: 1;
            }
            em {
              max-width: 1.3333rem;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }
        }
      }
      .more {
        color: $color_main;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.3733rem 0.64rem;
        font-size: 0.3733rem;
        font-weight: normal;
        line-height: 0.5333rem;
        i {
          position: relative;
          top: 0.0267rem;
          margin-right: 0.08rem;
        }
      }
    }
    .view {
      padding-bottom: 0.2667rem;

      span {
        color: $color_main;
        border: 0.0267rem solid $color_main;
        border-radius: 0.5333rem;
        padding: 0.1333rem 0.5333rem;
        cursor: pointer;
      }

      .end {
        color: #999;
        border-color: #999;
      }
    }
  }
}
.user_dialog {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.64rem;
  background: url('~@/assets/images/comm_bg.png') no-repeat;
  background-size: cover;
  overflow: hidden;
  border-radius: 0.64rem;

  .head {
    span {
      flex-shrink: 0;
      width: 2.0267rem;
      height: 2.0267rem;
      border-radius: 1.3333rem;
      overflow: hidden;
      border: 0.0533rem solid #fff;
      box-sizing: border-box;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .name {
    margin-top: 0.16rem;

    .unionname {
      font-size: 0.4267rem;
      font-weight: normal;
      color: #000;
      line-height: 0.5867rem;
    }

    .useiid {
      position: relative;
      top: 0.0533rem;
      margin-left: 0.1333rem;
      color: #666;
    }
  }

  .data {
    margin: 0.5333rem auto;

    .item {
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5867rem;

      em {
        color: #31293b;
      }

      span {
        color: $color_main;
        border: 0.0267rem solid $color_main;
        border-radius: 0.16rem;
        padding: 0 0.32rem;
      }
    }
  }

  .event {
    cursor: pointer;
    text-align: center;
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-weight: normal;
    border-radius: 0.5333rem;
    padding: 0.24rem 0;
    margin: 0 0.6133rem;
  }

  .out {
    cursor: pointer;
    text-align: center;
    font-size: 0.4267rem;
    font-weight: normal;
    color: $color_main;
    border: 0.0267rem solid $color_main;
    line-height: 0.5867rem;
    padding: 0.24rem 0;
    margin: 0 0.6133rem;
    border-radius: 0.5333rem;
    margin-top: 0.32rem;
  }
}
</style>

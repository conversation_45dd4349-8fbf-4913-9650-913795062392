<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">联系客服</div>
  </div>
  <div class="h44"></div>
  <div class="kefu_tips">
    <img src="@/assets/images/kefu_tips.png" alt="" />
  </div>
  <div class="data">
    <p>平台客服</p>
    <p>竭诚为您服务</p>
    <p class="time">
      工作时间：
      <em>工作日9:00～18:00</em>
    </p>
  </div>
  <div class="list">
    <template v-for="item in list" :key="item.kefuid">
      <div class="item_box flex flex_ac flex_jsb" v-if="item.kefuid">
        <div class="info flex flex_ac flex_1">
          <div class="head">
            <img v-lazy="item.headimg_url" type="union" alt="" />
          </div>
          <div class="content ws">
            <p class="name">{{ item.title }}</p>
            <p class="ws">微信: {{ item.weixin || '--' }}</p>
          </div>
        </div>
        <span class="btn flex_dc" v-if="item.weixin" @click="copy(item.weixin)">
          <i class="iconfont icon-fuzhi"></i>
          复制
        </span>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, computed } from 'vue'
import { useStore } from 'vuex'
import { getKefu } from '@/api/cp.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()

const config = computed(() => store.state.config)

const list = ref([])
const getList = () => {
  getKefu().then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      list.value = res.result.data
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const copy = data => {
  let elInput = document.createElement('input')
  elInput.value = data
  document.body.appendChild(elInput)
  elInput.select()
  document.execCommand('Copy')
  OEUI.toast({
    text: '复制成功',
  })
  elInput.remove()
}

getList()
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.kefu_tips {
  position: absolute;
  width: 4.8rem;
  height: 4.8rem;
  right: 0;
  top: 0.48rem;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.data {
  position: absolute;
  left: 0.64rem;
  top: 1.8133rem;
  p {
    font-size: 0.6933rem;
    line-height: 0.8rem;
    color: #2a2546;
    font-weight: bold;
  }
  .time {
    margin-top: 0.2133rem;
    font-size: 0.2933rem;
    line-height: 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #999;
    em {
      color: #2a2546;
    }
  }
}

.list {
  font-family: PingFang SC, PingFang SC;
  margin-top: 3.52rem;
  padding: 0 0.4267rem;
  .item_box {
    background: #fff;
    border-radius: 0.32rem;
    margin-bottom: 0.32rem;
    padding: 0.32rem;
    .info {
      flex: 1;
      .head {
        width: 1.7067rem;
        height: 1.7067rem;
        border-radius: 0.2667rem;
        overflow: hidden;
        flex-shrink: 0;
        margin-right: 0.2133rem;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .content {
        flex: 1;
        max-width: 60%;
        p {
          font-size: 0.3733rem;
          line-height: 0.4533rem;
          font-weight: normal;
          color: #666;
        }
        .name {
          font-size: 0.5333rem;
          line-height: 0.5867rem;
          font-weight: 500;
          margin-bottom: 0.2133rem;
          color: $color_main;
        }
      }
    }
    .btn {
      flex-shrink: 0;
      background: $color_main;
      color: #fff;
      font-size: 0.32rem;
      line-height: 0.4267rem;
      border-radius: 0.4267rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: normal;
      padding: 0.2133rem 0.5867rem;
      cursor: pointer;
      i {
        font-size: 0.3733rem;
        position: relative;
        top: 0.0267rem;
        margin-right: 0.1067rem;
      }
    }
  }
}
</style>

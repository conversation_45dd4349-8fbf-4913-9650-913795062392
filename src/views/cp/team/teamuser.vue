<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="back" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">团队用户</div>
  </div>
  <div class="h44"></div>
  <div class="main">
    <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="filter flex_dc">
        <div class="flex_dc" @click="changFlag">
          <span>{{ flagList[state.s_flag].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_flag ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changTime">
          <span>{{ timeList[state.s_time].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
        </div>

        <div class="flex_dc" @click="changOrderby">
          <span>{{ state.s_orderby == 'id_desc' ? '时间降序' : '时间升序' }}</span>
          <span class="flex_dc flex_v">
            <i :class="state.s_orderby == 'id_desc' ? 'color_c3' : ''" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; transform: rotate(180deg); top: 0.08rem"></i>
            <i :class="state.s_orderby == 'id_desc' ? '' : 'color_c3'" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; bottom: 0.0533rem"></i>
          </span>
        </div>
      </div>
      <div class="flex flex_jsb flex_1 data" style="padding: 0 0.4267rem">
        <div class="flex_dc flex_v">
          <p>{{ data.total }}</p>
          <span class="fn_i">总人数</span>
        </div>
        <div class="flex_dc flex_v">
          <p>{{ data.pass_total }}</p>
          <span class="fn_i">有效人数</span>
        </div>
        <div class="flex_dc flex_v">
          <p>{{ data.pass_user1 }}</p>
          <span class="fn_i">男用户</span>
        </div>
        <div class="flex_dc flex_v">
          <p>{{ data.pass_user2 }}</p>
          <span class="fn_i">女用户</span>
        </div>
      </div>
      <div class="search flex flex_ac flex_jsb">
        <i class="iconfont icon-sousuo"></i>
        <input type="text" v-model="state.s_name" class="flex_1" placeholder="请输入用户昵称或编号" />
        <span class="btn" @click="searchName">搜索</span>
      </div>
      <div class="list" v-if="list.length">
        <template v-for="item in list" :key="item.userid">
          <div class="item_box" @click="$router.push('/user/detail?id=' + item.userid)" v-if="item.userid">
            <div class="tit flex flex_ac flex_jsb">
              <div class="time flex flex_ac">
                <i class="iconfont icon-shizhong"></i>
                <p>注册时间：{{ getTime(item.addtime) }}</p>
              </div>
              <div class="status color_green" v-if="item.flag == 1">已通过</div>
              <div class="status color_red" v-else-if="item.flag == 2">未通过</div>
              <div class="status" v-else>待审核</div>
            </div>
            <div class="info_box">
              <div class="head">
                <img v-if="item.headimg" type="user" v-lazy="item.headimg_m1_url" alt="" />
                <template v-else>
                  <img v-if="item.gender == 1" src="@/assets/images/gender_1.png" alt="" />
                  <img v-else-if="item.gender == 2" src="@/assets/images/gender_2.png" alt="" />
                  <img v-else src="@/assets/images/gender_0.png" alt="" />
                </template>
              </div>
              <div class="info">
                <p class="job">
                  {{ item.age }}岁
                  <template v-if="item.job > 0">/{{ item.job_t }}</template>
                </p>
                <p class="name">
                  昵称: &nbsp;
                  <em>{{ item.username }}</em>
                  &emsp; (编号:&nbsp;{{ item.userid }})
                </p>
                <p class="base" v-if="item.marry > 0 || item.education > 0 || item.height > 0">
                  <template v-if="item.marry > 0">{{ item.marry_t }}</template>
                  <template v-if="item.marry > 0">·</template>
                  <template v-if="item.education > 0">{{ item.education_t }}</template>
                  <template v-if="item.education > 0">·</template>
                  <template v-if="item.height > 0">{{ item.height }}cm</template>
                </p>
              </div>
            </div>
            <div class="union flex flex_ac" v-if="item.tgunion && item.tgunion.unionid">
              <span>推广红娘：</span>
              <p>
                <span style="margin-right: 0.1333rem">{{ item.tgunion.unionname }}</span>
                <span>(编号: {{ item.tgunion.unionid }})</span>
              </p>
            </div>
          </div>
        </template>
      </div>
    </oeui-list>
  </div>

  <search_item ref="searchTime" type="time" :list="timeList" :current="state.s_time" @changItem="selectTime" @close="closeItem"></search_item>

  <search_item ref="searchFlag" type="flag" :list="flagList" :current="state.s_flag" @changItem="selectFlag" @close="closeItem"></search_item>
</template>

<script>
export default {
  name: 'Teamuser',
}
</script>

<script setup>
import { ref, getCurrentInstance, reactive, onMounted, nextTick } from 'vue'
import oeuiList from '@/oeui/list.vue'
import search_item from '@/components/search_itme.vue'
import { getTeamUser } from '@/api/team.js'
import { getTime } from '@/utils/hooks.js'
import { useRouter } from 'vue-router'
const { proxy } = getCurrentInstance()
const router = useRouter()

const OEUI = proxy.OEUI

const back = () => {
  if (window.history.length <= 1) {
    router.push('/home')
    return
  } else if (!window.history.state.back) {
    router.replace({ path: '/home' })
  } else {
    router.back()
  }
}

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')

const flagList = [
  { name: '全部状态', val: 0 },
  { name: '已通过', val: 1 },
  { name: '未通过', val: 2 },
  { name: '待审核', val: 3 },
]

const timeList = [
  { name: '全部时间', val: 0 },
  { name: '今天', val: 1 },
  { name: '昨天', val: 2 },
  { name: '本周', val: 3 },
  { name: '上周', val: 4 },
  { name: '本月', val: 5 },
  { name: '上月', val: 6 },
  { name: '今年', val: 7 },
  { name: '去年', val: 8 },
]

const data = reactive({
  pass_total: 0,
  pass_user1: 0,
  pass_user2: 0,
  total: 0,
})

const state = reactive({
  s_time: 0,
  s_orderby: 'id_desc',
  s_name: '',
  s_flag: 0,
})
const is_time = ref(false)
const is_flag = ref(false)

onMounted(() => {
  getList()
})

const searchName = () => {
  //if (!state.s_username.length) return
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const changFlag = () => {
  proxy.$refs.searchTime.close()
  is_time.value = false
  nextTick(() => {
    if (is_flag.value) {
      is_flag.value = false
      proxy.$refs.searchFlag.close()
    } else {
      is_flag.value = true
      proxy.$refs.searchFlag.open()
    }
  })
}

const closeItem = () => {
  is_flag.value = false
  is_time.value = false
}

const selectFlag = val => {
  is_flag.value = false
  state.s_flag = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const changTime = () => {
  proxy.$refs.searchFlag.close()
  is_flag.value = false
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}

const selectTime = val => {
  is_time.value = false
  state.s_time = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}
const changOrderby = () => {
  if (state.s_orderby == 'id_desc') {
    state.s_orderby = 'id_asc'
  } else {
    state.s_orderby = 'id_desc'
  }
  page.value = 1
  //list.value = []
  //listStatus.value = 'loading'
  getList(true)
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getTeamUser({
    page: page.value,
    ...state,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (page.value == 1) {
        data.total = res.result.total || 0
        data.pass_total = res.result.pass_total || 0
        data.pass_user1 = res.result.pass_user1 || 0
        data.pass_user2 = res.result.pass_user2 || 0
      }

      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}

.font_din {
  font-family: 'cpDin';
}

.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}

.main {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.0533rem 0.4267rem;

  .filter {
    height: 1.0667rem;
    border-radius: 0.32rem;
    background: rgba(255, 255, 255, 0.5);

    div {
      flex: 1;
      color: #2a2546;
      text-align: center;

      span {
        margin-right: 0.1333rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        max-width: 1.8667rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .color_c3 {
        color: #c5c3c7;
      }

      .hide {
        transform: rotate(90deg);
      }

      .show {
        transform: rotate(-90deg);
        position: relative;
        top: 0.0267rem;
      }
    }
  }

  .data {
    margin-top: 0.9067rem;

    div {
      p {
        font-size: 0.64rem;
        font-weight: 700;
        color: #31293b;
        line-height: 0.7733rem;
        @extend .font_din;
      }

      span {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
    }
  }

  .tip {
    margin-top: 0.4267rem;
    border-radius: 0.32rem;
    background: #ececf9;
    padding: 0.32rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.6133rem;
  }

  .search {
    margin-top: 0.3733rem;
    background: #fff;
    padding: 0.1067rem 0.2133rem;
    border-radius: 0.32rem;

    i {
      padding-left: 0.1067rem;
      color: #000;
      font-size: 0.48rem;
    }

    input {
      padding: 0 0.2133rem;
    }

    .btn {
      background: #7d68fe;
      color: #fff;
      font-size: 0.3467rem;
      line-height: 0.48rem;
      font-weight: normal;
      padding: 0.1867rem 0.4267rem;
      border-radius: 0.4267rem;
    }
  }

  .list {
    margin-top: 0.3733rem;

    .item_box {
      cursor: pointer;
      background: #fff;
      border-radius: 0.32rem;
      margin-bottom: 0.32rem;

      .tit {
        padding: 0.2667rem 0.32rem 0 0.32rem;
        padding-top: 0.32rem;

        .time {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          color: #999999;

          i {
            position: relative;
            //top: 0.0267rem;
            margin-right: 0.1067rem;
          }
        }

        .status {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          color: #999999;
        }
      }

      .info_box {
        padding: 0.32rem;
        box-sizing: border-box;
        display: flex;
        align-content: center;
        border-bottom: 0.0267rem solid #f1f1f1;

        .head {
          width: 2.0533rem;
          height: 2.0533rem;
          border-radius: 0.2133rem;
          overflow: hidden;
          margin-right: 0.2133rem;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .job {
            font-size: 0.48rem;
            line-height: 0.6667rem;
            color: #3d3d3d;
            font-weight: 500;
          }

          .name {
            margin-top: 0.1067rem;
            font-size: 0.32rem;
            font-family: PingFang SC, PingFang SC;
            font-weight: normal;
            color: #999999;
            line-height: 0.4533rem;
            display: flex;
            align-items: center;

            em {
              max-width: 2.6667rem;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }

          .base {
            margin-top: 0.2133rem;
            font-size: 0.3467rem;
            line-height: 0.48rem;
            color: #666;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: normal;
          }
        }
      }

      .union {
        padding: 0.1333rem 0.32rem;

        p {
          color: #999;
        }
      }
    }
  }
}
</style>

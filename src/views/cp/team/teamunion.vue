<!-- @format -->

src/views/cp/team/teamunion.vue
<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">团队红娘</div>
  </div>
  <div class="h44"></div>
  <div class="main">
    <oeui-list @refresh="refresh" :top="0" :bottom="1.9" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="data flex flex_ac">
        <div class="head">
          <img v-lazy="unionInfo.headimg_url" :key="unionInfo.headimg_url" type="union" alt="" />
        </div>
        <div>
          <p class="name">{{ unionInfo.leader_name }}</p>
          <p>
            您的团队中目前共有推广红娘：
            <em>{{ total }}</em>
            人
          </p>
        </div>
      </div>
      <div class="list">
        <template v-for="item in list" :key="item.unionid">
          <div class="item_box">
            <div class="info" :class="config.union2_viewcontact == 1 ? 'bob' : ''">
              <div class="head">
                <img v-lazy="item.headimg_url" type="union" alt="" />
              </div>
              <div class="content flex_1">
                <div class="name flex flex_ac flex_jsb">
                  <div class="flex flex_ac">
                    <span class="unionname">{{ item.unionname }}</span>
                    <span v-if="item.level1_name" class="group" :class="'group' + item.level1">
                      <i v-if="getPgroupUrl('pgroup', item.level1)">
                        <img :src="getPgroupUrl('pgroup', item.level1)" alt="" />
                      </i>
                      <em>{{ item.level1_name }}</em>
                    </span>
                  </div>
                  <!--<span class="status">正常</span>-->
                </div>
                <div class="id flex flex_ac flex_jsb">
                  <span>编号：{{ item.unionid }}</span>
                  <span class="time">加入时间：{{ item.be_leadtime !== '0' ? getTime(item.be_leadtime, true) : '--' }}</span>
                </div>
                <div class="count flex flex_ac flex_jsb">
                  <span class="fles_1 flx_s">
                    业绩：¥
                    <em>{{ item.total_yeji }}</em>
                  </span>
                  <span class="fles_1 flx_s">
                    有效用户：
                    <em>{{ item.users }}</em>
                    人
                  </span>
                </div>
              </div>
            </div>
            <div v-if="config.union2_viewcontact == 1" class="more" @click="showUnion(item.unionid)">
              <i class="iconfont icon-dianhua2"></i>
              <span>联系TA</span>
            </div>
          </div>
        </template>
      </div>
    </oeui-list>
  </div>
  <oe_popup v-if="state.unionid" ref="union_dialog" width="7.84rem" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="union_dialog">
      <p class="title">红娘信息</p>
      <div class="head flex_dc">
        <span>
          <img v-lazy="state.headimg_url" type="union" alt="" />
        </span>
      </div>
      <div class="name flex_dc">
        <span class="unionname">{{ state.unionname }}</span>
        <span v-if="state.level1_name" class="group" :class="'group' + state.level1">
          <i v-if="getPgroupUrl('pgroup', state.level1)">
            <img :src="getPgroupUrl('pgroup', state.level1)" alt="" />
          </i>
          <em>{{ state.level1_name }}</em>
        </span>
      </div>
      <div class="data">
        <div class="item flex flex_ac flex_jsb">
          <p>
            电话:
            <em>{{ state.mobile || '--' }}</em>
          </p>
          <span v-if="state.mobile" @click="copy(state.mobile)">复制</span>
        </div>
        <div class="item flex flex_ac flex_jsb" style="margin-top: 0.24rem">
          <p>
            微信:
            <em>{{ state.weixin || '--' }}</em>
          </p>
          <span v-if="state.weixin" @click="copy(state.weixin)">复制</span>
        </div>
      </div>
      <div @click="proxy.$refs.union_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>
</template>

<script>
export default {
  name: 'Teamunion',
}
</script>

<script setup>
import { ref, getCurrentInstance, computed, nextTick } from 'vue'
import oeuiList from '@/oeui/list.vue'
import oe_popup from '@/oeui/popup.vue'
import { getTeamUnion, getTeamUnionContact } from '@/api/team.js'
import { getTime } from '@/utils/hooks.js'
import { useStore } from 'vuex'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const unionInfo = computed(() => store.state.unionInfo)
const config = computed(() => store.state.config)

const total = ref(0)
const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')

const getPgroupUrl = (type, val) => {
  if (val == 0) return ''
  return require(`@/assets/images/grade/${type}${val}.png`)
}
const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getTeamUnion({
    page: page.value,
  }).then(res => {
    if (res.ret == 1) {
      total.value = res.result.total
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const state = ref({})
const is_show = ref(true)
const showUnion = id => {
  if (!is_show.value) return
  is_show.value = false
  getTeamUnionContact({ id })
    .then(res => {
      if (res.ret == 1) {
        state.value = res.result.data
        nextTick(() => {
          proxy.$refs.union_dialog.open()
        })
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙，请稍后再试',
        })
      }
      is_show.value = true
    })
    .catch(() => {
      is_show.value = true
      OEUI.toast({
        text: '系统繁忙，请稍后再试',
      })
    })
}

const copy = data => {
  let elInput = document.createElement('input')
  elInput.value = data
  document.body.appendChild(elInput)
  elInput.select()
  document.execCommand('Copy')
  OEUI.toast({
    text: '复制成功',
  })
  elInput.remove()
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}
getList()
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.main {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.2133rem 0.4267rem;
  box-sizing: border-box;
  .data {
    .head {
      width: 1.3867rem;
      height: 1.3867rem;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      margin-right: 0.32rem;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    p {
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5333rem;
      em {
        color: $color_main;
      }
    }
    .name {
      font-size: 0.4267rem;
      font-weight: 500;
      color: #31293b;
      line-height: 0.5867rem;
      margin-bottom: 0.0533rem;
    }
  }
  .list {
    margin-top: 0.64rem;
    .item_box {
      background: #fff;
      margin-bottom: 0.32rem;
      border-radius: 0.32rem;
      box-shadow: 0px 0.0533rem 0.2133rem 0px rgba(58, 33, 209, 0.12);
      .info {
        display: flex;
        align-items: center;
        padding: 0.32rem;
        &.bob {
          border-bottom: 0.0267rem solid #f2f4f5;
        }
        .head {
          width: 2.0533rem;
          height: 2.0533rem;
          border-radius: 0.32rem;
          overflow: hidden;
          flex-shrink: 0;
          margin-right: 0.2133rem;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .content {
          .name {
            .unionname {
              max-width: 2.6667rem;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              font-size: 0.48rem;
              font-weight: normal;
              color: #3d3d3d;
              line-height: 0.6667rem;
            }
            .group {
              margin-left: 0.2667rem;
            }
            .status {
              font-size: 0.3467rem;
              font-weight: normal;
              color: #15ce7b;
              line-height: 0.48rem;
              &.fail {
                color: #ed1616;
              }
              &.audit {
                color: #999;
              }
            }
          }
          .id {
            margin-top: 0.1067rem;
            font-size: 0.32rem;
            font-weight: normal;
            color: #666666;
            line-height: 0.4533rem;
            .time {
              color: #999999;
            }
          }
          .count {
            margin-top: 0.2133rem;
            font-size: 0.3467rem;
            font-weight: normal;
            color: #666666;
            line-height: 0.48rem;

            span {
              display: flex;
              align-items: center;
              text-align: left;
              flex: 1;
            }
            em {
              max-width: 1.3333rem;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }
          }
        }
      }
      .more {
        color: $color_main;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.3733rem 0.64rem;
        font-size: 0.3733rem;
        font-weight: normal;
        line-height: 0.5333rem;
        i {
          position: relative;
          top: 0.0267rem;
          margin-right: 0.08rem;
        }
      }
    }
  }
}
.union_dialog {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.64rem;
  .title {
    text-align: center;
    font-size: 0.4267rem;
    font-weight: 500;
    color: #31293b;
    line-height: 0.5867rem;
  }
  .tips {
    margin-top: 0.64rem;
    text-align: center;
    font-size: 0.4267rem;
    font-weight: normal;
    color: #31293b;
    line-height: 0.5867rem;
    em {
      font-weight: 500;
    }
  }
  .head {
    margin-top: 0.64rem;
    span {
      flex-shrink: 0;
      width: 2.0533rem;
      height: 2.0533rem;
      border-radius: 0.32rem;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  .name {
    margin-top: 0.16rem;
    .unionname {
      font-size: 0.4267rem;
      font-weight: normal;
      color: #3d3d3d;
      line-height: 0.5867rem;
    }
    .group {
      margin-left: 0.2667rem;
    }
  }
  .data {
    margin: 0.5333rem auto;
    .item {
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5867rem;
      em {
        color: #31293b;
      }
      span {
        color: $color_main;
        border: 0.0267rem solid $color_main;
        border-radius: 0.16rem;
        padding: 0 0.32rem;
      }
    }
  }
  .event {
    cursor: pointer;
    text-align: center;
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-weight: normal;
    border-radius: 0.5333rem;
    padding: 0.24rem 0;
    margin: 0 0.6133rem;
  }
  .out {
    cursor: pointer;
    text-align: center;
    font-size: 0.4267rem;
    font-weight: normal;
    color: $color_main;
    border: 0.0267rem solid $color_main;
    line-height: 0.5867rem;
    padding: 0.24rem 0;
    margin: 0 0.6133rem;
    border-radius: 0.5333rem;
    margin-top: 0.32rem;
  }
}
</style>

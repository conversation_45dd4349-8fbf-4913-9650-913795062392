<!-- @format -->

<template>
  <div class="item_box" v-if="item.joinid && item.union">
    <div class="info flex flex_ac bob">
      <div class="head">
        <img v-if="item.union.headimg" v-lazy="item.union.headimg_url" type="union" alt="" />
        <img v-else src="@/assets/images/gender_0.png" alt="" />
      </div>
      <div class="data">
        <div class="flex flex_ac flex_jsb">
          <span class="name">{{ item.union.unionname }}</span>
          <span class="time">{{ getTime(item.addtime, true) }}</span>
        </div>
        <p class="dist">
          {{ config.dist_title || '居住地' }}：
          {{ item.union.area1 > 0?item.union.area1_t:'' }}
          {{ item.union.area2 > 0?item.union.area2_t:'' }}
          {{ item.union.area3 > 0?item.union.area3_t:'' }}
          {{ item.union.area4 > 0?item.union.area4_t:'' }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, computed } from 'vue'
import { getTime } from '@/utils/hooks.js'
import { useStore } from 'vuex'
const { proxy } = getCurrentInstance()
const store = useStore()
const config = computed(() => store.state.config)
defineProps({
  item: {
    type: Object,
    required: true,
  },
})
</script>

<style lang="scss" scoped>
.item_box {
  border-radius: 0.32rem;
  background: #fff;
  box-shadow: 0px 0.0533rem 0.2133rem 0px rgba(58, 33, 209, 0.12);
  margin-bottom: 0.32rem;

  .info {
    padding: 0.32rem;

    &.bob {
      border-bottom: 0.0267rem solid #f2f4f5;
    }

    .head {
      width: 1.3867rem;
      height: 1.3867rem;
      border-radius: 0.32rem;
      flex-shrink: 0;
      margin-right: 0.32rem;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .data {
      flex: 1;

      .name {
        font-size: 0.48rem;
        font-weight: normal;
        color: #31293b;
        line-height: 0.6667rem;
      }

      .time {
        font-size: 0.3467rem;
        font-weight: normal;
        color: #999999;
        line-height: 0.48rem;
      }

      .dist {
        margin-top: 0.0267rem;
        font-size: 0.3467rem;
        font-weight: normal;
        color: #666666;
        line-height: 0.48rem;
      }
    }
  }

  .event {
    padding: 0.2133rem 0.4267rem;

    span {
      flex: 1;
      text-align: center;
      font-size: 0.3733rem;
      font-weight: normal;
      line-height: 0.5333rem;
      padding: 0.16rem 0;
      border-radius: 0.4267rem;
      cursor: pointer;
    }

    .reject {
      margin-right: 0.88rem;
      background: #f2f4f5;
      color: $color_main;
    }

    .agree {
      color: #fff;
      background: $color_main;
    }
  }
}
</style>

<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">团队记录</div>
  </div>
  <div class="h44"></div>
  <div class="main">
    <div class="nav flex flex_ac">
      <span @click="changActive('join')" :class="active == 'join' ? 'current' : ''">入团</span>
      <span @click="changActive('quit')" :class="active == 'quit' ? 'current' : ''">退团</span>
    </div>
    <oeui-list @refresh="refresh" :top="0" :bottom="2.9333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="list">
        <template v-if="active == 'join'">
          <teamjoin :item="item" v-for="item in list" :key="item.joinid" />
        </template>
        <template v-else>
          <teamquit :item="item" v-for="item in list" :key="item.joinid" />
        </template>
      </div>
    </oeui-list>
  </div>
</template>

<script>
export default {
  name: 'Teamlog',
}
</script>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import oeuiList from '@/oeui/list.vue'
import teamjoin from './teamjoin.vue'
import teamquit from './teamquit.vue'

import { joinTeamList, quitTeamList } from '@/api/team.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const active = ref('quit')

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')

const changActive = val => {
  active.value = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}

const activeType = params => {
  if (active.value == 'join') {
    return joinTeamList(params)
  } else {
    return quitTeamList(params)
  }
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  activeType({
    page: page.value,
  }).then(res => {
    console.log(res);

    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}
getList()
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.main {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0 0.4267rem;
  background: #fff;
  border-radius: 0.64rem 0.64rem 0 0;
  min-height: calc(100vh - 1.1733rem);
  box-sizing: border-box;
  overflow: hidden;
  .nav {
    background: #fff;
    position: relative;
    z-index: 10;
    height: 1.1733rem;
    margin-top: 0.32rem;
    span {
      margin-right: 0.64rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.6133rem;
      position: relative;
      top: 0.1333rem;
      &.current {
        font-size: 0.5333rem;
        font-weight: 500;
        color: #2a2546;
        line-height: 0.7467rem;
        top: 0;
        &::after {
          position: absolute;
          content: '';
          width: 0.32rem;
          height: 0.08rem;
          border-radius: 0.0533rem;
          background: $color_main;
          left: 50%;
          bottom: -0.08rem;
          transform: translateX(-50%);
        }
      }
    }
  }
  .list {
    box-sizing: border-box;
    padding: 0.32rem 0;
  }
}
</style>

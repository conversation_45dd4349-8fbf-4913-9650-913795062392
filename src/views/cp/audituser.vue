<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">资料审核</div>
  </div>
  <div class="h44"></div>
  <div class="main">
    <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="filter flex_dc">
        <div class="flex_dc" @click="changSettle">
          <span>{{ state.s_flag == 99 ? '待审核' : settleList[state.s_flag].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_settle ? 'show' : 'hide'"></i>
        </div>

        <div class="flex_dc" @click="changOrderby">
          <span>{{ state.s_orderby == 'datatime_desc' ? '提交时间' : '注册时间' }}</span>
          <span class="flex_dc flex_v">
            <i :class="state.s_orderby == 'datatime_desc' ? 'color_c3' : ''" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; transform: rotate(180deg); top: 0.08rem"></i>
            <i :class="state.s_orderby == 'datatime_desc' ? '' : 'color_c3'" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; bottom: 0.0533rem"></i>
          </span>
        </div>
        <div class="flex_dc" @click="changTime">
          <span>{{ timeList[state.s_time].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
        </div>
      </div>
      <!-- <div class="flex flex_jsb flex_1 data" style="padding: 0 0.4267rem">
        <div class="flex_dc flex_v">
          <p>{{ data.total0 }}</p>
          <span class="fn_i">待审人数</span>
        </div>
        <div class="flex_dc flex_v">
          <p>{{ data.total1 }}</p>
          <span class="fn_i">通过人数</span>
        </div>
        <div class="flex_dc flex_v">
          <p>{{ data.audit_income }}</p>
          <span class="fn_i">奖励金额（元）</span>
        </div>
      </div> -->
      <div class="list">
        <template v-for="item in list" :key="item.userid">
          <list_item @show="showFailAudit" @audit="selectAudit" :item="item" />
        </template>
      </div>
    </oeui-list>
  </div>

  <search_item ref="searchSettle" type="settle" :list="settleList" :current="state.s_flag" @changItem="selectSettle" @close="closeItem"></search_item>

  <search_item ref="searchTime" type="time" :list="timeList" :current="state.s_time" @changItem="selectTime" @close="closeItem"></search_item>

  <!--审核-->
  <oe_popup ref="audit_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="audit_dialog">
      <p class="title">资料审核</p>
      <p class="tip">请如实审核用户资料，通过用户填写的 信息是否真实，完善度是否达到要求。 该操作不可逆，请谨慎操作。</p>
      <div class="btn flex_dc">
        <span @click="failAuditEvent" class="fail">不通过</span>
        <span @click="sendAuditPass">通过</span>
      </div>
      <span @click="proxy.$refs.audit_dialog.close()" class="close iconfont icon-guanbi2"></span>
    </div>
  </oe_popup>

  <oe_popup ref="auditResult_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">不奖励原因</p>
      <p class="tip">{{ fail_result }}</p>
      <div @click="proxy.$refs.auditResult_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <oe_popup ref="auditFail_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="auditFail_dialog">
      <p class="title">审核不通过原因</p>
      <div class="content">
        <textarea v-model="fail_remark" maxlength="200" placeholder="请输入审核不通过的原因"></textarea>
        <span class="nums">{{ fail_remark.length }}/200</span>
      </div>
      <div class="btn flex_dc">
        <span @click="sendAuditFail(false)" class="fail">不填</span>
        <span @click="sendAuditFail(true)">确定</span>
      </div>
      <span
        @click="
          proxy.$refs.auditFail_dialog.close(() => {
            fail_remark = ''
          })
        "
        class="close iconfont icon-guanbi2"
      ></span>
    </div>
  </oe_popup>
</template>

<script>
export default {
  name: 'MyAudit',
}
</script>

<script setup>
import { ref, getCurrentInstance, reactive, onMounted, nextTick } from 'vue'
import oeuiList from '@/oeui/list.vue'
import search_item from '@/components/search_itme.vue'
import list_item from '@/views/index/components/audit_item.vue'
import oe_popup from '@/oeui/popup.vue'
import { getAuditUser, auditPass, auditFail } from '@/api/audit_user.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')

const timeList = [
  { name: '全部时间', val: 0 },
  { name: '今天', val: 1 },
  { name: '昨天', val: 2 },
  { name: '本周', val: 3 },
  { name: '上周', val: 4 },
  { name: '本月', val: 5 },
  { name: '上月', val: 6 },
  { name: '今年', val: 7 },
  { name: '去年', val: 8 },
]

const settleList = [
  { name: '全部状态', val: 0 },
  { name: '已审核', val: 1 },
  { name: '待审核', val: 99 },
]

const data = reactive({
  total0: 0,
  total1: 0,
  audit_income: 0,
})

const state = reactive({
  s_time: 0,
  s_orderby: 'datatime_desc',
  s_flag: 0,
})
const is_time = ref(false)
const is_settle = ref(false)

onMounted(() => {
  getList()
})

const changSettle = () => {
  proxy.$refs.searchTime.close()
  is_time.value = false
  nextTick(() => {
    if (is_settle.value) {
      is_settle.value = false
      proxy.$refs.searchSettle.close()
    } else {
      is_settle.value = true
      proxy.$refs.searchSettle.open()
    }
  })
}
const changOrderby = () => {
  if (state.s_orderby == 'datatime_desc') {
    state.s_orderby = 'id_desc'
  } else {
    state.s_orderby = 'datatime_desc'
  }
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}
const changTime = () => {
  proxy.$refs.searchSettle.close()
  is_settle.value = false
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}

const selectSettle = val => {
  is_settle.value = false
  state.s_flag = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const selectTime = val => {
  is_time.value = false
  state.s_time = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const closeItem = () => {
  is_time.value = false
  is_settle.value = false
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getAuditUser({
    page: page.value,
    ...state,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (page.value == 1) {
        data.total0 = res.result.total0
        data.total1 = res.result.total1
        data.audit_income = res.result.audit_income
      }

      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

//审核操作
const auditid = ref(null)
const fail_remark = ref('')
const is_send = ref(true)
const fail_result = ref('')

const selectAudit = id => {
  auditid.value = id
  proxy.$refs.audit_dialog.open()
}
const failAuditEvent = () => {
  proxy.$refs.audit_dialog.close(() => {
    proxy.$refs.auditFail_dialog.open()
  })
}
const sendAuditPass = () => {
  if (!is_send.value) return
  is_send.value = false
  auditPass({
    id: auditid.value,
  }).then(res => {
    if (res.ret == 1) {
      proxy.$refs.audit_dialog.close(() => {
        OEUI.toast({
          text: '操作成功',
        })
        auditid.value = null
        page.value = 1
        getList(true)
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}
const sendAuditFail = flag => {
  if (flag && !fail_remark.value.length) {
    return OEUI.toast({
      text: '请填写审核不通过的原因',
    })
  }
  if (!is_send.value) return
  is_send.value = false
  auditFail({
    id: auditid.value,
    remark: fail_remark.value,
  }).then(res => {
    if (res.ret == 1) {
      proxy.$refs.auditFail_dialog.close(() => {
        OEUI.toast({
          text: '操作成功',
        })
        auditid.value = null
        fail_remark.value = ''
        page.value = 1
        getList(true)
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}
const showFailAudit = val => {
  fail_result.value = val
  proxy.$refs.afterFail_dialog.open()
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}
.font_din {
  font-family: 'cpDin';
}
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.main {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.0533rem 0.4267rem;
  .filter {
    height: 1.0667rem;
    border-radius: 0.32rem;
    background: rgba(255, 255, 255, 0.5);
    div {
      flex: 1;
      color: #2a2546;
      text-align: center;
      span {
        margin-right: 0.1333rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }
      .color_c3 {
        color: #c5c3c7;
      }

      .hide {
        transform: rotate(90deg);
      }
      .show {
        transform: rotate(-90deg);
        position: relative;
        top: 0.0267rem;
      }
    }
  }
  .data {
    margin-top: 0.9067rem;
    div {
      p {
        font-size: 0.64rem;
        font-weight: 700;
        color: #31293b;
        line-height: 0.7733rem;
        @extend .font_din;
      }
      span {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
    }
  }

  .list {
    margin-top: 0.64rem;
    .item_box {
      cursor: pointer;
      background: #fff;
      border-radius: 0.32rem;
      margin-bottom: 0.32rem;
      .tit {
        padding-top: 0.2667rem;
        padding: 0.2667rem 0.32rem 0 0.32rem;
        .time {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          color: #999999;
          i {
            position: relative;
            top: 0.0267rem;
            margin-right: 0.1067rem;
          }
        }
        .status {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          color: #999999;
        }
      }
      .info {
        border-bottom: 0.0267rem solid #f4f6f7;
        padding: 0.32rem;
        .head {
          width: 1.1733rem;
          height: 1.1733rem;
          border-radius: 0.2133rem;
          overflow: hidden;
          margin-right: 0.32rem;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .name {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-weight: 500;
          color: #31293b;
        }
        i {
          position: relative;
          top: 0.0267rem;
          margin-left: 0.1333rem;
        }
        .icon-nanbiao-01 {
          color: #0570f1;
        }
        .icon-nvbiao-01 {
          color: #fe6897;
        }
        .money {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-weight: 500;
          color: #7d68fe;
        }
      }
    }
  }
}
.audit_dialog {
  padding: 0.64rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;
  .title {
    font-size: 0.4267rem;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    text-align: center;
  }
  .tip {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.5867rem;
  }
  .btn {
    margin-top: 0.5333rem;
    span {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.4267rem;
      font-weight: normal;
      line-height: 0.5867rem;
      padding: 0.24rem 0;
      cursor: pointer;
      background: $color_main;
      border-radius: 0.5333rem;
      color: #fff;
    }
    .fail {
      margin-right: 0.5867rem;
      color: $color_main;
      background: rgba(125, 104, 254, 0.24);
    }
  }
  .close {
    position: absolute;
    right: 0.4rem;
    top: 0.2667rem;
    font-size: 0.48rem;
  }
}
.auditFail_dialog {
  padding: 0.4267rem 0.64rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;
  .title {
    font-size: 0.4267rem;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    text-align: center;
  }
  .content {
    margin-top: 0.32rem;
    background: #f2f4f5;
    height: 2.8rem;
    border-radius: 0.32rem;
    padding: 0.32rem;
    box-sizing: border-box;
    position: relative;
    textarea {
      resize: none;
      border: none;
      width: 100%;
      height: 100%;
      background: none;
      &::placeholder {
        font-size: 0.3733rem;
        font-weight: normal;
        color: #c5c3c7;
        line-height: 0.5333rem;
      }
    }
    .nums {
      position: absolute;
      font-size: 0.32rem;
      font-weight: normal;
      color: #c5c3c7;
      line-height: 0.4533rem;
      right: 0.4rem;
      bottom: 0.0533rem;
    }
  }
  .btn {
    margin-top: 0.4rem;
    padding: 0 0.2133rem;
    span {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.4267rem;
      font-weight: normal;
      line-height: 0.5867rem;
      padding: 0.24rem 0;
      cursor: pointer;
      background: $color_main;
      border-radius: 0.5333rem;
      color: #fff;
    }
    .fail {
      margin-right: 0.5867rem;
      color: $color_main;
      background: rgba(125, 104, 254, 0.24);
    }
  }
  .close {
    position: absolute;
    right: 0.4rem;
    top: 0.2667rem;
    font-size: 0.48rem;
  }
}
</style>

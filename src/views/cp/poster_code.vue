<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">我的推广码</div>
  </div>
  <div class="h44"></div>
  <div class="main">
    <div class="code">
      <img v-if="local_img" :src="local_img" alt="">
    </div>
    <div class="form">
      <p style="margin-bottom: .2667rem;">推广码可用于制作图片、海报等多种方式</p>
      <p class="fb">邀请对象</p>
      <div class="radio">
        <span @click="changTgType('user')">
          <i class="iconfont" :class="s_tgtype == 'user' ? 'icon-redio_checked' : 'icon-rediobtn_nor'"></i>
          单身用户
        </span>
        <span @click="changTgType('union')">
          <i class="iconfont" :class="s_tgtype == 'union' ? 'icon-redio_checked' : 'icon-rediobtn_nor'"></i>
          红娘
        </span>
      </div>
      <p class="fb" style="margin-top: .1333rem;">扫码前往页面</p>
      <div class="radio">
        <span @click="changValidType('h5')">
          <i class="iconfont" :class="s_valid == 'h5' ? 'icon-redio_checked' : 'icon-rediobtn_nor'"></i>
          H5链接
        </span>
        <span @click="changValidType('wap')">
          <i class="iconfont" :class="s_valid == 'wap' ? 'icon-redio_checked' : 'icon-rediobtn_nor'"></i>
          公众号
        </span>
      </div>

      <div class="flex_dc btn" @click="download">
        <i class="iconfont icon-xiazai"></i>
        <span>确定生成并保存至相册</span>
      </div>
      <p class="flex_dc" style="color: #999;margin-top: .2667rem;">或长按二维码保存到相册</p>
    </div>

  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, nextTick } from "vue"
import { getPosterQrCode } from '@/api/cp'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const s_tgtype = ref('user')
const s_valid = ref('wap')
const local_img = ref('')

const changTgType = (val) => {
  s_tgtype.value = val
  nextTick(() => {
    getQrcode()
  })
}
const changValidType = (val) => {
  s_valid.value = val
  nextTick(() => {
    getQrcode()
  })
}


const getQrcode = () => {

  let type = ''
  if (s_valid.value == 'wap') {
    type = 'qrcode'
  } else {
    type = 'h5qrcode'
  }
  getPosterQrCode({
    a: type,
    tgtype: s_tgtype.value
  }).then((res) => {
    if (res.ret == 1) {
      local_img.value = res.result.local_img_url
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试'
      })
    }

  }).catch(() => {
    OEUI.toast({
      text: '系统繁忙，请稍后再试'
    })
  })

}

const download = () => {
  if (!local_img.value) return
  savePicture(local_img.value)
}

const savePicture = (Url) => {
  var blob = new Blob([''], { type: 'application/octet-stream' });
  var url = URL.createObjectURL(blob);
  var a = document.createElement('a');
  a.href = Url;
  a.download = Url.replace(/(.*\/)*([^.]+.*)/ig, "$2").split("?")[0];
  var e = document.createEvent('MouseEvents');
  e.initMouseEvent('click', false, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
  a.dispatchEvent(e);
  URL.revokeObjectURL(url);
}




onMounted(() => {
  getQrcode()
})

</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}

.font_din {
  font-family: 'cpDin';
}

.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}

.main {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.0533rem 0.4267rem;

  .code {
    width: 6.9333rem;
    height: 6.9333rem;
    background: #fff;
    margin: .4rem auto;
    border-radius: .32rem;
    padding: .4rem;
    box-sizing: border-box;

    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
  }

  .form {
    width: 6.9333rem;
    background: #fff;
    margin: .4rem auto;
    border-radius: .32rem;
    padding: .4rem;
    box-sizing: border-box;

    .radio {
      margin-top: 0.2133rem;

      span {
        margin-right: 0.8533rem;
        font-size: 0.3733rem;
        font-weight: normal;
        color: #666666;
        line-height: 0.5333rem;

        i {
          position: relative;
          top: 0.0533rem;
          color: #999999;
          font-size: 0.48rem;
        }

        .icon-redio_checked {
          color: $color_main;
        }
      }
    }

    .btn {
      background: $color_main;
      color: #fff;
      border-radius: .1067rem;
      padding: .2133rem 0;
      margin-top: .8rem;
      cursor: pointer;

      .iconfont {
        margin-right: .1333rem;
      }
    }
  }
}
</style>
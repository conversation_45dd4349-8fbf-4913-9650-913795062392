<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">编辑资料</div>
    </div>
    <div class="h44"></div>
    <div class="menu">
      <div class="item" @click="openHeadimgUp('headimg')">
        <span class="name">头像</span>
        <div class="event">
          <span class="head">
            <img v-if="info.headimg_url" :src="info.headimg_url" alt="" />
            <img v-else src="@/assets/images/gender_0.png" alt="" />
          </span>
          <i class="iconfont icon-you" style="top: 0.08rem"></i>
        </div>
      </div>
      <div class="item" @click="openItemPicker('unionname')">
        <span class="name">昵称</span>
        <div class="event">
          <span class="value" v-if="info.unionname">{{ info.unionname }}</span>
          <span v-else>请填写</span>
          <i class="iconfont icon-you"></i>
        </div>
      </div>
      <div class="item" v-if="unionInfo.idrz == 1">
        <span class="name">实名</span>
        <div class="event">
          <span class="value">已实名</span>
        </div>
      </div>
      <div class="item" @click="goIdrz" v-else>
        <span class="name">实名</span>
        <div class="event">
          <span class="value">未实名</span>
          <i class="iconfont icon-you"></i>
        </div>
      </div>
      <div class="item">
        <span class="name">手机</span>
        <div class="event">
          <span class="value" v-if="info.mobile">{{ info.mobile }}</span>
          <span v-else>未绑定</span>
          <i class="iconfont icon-you" v-if="!info.mobile"></i>
        </div>
      </div>
      <div class="item" @click="openItemPicker('weixin')">
        <span class="name">微信</span>
        <div class="event">
          <span class="value" v-if="info.weixin">{{ info.weixin }}</span>
          <span v-else>修改</span>
          <i class="iconfont icon-you"></i>
        </div>
      </div>

      <div class="item" @click="openHeadimgUp('weixincode')">
        <span class="name">微信二维码</span>
        <div class="event">
          <span class="head">
            <img v-if="info.wxcode_url" :src="info.wxcode_url" alt="" />
            <img v-else src="@/assets/images/gender_0.png" alt="" />
          </span>
          <i class="iconfont icon-you" style="top: 0.08rem"></i>
        </div>
      </div>

      <div class="item" @click="$router.push('/edit/password')">
        <span class="name">密码</span>
        <div class="event">
          <span>修改</span>
          <i class="iconfont icon-you"></i>
        </div>
      </div>
      <div class="item" @click="openItemPicker('area')">
        <span class="name">{{ config.dist_title || '居住地' }}</span>
        <div class="event">
          <span class="value" v-if="info.area1 > 0">
            {{ info.area1_t }}
            {{ info.area2_t }}
            {{ info.area3_t }}
            {{ info.area4_t }}
          </span>
          <span v-else>修改</span>
          <i class="iconfont icon-you"></i>
        </div>
      </div>
      <div class="item">
        <span class="name">注册时间</span>
        <div class="event">
          <span class="value" v-if="unionInfo.addtime > 0">{{ getTime(unionInfo.addtime || 0) }}</span>
          <!--<i class="iconfont icon-you"></i>-->
        </div>
      </div>
    </div>
    <div class="login_out flex_dc">
      <span @click="btnLoginOut">退出登录</span>
    </div>
  </div>

  <edit-text ref="unionnameEdit" title="昵称修改" inputText="请输入昵称" @next="sendNameEdit"
    :current="info.unionname"></edit-text>

  <edit-text ref="weixinEdit" title="微信" inputText="请输入你的微信号" @next="sendWeixin" :current="weixin" :length="20">
    <div class="flex_dc weixin_slot" @click="change_ismobile">
      <i v-if="is_mobile" class="iconfont icon-redio_checked current"></i>
      <i v-else class="iconfont icon-rediobtn_nor"></i>
      <span>与手机同号</span>
    </div>
  </edit-text>

  <oeui-area ref="areaEdit" :title="config.dist_title || '居住地'" :list="listState.areaList"
    :current="[info.area1, info.area2, info.area3, info.area4]" v-if="listState.areaList.length > 0" @change="sendArea">
  </oeui-area>
  <upload-headimg ref="uploadBox" @callback="uploadHead"></upload-headimg>
</template>

<script setup>
import { ref, getCurrentInstance, reactive, nextTick, watch, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import oeuiArea from '@/oeui/area.vue'
import editText from '@/components/edit_text.vue'
import { getTime } from '@/utils/hooks.js'
import uploadHeadimg from '@/components/upload_headimg.vue'
import { getDistPicker } from '@/utils/main.js'
import { uploadImage, editAvatar, editUsername, editWeixin, editWinxinCode, editArea } from '@/api/edit.js'
import { loginOut } from '@/api/login.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const router = useRouter()
const unionInfo = ref({})
const config = computed(() => store.state.config)

const listState = reactive({
  areaList: []
})

const info = ref({})
const weixin = ref('')

const dataList = ['headimg_url', 'unionname', 'mobile', 'weixin', 'wxcode_url', 'area1', 'area1_t', 'area2', 'area2_t', 'area3', 'area3_t', 'area4', 'area4_t']

//退出登录
const is_out = ref(true)
const btnLoginOut = () => {
  if (!is_out.value) return
  is_out.value = false
  loginOut().then(res => {
    if (res.ret == 1) {
      localStorage.removeItem('userSign')
      sessionStorage.removeItem('regTcpAgree')
      router.replace({ path: '/reg' })
    }
    is_out.value = true
  })
}

const getDistData = () => {
  //获取地区数据
  getDistPicker().then(res => {
    if (res.ret == 1) {
      listState.areaList = res.result
      nextTick(() => {
        proxy.$refs['areaEdit'].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
}

const openItemPicker = item => {
  if (item == 'area') {
    return getDistData()
  } else if (item == 'head') {
    proxy.$refs.poppicker.open()
  }
  proxy.$refs[item + 'Edit'].open()
}

const sendArea = obj => {
  editArea({
    area1: (obj[0] && obj[0].value) || 0,
    area2: (obj[1] && obj[1].value) || 0,
    area3: (obj[2] && obj[2].value) || 0,
    area4: (obj[3] && obj[3].value) || 0
  }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
      store.commit('setUnionInfo', res.result.info)
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}

const is_mobile = ref(false)
const change_ismobile = () => {
  is_mobile.value = !is_mobile.value
  if (is_mobile.value) {
    weixin.value = info.value.mobile
  } else {
    weixin.value = ''
  }
}

const sendWeixin = val => {
  editWeixin({ weixin: val }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
      store.commit('setUnionInfo', res.result.info)
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}

const sendNameEdit = val => {
  editUsername({ unionname: val }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
      store.commit('setUnionInfo', res.result.info)
    } else {
      OEUI.toast({ text: '保存失败，请检查!' })
    }
  })
}

// headimg-头像  weixincode-微信二维码
const upload_type = ref('headimg')

const openHeadimgUp = val => {
  upload_type.value = val
  //打开图片上传
  if (val == 'weixincode') {
    proxy.$refs.uploadBox.open(true)
  } else {
    proxy.$refs.uploadBox.open()
  }


}
const uploadHead = obj => {
  if (!obj.src) return OEUI.toast({ text: '图片获取失败，请检查!' })
  if (upload_type.value == 'headimg') {
    sendHeadimg(obj)
  } else {
    sendWeinxinCode(obj)
  }
}

//保存头像
const sendHeadimg = obj => {
  try {
    uploadImage({
      base64img: obj.src,
      module: 'union',
      thumb: 1
    }).then(data => {
      if (data.ret == 1) {
        editAvatar({
          headimg: data.result.drawimg
        }).then(res => {
          if (res.ret == 1) {
            OEUI.toast({ text: '保存成功' })
            store.commit('setUnionInfo', res.result.info)
          } else {
            OEUI.toast({ text: '保存失败，请检查!' })
          }
        })
      } else {
        OEUI.toast({ text: '图片上传失败，请检查!' })
      }
    })
  } catch (error) {
    OEUI.toast({ text: '系统繁忙，请检查!' })
  }
}
//保存微信二维码
const sendWeinxinCode = obj => {
  try {
    uploadImage({
      base64img: obj.src,
      module: 'union',
      thumb: 1
    }).then(data => {
      if (data.ret == 1) {
        editWinxinCode({
          wxcode: data.result.drawimg
        }).then(res => {
          if (res.ret == 1) {
            OEUI.toast({ text: '保存成功' })
            store.commit('setUnionInfo', res.result.info)
          } else {
            OEUI.toast({ text: '保存失败，请检查!' })
          }
        })
      } else {
        OEUI.toast({ text: '图片上传失败，请检查!' })
      }
    })
  } catch (error) {
    OEUI.toast({ text: '系统繁忙，请检查!' })
  }
}

//去实名
const goIdrz = () => {
  if (config.value.mprz_type == 1) {
    // 三要素
    router.push('/idrz')
  } else {
    // 人工核验
    router.push('/idrz2')
  }
}

watch(
  () => store.state.unionInfo,
  newInfo => {
    if (newInfo) {
      unionInfo.value = newInfo
      if (newInfo.weixin) {
        weixin.value = newInfo.weixin
        nextTick(() => {
          is_mobile.value = false
        })
      } else {
        weixin.value = newInfo.mobile
        nextTick(() => {
          is_mobile.value = true
        })
      }
      dataList.forEach(v => {
        info.value[v] = newInfo[v] || ''
      })
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.main {
  position: absolute;
  width: 100vw;
  min-height: 100vh;
  background: #f7f8fa;

  .menu {
    padding: 0 0.4267rem;

    .item {
      height: 1.28rem;
      margin: 0.0533rem 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .name {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #31293b;
      }

      .event {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.3733rem;
        line-height: 0.4533rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #999999;

        .head {
          width: 0.64rem;
          height: 0.64rem;
          border-radius: 50%;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        //.value {
        //  color: #2a2546;
        //}
        i {
          font-size: 0.32rem;
          position: relative;
          top: 0.0267rem;
          margin-left: 0.1067rem;
          color: #666666;
        }
      }
    }
  }
}

.login_out {
  position: fixed;
  width: 100%;
  bottom: 0.8rem;
  font-size: 0.4267rem;
  cursor: pointer;

  span {
    color: #31293b;
    background: #fff;
    padding: 0.2133rem 0.64rem;
    border-radius: 0.4267rem;
    cursor: pointer;
  }
}

.weixin_slot {
  i {
    font-size: 0.5333rem;
    margin-right: 0.08rem;
    color: #999;

    &.current {
      color: $color_main;
    }
  }

  span {
    font-size: 0.3733rem;
    font-weight: normal;
    line-height: 0.5333rem;
    color: #666;
  }
}
</style>

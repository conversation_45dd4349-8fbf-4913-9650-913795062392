<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">消费分成</div>
  </div>
  <div class="h44"></div>
  <div class="main">
    <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="filter flex_dc">
        <div class="flex_dc" @click="changType" v-if="typeList.length">
          <span>{{ typeList.find(v => v.val == state.s_mdtype).name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_type ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changOrderby">
          <span>{{ state.s_orderby == 'id_desc' ? '时间降序' : '时间升序' }}</span>
          <span class="flex_dc flex_v">
            <i :class="state.s_orderby == 'id_desc' ? 'color_c3' : ''" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; transform: rotate(180deg); top: 0.08rem"></i>
            <i :class="state.s_orderby == 'id_desc' ? '' : 'color_c3'" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; bottom: 0.0533rem"></i>
          </span>
        </div>
        <div class="flex_dc" @click="changTime">
          <span>{{ timeList[state.s_time].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
        </div>
      </div>
      <div class="flex flex_jsb flex_1 data" style="padding: 0 0.4267rem">
        <div class="flex_dc flex_v flex_1">
          <p>{{ data.user_total }}</p>
          <span class="fn_i">消费用户(人)</span>
        </div>
        <div class="flex_dc flex_v flex_1">
          <p>{{ Number(data.income_amount) >= 10000 ? (Number(data.income_amount) / 10000).toFixed(2) : data.income_amount }}</p>
          <span class="fn_i">分成金额({{ Number(data.income_amount) >= 10000 ? '万' : '元' }})</span>
        </div>
      </div>
      <div class="search flex flex_ac flex_jsb">
        <i class="iconfont icon-sousuo"></i>
        <input type="text" v-model="state.s_name" class="flex_1" placeholder="请输入用户昵称或编号" />
        <span class="btn" @click="searchName">搜索</span>
      </div>
      <div class="list">
        <list_item :item="item" v-for="item in list" :key="item.userid" />
      </div>
    </oeui-list>
  </div>

  <search_item ref="searchTime" type="time" :list="timeList" :current="state.s_time" @changItem="selectTime" @close="closeItem"></search_item>
  <search_item ref="searchType" type="type" :list="typeList" :current="state.s_mdtype" @changItem="selectType" @close="closeItem"></search_item>
</template>

<script>
export default {
  name: 'Rwbuy',
}
</script>

<script setup>
import { ref, getCurrentInstance, reactive, onMounted, nextTick } from 'vue'
import oeuiList from '@/oeui/list.vue'
import search_item from '@/components/search_itme.vue'
import list_item from '@/views/income/components/rwbuy_item.vue'
import { getRwbuy } from '@/api/income.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')

const timeList = [
  { name: '全部时间', val: 0 },
  { name: '今天', val: 1 },
  { name: '昨天', val: 2 },
  { name: '本周', val: 3 },
  { name: '上周', val: 4 },
  { name: '本月', val: 5 },
  { name: '上月', val: 6 },
  { name: '今年', val: 7 },
  { name: '去年', val: 8 },
]
const typeList = ref([])

const data = reactive({
  user_total: 0,
  income_amount: 0,
})

const state = reactive({
  s_time: 0,
  s_orderby: 'id_desc',
  s_mdtype: '',
  s_name: '',
})
const is_time = ref(false)
const is_type = ref(false)
onMounted(() => {
  getList()
})

const changTime = () => {
  proxy.$refs.searchType.close()
  is_type.value = false
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}

const closeItem = () => {
  is_time.value = false
  is_type.value = false
}

const selectTime = val => {
  is_time.value = false
  state.s_time = val
  page.value = 1
  getList(true)
}

const changType = () => {
  proxy.$refs.searchTime.close()
  is_time.value = false
  nextTick(() => {
    if (is_type.value) {
      is_type.value = false
      proxy.$refs.searchType.close()
    } else {
      is_type.value = true
      proxy.$refs.searchType.open()
    }
  })
}

const selectType = val => {
  is_type.value = false
  state.s_mdtype = val
  page.value = 1
  getList(true)
}

const changOrderby = () => {
  if (state.s_orderby == 'id_desc') {
    state.s_orderby = 'id_asc'
  } else {
    state.s_orderby = 'id_desc'
  }
  page.value = 1
  getList(true)
}

const searchName = () => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getRwbuy({
    page: page.value,
    ...state,
  }).then(res => {
    if (res.ret == 1) {
      if (page.value == 1) {
        const arr = []
        res.result.typelist.forEach((v, i) => {
          arr.push({
            name: v.text,
            val: v.mark,
          })
        })
        typeList.value = arr
        typeList.value.unshift({ name: '全部类型', val: '' })
      }
      res.result.data = res.result.data ? res.result.data : []
      if (page.value == 1) {
        data.user_total = res.result.user_total
        data.income_amount = res.result.income_amount
      }
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}
.font_din {
  font-family: 'cpDin';
}
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.main {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.0533rem 0.4267rem;
  .filter {
    height: 1.0667rem;
    border-radius: 0.32rem;
    background: rgba(255, 255, 255, 0.5);
    div {
      flex: 1;
      color: #2a2546;
      text-align: center;
      span {
        margin-right: 0.1333rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        max-width: 1.8667rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .color_c3 {
        color: #c5c3c7;
      }

      .hide {
        transform: rotate(90deg);
      }
      .show {
        transform: rotate(-90deg);
        position: relative;
        top: 0.0267rem;
      }
    }
  }
  .data {
    margin-top: 0.9067rem;
    div {
      p {
        font-size: 0.64rem;
        font-weight: 700;
        color: #31293b;
        line-height: 0.7733rem;
        @extend .font_din;
      }
      span {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
    }
  }
  .search {
    margin-top: 0.3733rem;
    background: #fff;
    padding: 0.1067rem;
    border-radius: 0.32rem;
    i {
      padding-left: 0.1067rem;
      color: #000;
      font-size: 0.48rem;
    }
    input {
      padding: 0 0.2133rem;
    }
    .btn {
      background: #7d68fe;
      color: #fff;
      font-size: 0.3467rem;
      line-height: 0.48rem;
      font-weight: normal;
      padding: 0.1867rem 0.4267rem;
      border-radius: 0.4267rem;
    }
  }
  .list {
    margin-top: 0.3733rem;
  }
}
</style>

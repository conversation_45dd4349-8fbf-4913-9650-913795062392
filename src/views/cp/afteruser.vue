<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">红娘帮约</div>
  </div>
  <div class="h44"></div>
  <div class="main">
    <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="filter flex_dc">
        <div class="flex_dc" @click="changFlag">
          <span>{{ state.s_flag == 99 ? '待帮约' : flagList[state.s_flag].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_flag ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changOrderby">
          <span>{{ orderbyList.find(v => v.val == state.s_orderby).name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_orderby ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changTime">
          <span>{{ timeList[state.s_time].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
        </div>
      </div>
      <!-- <div class="flex flex_el flex_1 data" style="padding: 0 0.4267rem">
        <div class="flex_dc flex_v flex_1">
          <p>{{ data.total2 }}</p>
          <span class="fn_i">帮约成功(人)</span>
        </div>
        <div class="flex_dc flex_v flex_1">
          <p>{{ data.after_income }}</p>
          <span class="fn_i">帮约收益(元)</span>
        </div>
      </div> -->
      <div class="list">
        <template v-for="item in list" :key="item.userid">
          <list_item @start="afterStartEvent" @show="showFailAfter" @after="AfterEvent" :item="item" />
        </template>
      </div>
    </oeui-list>
  </div>

  <search_item ref="searchFlag" type="flag" :list="flagList" :current="state.s_flag" @changItem="selectFlag" @close="closeItem"></search_item>

  <search_item ref="searchTime" type="time" :list="timeList" :current="state.s_time" @changItem="selectTime" @close="closeItem"></search_item>

  <search_item ref="searchOrderby" type="orderby" :list="orderbyList" :current="state.s_orderby" @changItem="selectOrderby" @close="closeItem"></search_item>

  <!--帮约-->
  <!--开始帮约-->
  <oe_popup ref="after_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="audit_dialog">
      <p class="title">开始帮约</p>
      <p class="tip">请如实匹配双方情况，积极帮约用户完成帮约牵线目标。</p>
      <div class="btn flex_dc">
        <span @click="proxy.$refs.after_dialog.close()" class="fail" style="background: #f2f4f5; color: #666">稍后再说</span>
        <span @click="sendAfterStart">开始帮约</span>
      </div>
    </div>
  </oe_popup>

  <!--帮约失败-->
  <oe_popup ref="afterFail_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">不奖励原因</p>
      <p class="tip">{{ fail_result }}</p>
      <div @click="proxy.$refs.afterFail_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <!-- 修改帮约结果弹窗样式 -->
  <!--帮约结果-->
  <oe_popup ref="afterResult_dialog" width="100%">
    <div class="afterResult_dialog">
      <div class="nav flex_dc">
        <span @click="proxy.$refs.afterResult_dialog.close()" class="back flex_dc">
          <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
        </span>
        <div class="title flex_dc">帮约结果</div>
      </div>
      <div class="h44"></div>
      <div class="content">
        <p class="tip">请如实选择帮约结果且可上传佐证图片，平台会对帮约用户不定时进行抽检，如发现不实牵线，平台有权扣除相应奖励，并收回你的服务权限。</p>
        <div class="type">
          <p>选择帮约结果</p>
          <div class="radio">
            <span @click="changAfterType(2)">
              <i class="iconfont" :class="after_type == 2 ? 'icon-redio_checked' : 'icon-rediobtn_nor'"></i>
              帮约成功
            </span>
            <span @click="changAfterType(3)">
              <i class="iconfont" :class="after_type == 3 ? 'icon-redio_checked' : 'icon-rediobtn_nor'"></i>
              帮约失败
            </span>
          </div>
        </div>
        <div class="proof">
          <p>上传佐证（{{ after_list.length }}/9）</p>
          <div class="item flex">
            <span class="flex_s img" @click="deleteImg(item)" v-for="item in after_list" :key="item">
              <img v-lazy="item" alt="" />
              <i class="iconfont icon-guanbi1"></i>
            </span>
            <span @click="proxy.$refs.uploadBox.open(true)" v-show="after_list.length < 9" class="flex_s flex_dc iconfont icon-fabu-01"></span>
          </div>
        </div>
        <div class="remark" v-show="after_type == 3">
          <p>失败原因</p>
          <div class="box">
            <textarea v-model="fail_remark" maxlength="200" placeholder="请输入帮约失败的原因"></textarea>
            <span class="nums">{{ fail_remark.length }}/200</span>
          </div>
        </div>
        <div class="remark" v-show="after_type == 2">
          <p>备注</p>
          <div class="box">
            <textarea v-model="fail_remark" maxlength="200" placeholder="请输入帮约备注"></textarea>
            <span class="nums">{{ fail_remark.length }}/200</span>
          </div>
        </div>
        <div style="height: 2.6667rem"></div>
      </div>
      <div class="btn flex_dc">
        <span @click="sendAfterEnd()">确定</span>
      </div>
    </div>
  </oe_popup>
  <upload-headimg ref="uploadBox" @callback="sendImg"></upload-headimg>
</template>

<script>
export default {
  name: 'MyAfter',
}
</script>

<script setup>
import { ref, getCurrentInstance, reactive, onMounted, nextTick } from 'vue'
import oeuiList from '@/oeui/list.vue'
import search_item from '@/components/search_itme.vue'
import uploadHeadimg from '@/components/upload_headimg.vue'
import oe_popup from '@/oeui/popup.vue'
import list_item from '@/views/index/components/after_item.vue'
import { getAfterUser, afterStart, afterEnd } from '@/api/after.js'
import { uploadImage } from '@/api/edit.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')

const timeList = [
  { name: '全部时间', val: 0 },
  { name: '今天', val: 1 },
  { name: '昨天', val: 2 },
  { name: '本周', val: 3 },
  { name: '上周', val: 4 },
  { name: '本月', val: 5 },
  { name: '上月', val: 6 },
  { name: '今年', val: 7 },
  { name: '去年', val: 8 },
]
const orderbyList = [
  { name: '时间降序', val: 'id_desc' },
  { name: '时间升序', val: 'id_asc' },
  { name: '金额从高到低', val: 'amount_desc' },
  { name: '金额从低到高', val: 'amount_asc' },
]

const flagList = [
  { name: '全部状态', val: 0 },
  { name: '帮约中', val: 1 },
  { name: '帮约成功', val: 2 },
  { name: '帮约失败', val: 3 },
  { name: '待帮约', val: 99 },
]

const data = reactive({
  total2: 0,
  after_income: 0,
})

const state = reactive({
  s_time: 0,
  s_orderby: 'id_desc',
  s_flag: 0,
})
const is_time = ref(false)
const is_orderby = ref(false)
const is_flag = ref(false)

onMounted(() => {
  getList()
})

const changFlag = () => {
  proxy.$refs.searchTime.close()
  proxy.$refs.searchOrderby.close()
  is_time.value = false
  is_orderby.value = false
  nextTick(() => {
    if (is_flag.value) {
      is_flag.value = false
      proxy.$refs.searchFlag.close()
    } else {
      is_flag.value = true
      proxy.$refs.searchFlag.open()
    }
  })
}
const changOrderby = () => {
  proxy.$refs.searchFlag.close()
  proxy.$refs.searchTime.close()
  is_time.value = false
  is_flag.value = false
  nextTick(() => {
    if (is_orderby.value) {
      is_orderby.value = false
      proxy.$refs.searchOrderby.close()
    } else {
      is_orderby.value = true
      proxy.$refs.searchOrderby.open()
    }
  })
}
const changTime = () => {
  proxy.$refs.searchFlag.close()
  proxy.$refs.searchOrderby.close()
  is_orderby.value = false
  is_flag.value = false
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}

const selectFlag = val => {
  is_flag.value = false
  state.s_flag = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}
const selectOrderby = val => {
  is_orderby.value = false
  state.s_orderby = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}
const selectTime = val => {
  is_time.value = false
  state.s_time = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const closeItem = () => {
  is_time.value = false
  is_orderby.value = false
  is_flag.value = false
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getAfterUser({
    page: page.value,
    ...state,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (page.value == 1) {
        data.total2 = res.result.total2
        data.after_income = res.result.after_income
      }
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

//帮约操作
const afterid = ref(null)
const is_send = ref(true)
const fail_result = ref('')
const fail_remark = ref('')
const after_type = ref(2)
const after_list = ref([])
const changAfterType = val => {
  after_type.value = val
}

const sendImg = obj => {
  if (!obj.src) return OEUI.toast({ text: '图片获取失败，请检查!' })
  try {
    uploadImage({
      base64img: obj.src,
      module: 'upload',
      thumb: 1,
    }).then(data => {
      if (data.ret == 1) {
        after_list.value.push(data.result.drawimg_url)
      } else {
        OEUI.toast({ text: '图片上传失败，请检查!' })
      }
    })
  } catch (error) {
    OEUI.toast({ text: '系统繁忙，请检查!' })
  }
}
const deleteImg = val => {
  after_list.value = after_list.value.filter(item => item != val)
}
const ininAfterList = () => {
  if (!after_list.value.length) return
  let data = {}
  after_list.value.forEach((v, i) => {
    data['link_img' + (i + 1)] = v
  })
  return data
}

const afterStartEvent = id => {
  afterid.value = id
  proxy.$refs.after_dialog.open()
}
const AfterEvent = id => {
  afterid.value = id
  after_list.value = []
  proxy.$refs.afterResult_dialog.open()
}

const sendAfterStart = () => {
  if (!is_send.value) return
  is_send.value = false
  afterStart({
    id: afterid.value,
  }).then(res => {
    if (res.ret == 1) {
      proxy.$refs.after_dialog.close(() => {
        OEUI.toast({
          text: '操作成功',
        })
        afterid.value = null
        page.value = 1
        getList(true)
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}
const sendAfterEnd = () => {
  if (!is_send.value) return
  is_send.value = false
  afterEnd({
    id: afterid.value,
    flag: after_type.value,
    remark: fail_remark.value,
    ...ininAfterList(),
  }).then(res => {
    if (res.ret == 1) {
      proxy.$refs.afterResult_dialog.close(() => {
        OEUI.toast({
          text: '操作成功',
        })
        after_type.value = 2
        afterid.value = null
        fail_remark.value = ''
        after_list.value = []
        page.value = 1
        getList(true)
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}
const showFailAfter = val => {
  fail_result.value = val
  proxy.$refs.afterFail_dialog.open()
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}
.font_din {
  font-family: 'cpDin';
}
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.main {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.0533rem 0.4267rem;
  .filter {
    height: 1.0667rem;
    border-radius: 0.32rem;
    background: rgba(255, 255, 255, 0.5);
    div {
      flex: 1;
      color: #2a2546;
      text-align: center;
      span {
        margin-right: 0.1333rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }
      .color_c3 {
        color: #c5c3c7;
      }

      .hide {
        transform: rotate(90deg);
      }
      .show {
        transform: rotate(-90deg);
        position: relative;
        top: 0.0267rem;
      }
    }
  }
  .data {
    margin-top: 0.9067rem;
    div {
      p {
        font-size: 0.64rem;
        font-weight: 700;
        color: #31293b;
        line-height: 0.7733rem;
        @extend .font_din;
      }
      span {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
    }
  }

  .list {
    margin-top: 0.64rem;
    .item_box {
      cursor: pointer;
      background: #fff;
      border-radius: 0.32rem;
      margin-bottom: 0.32rem;
      .tit {
        padding-top: 0.2667rem;
        padding: 0.2667rem 0.32rem 0 0.32rem;
        .time {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          color: #999999;
          i {
            position: relative;
            top: 0.0267rem;
            margin-right: 0.1067rem;
          }
        }
        .status {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          color: #999999;
        }
      }
      .info {
        border-bottom: 0.0267rem solid #f4f6f7;
        padding: 0.32rem;
        .head {
          width: 1.1733rem;
          height: 1.1733rem;
          border-radius: 0.2133rem;
          overflow: hidden;
          margin-right: 0.32rem;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .name {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-weight: 500;
          color: #31293b;
        }
        i {
          position: relative;
          top: 0.0267rem;
          margin-left: 0.1333rem;
        }
        .icon-nanbiao-01 {
          color: #0570f1;
        }
        .icon-nvbiao-01 {
          color: #fe6897;
        }
        .money {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-weight: 500;
          color: #7d68fe;
        }
      }
    }
  }
}

.audit_dialog {
  padding: 0.64rem;
  font-family: PingFang SC, PingFang SC;
  position: relative;
  .title {
    font-size: 0.4267rem;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    text-align: center;
  }
  .tip {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.5867rem;
  }
  .btn {
    margin-top: 0.5333rem;
    span {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.4267rem;
      font-weight: normal;
      line-height: 0.5867rem;
      padding: 0.24rem 0;
      cursor: pointer;
      background: $color_main;
      border-radius: 0.5333rem;
      color: #fff;
    }
    .fail {
      margin-right: 0.5867rem;
      color: $color_main;
      background: rgba(125, 104, 254, 0.24);
    }
  }
  .close {
    position: absolute;
    right: 0.4rem;
    top: 0.2667rem;
    font-size: 0.48rem;
  }
}

.afterResult_dialog {
  font-family: PingFang SC, PingFang SC;
  .nav {
    height: 1.1733rem;
    position: fixed;
    top: 0;
    width: 100vw;
    z-index: 100;
    font-family: PingFang SC-Regular, PingFang SC;
    span {
      width: 1.1733rem;
      height: 1.1733rem;
      i {
        font-size: 0.64rem;
        position: relative;
        top: 0.0267rem;
      }
    }
    .back {
      position: absolute;
      left: 0;
    }
    .title {
      font-size: 0.48rem;
      line-height: 0.64rem;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #2a2546;
    }
  }
  .content {
    padding: 0 0.64rem;
    height: calc(100vh - 1.1733rem);
    overflow-y: scroll;
    margin-bottom: 100px;
    .tip {
      margin-top: 0.4267rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.5867rem;
    }
    .type {
      margin-top: 0.4267rem;
      p {
        font-size: 0.3733rem;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 0.5333rem;
      }
      .radio {
        margin-top: 0.2133rem;
        span {
          margin-right: 0.8533rem;
          font-size: 0.3733rem;
          font-weight: normal;
          color: #666666;
          line-height: 0.5333rem;
          i {
            position: relative;
            top: 0.0533rem;
            color: #999999;
            font-size: 0.48rem;
          }
          .icon-redio_checked {
            color: $color_main;
          }
        }
      }
    }
    .proof {
      margin-top: 0.4267rem;
      p {
        font-size: 0.3733rem;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 0.5333rem;
      }
      .item {
        margin-top: 0.2133rem;
        flex-wrap: wrap;
        span {
          width: 1.8667rem;
          height: 1.8667rem;
          background: #f2f4f5;
          margin-right: 0.5067rem;
          margin-bottom: 0.2667rem;
          border-radius: 0.3733rem;
          overflow: hidden;
          font-size: 0.96rem;
          color: #999;
          position: relative;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          .icon-guanbi1 {
            position: absolute;
            right: 0.0267rem;
            top: 0.0267rem;
            color: #ed1616;
            z-index: 100;
          }
        }
        & > span:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
    .remark {
      margin-top: 0.4267rem;
      p {
        font-size: 0.3733rem;
        font-weight: 500;
        color: #3d3d3d;
        line-height: 0.5333rem;
      }
      .box {
        margin-top: 0.2133rem;
        background: #f2f4f5;
        height: 2.8rem;
        border-radius: 0.32rem;
        padding: 0.32rem;
        box-sizing: border-box;
        position: relative;
        textarea {
          resize: none;
          border: none;
          width: 100%;
          height: 100%;
          background: none;
          &::placeholder {
            font-size: 0.3733rem;
            font-weight: normal;
            color: #c5c3c7;
            line-height: 0.5333rem;
          }
        }
        .nums {
          position: absolute;
          font-size: 0.32rem;
          font-weight: normal;
          color: #c5c3c7;
          line-height: 0.4533rem;
          right: 0.4rem;
          bottom: 0.0533rem;
        }
      }
    }
  }
  .btn {
    position: fixed;
    height: 2.6667rem;
    width: 100vw;
    bottom: 0;
    left: 0;
    margin-top: 0.4rem;
    span {
      width: 80%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.4267rem;
      font-weight: normal;
      line-height: 0.5867rem;
      padding: 0.24rem 0;
      cursor: pointer;
      background: $color_main;
      border-radius: 0.5333rem;
      color: #fff;
    }
  }
}
</style>

<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc" style="background: #f2f4f5">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">录入资源</div>
      <div class="search" @click="send">保存</div>
    </div>
    <div class="h44"></div>
    <div class="content">
      <p class="tit">基础资料</p>
      <div class="module">
        <div class="item" @click="openItemPicker('nickname')">
          <span class="name">
            <font color="red">*</font>
            昵称
          </span>
          <span class="value" v-if="state.nickname">
            <p>{{ state.nickname }}</p>
          </span>
          <span v-else>请输入</span>
        </div>
        <div class="item" @click="openItemPicker('mobile')">
          <span class="name">
            <font color="red">*</font>
            手机号
          </span>
          <span class="value" v-if="state.mobile">
            <p>{{ state.mobile }}</p>
          </span>
          <span v-else>请输入</span>
        </div>
        <div class="item">
          <span class="name">
            <font color="red">*</font>
            性别
          </span>
          <div class="gender">
            <span @click="changeGender(1)" :class="state.gender == 1 ? 'current' : ''"
              style="border-right: 0.0267rem solid #f2f4f5">男</span>
            <span :class="state.gender == 2 ? 'current' : ''" @click="changeGender(2)">女</span>
          </div>
        </div>
        <div class="item" @click="openItemPicker('age')">
          <span class="name">
            生日
          </span>
          <span class="value" v-if="state.ageyear > 0">
            {{ state.ageyear }}-{{ state.agemonth }}-{{ state.ageday }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('lunar')">
          <span class="name"> 生肖 </span>
          <span class="value" v-if="state.lunar > 0">
            {{ stateText.lunar }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('astro')">
          <span class="name"> 星座 </span>
          <span class="value" v-if="state.astro > 0">
            {{ stateText.astro }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('marry')">
          <span class="name"> 婚况 </span>
          <span class="value" v-if="state.marry > 0">
            {{ stateText.marry }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('education')">
          <span class="name"> 学历 </span>
          <span class="value" v-if="state.education > 0">
            {{ stateText.education }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('salary')">
          <span class="name"> 年收入 </span>
          <span class="value" v-if="state.salary > 0">
            {{ stateText.salary }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('area')">
          <span class="name">{{config.dist_title||'居住地'}}  </span>
          <span class="value" v-if="state.dist1 > 0">
            <p>{{ stateText.dist1 }} {{ stateText.dist2 }} {{ stateText.dist3 }} {{ stateText.dist4 }}</p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('home')">
          <span class="name"> {{config.home_title||'户籍地'}} </span>
          <span class="value" v-if="state.home1 > 0">
            <p>{{ stateText.home1 }}{{ stateText.home2 }} {{ stateText.home3 }} {{ stateText.home4 }}</p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>

        <div class="item" @click="openItemPicker('height')">
          <span class="name"> 身高 </span>
          <span class="value" v-if="state.height">
            {{ state.height }}cm
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('weight')">
          <span class="name"> 体重 </span>
          <span class="value" v-if="state.weight">
            {{ state.weight }}kg
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('school')">
          <span class="name"> 毕业院校 </span>
          <span class="value" v-if="state.school">
            <p>{{ state.school }}</p>
          </span>
          <span v-else>请输入</span>
        </div>
        <div class="item" @click="openItemPicker('national')">
          <span class="name"> 民族 </span>
          <span class="value" v-if="state.national > 0">
            {{ stateText.national }}
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            请选择
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
      </div>

      <p class="tit">择偶要求</p>
      <div class="module">
        <div class="item" @click="openItemPicker('condAge')">
          <span class="name"> 年龄范围 </span>
          <span class="value" v-if="state.cond_age1 > 0 && state.cond_age2 > 0">
            {{ state.cond_age1 }}- {{ state.cond_age2 }}岁
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            不限
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('condHeight')">
          <span class="name"> 身高范围 </span>
          <span class="value" v-if="state.cond_height1 > 0 && state.cond_height2 > 0">
            {{ state.cond_height1 }}- {{ state.cond_height2 }}cm
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            不限
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('condWeight')">
          <span class="name"> 体重范围 </span>
          <span class="value" v-if="state.cond_weight1 > 0 && state.cond_weight2 > 0">
            {{ state.cond_weight1 }}- {{ state.cond_weight2 }}kg
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            不限
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('condMarry')">
          <span class="name">婚况</span>
          <span class="value" v-if="state.cond_marry">
            <p>{{ stateText.cond_marry }}</p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            不限
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('condEducation')">
          <span class="name"> 学历 </span>
          <span class="value" v-if="state.cond_education">
            <p>{{ stateText.cond_education }}</p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            不限
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('condSalary')">
          <span class="name"> 年收入 </span>
          <span class="value" v-if="state.cond_salary">
            <p>{{ stateText.cond_salary }}</p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            不限
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('condHouse')">
          <span class="name"> 购房 </span>
          <span class="value" v-if="state.cond_house">
            <p>{{ stateText.cond_house }}</p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            不限
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('condCar')">
          <span class="name"> 购车 </span>
          <span class="value" v-if="state.cond_car">
            <p>{{ stateText.cond_car }}</p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            不限
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('condChild')">
          <span class="name"> 小孩情况 </span>
          <span class="value" v-if="state.cond_child">
            <p>{{ stateText.cond_child }}</p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            不限
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('condLunar')">
          <span class="name"> 生肖 </span>
          <span class="value" v-if="state.cond_lunar">
            <p>{{ stateText.cond_lunar }}</p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            不限
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('condAstro')">
          <span class="name"> 星座 </span>
          <span class="value" v-if="state.cond_astro">
            <p>{{ stateText.cond_astro }}</p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            不限
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
        <div class="item" @click="openItemPicker('condArea')">
          <span class="name"> {{config.dist_title||'居住地'}} </span>
          <span class="value" v-if="state.cond_dist1 > 0">
            <p>{{ stateText.cond_dist1 }}{{ stateText.cond_dist2 }} {{ stateText.cond_dist3 }} {{ stateText.cond_dist4 }}
            </p>
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
          <span v-else>
            不限
            <i class="iconfont icon-youjiantou-01"></i>
          </span>
        </div>
      </div>
    </div>
    <div class="h44"></div>
  </div>

  <edit-text ref="nicknamePicker" title="昵称" inputText="请输入昵称" @next="sendNickname" :current="state.nickname"></edit-text>
  <edit-text ref="mobilePicker" :length="11" title="手机号" inputText="请输入手机号" @next="sendMobile"
    :current="state.mobile"></edit-text>
  <edit-text ref="schoolPicker" title="毕业院校" inputText="请输入毕业院校" @next="sendSchool" :current="state.school"></edit-text>

  <oeui-time ref="agePicker" :start="18" :end="70"
    :current="[Number(state.ageyear), Number(state.agemonth), Number(state.ageday)]" @confirm="sendAge"> </oeui-time>

  <oeui-picker ref="lunarPicker" title="生肖" :list="listState.lunarList" :current="[Number(state.lunar)]"
    v-if="listState.lunarList.length > 0" @confirm="sendLunar"> </oeui-picker>
  <oeui-picker ref="astroPicker" title="星座" :list="listState.astroList" :current="[Number(state.astro)]"
    v-if="listState.astroList.length > 0" @confirm="sendAstro"> </oeui-picker>

  <oeui-picker ref="marryPicker" title="婚况" :list="listState.marryList" :current="[Number(state.marry)]"
    v-if="listState.marryList.length > 0" @confirm="sendMarry"> </oeui-picker>
  <oeui-picker ref="educationPicker" title="学历" :list="listState.educationList" :current="[Number(state.education)]"
    v-if="listState.educationList.length > 0" @confirm="sendEducation" />
  <oeui-picker ref="salaryPicker" title="年收入" :list="listState.salaryList" :current="[Number(state.salary)]"
    v-if="listState.salaryList.length > 0" @confirm="sendSalary"> </oeui-picker>

  <oeui-area ref="areaPicker" :title="config.dist_title||'居住地'" :list="listState.areaList"
    :current="[state.dist1, state.dist2, state.dist3, state.dist4]" @change="sendArea"
    v-if="listState.areaList.length > 0"></oeui-area>
  <oeui-area ref="homePicker" :title="config.home_title||'户籍地'" :list="listState.homeList"
    :current="[state.home1, state.home2, state.home3, state.home4]" @change="sendHome"
    v-if="listState.homeList.length > 0"></oeui-area>

  <oeui-picker ref="heightPicker" title="身高" :list="listState.heightList" :current="[Number(state.height)]"
    v-if="listState.heightList.length > 0" @confirm="sendHeight"> </oeui-picker>
  <oeui-picker ref="weightPicker" title="体重" :list="listState.weightList" :current="[Number(state.weight)]"
    v-if="listState.weightList.length > 0" @confirm="sendWeight"> </oeui-picker>
  <oeui-picker ref="nationalPicker" title="民族" :list="listState.nationalList" :current="[Number(state.national)]"
    v-if="listState.nationalList.length > 0" @confirm="sendNational"> </oeui-picker>

  <oeui-contrast ref="condAgePicker" title="你期望TA的年龄" :list="listState.ageCondList" :list2="listState.ageCondList"
    :current="[Number(state.cond_age1), Number(state.cond_age2)]" @confirm="sendCondAge"
    v-if="listState.ageCondList.length > 0"> </oeui-contrast>
  <oeui-contrast ref="condHeightPicker" title="你期望TA的身高" :list="listState.heightCondList"
    :list2="listState.heightCondList" :current="[Number(state.cond_height1), Number(state.cond_height2)]"
    @confirm="sendCondHeight" v-if="listState.heightCondList.length > 0"> </oeui-contrast>
  <oeui-contrast ref="condWeightPicker" title="你期望TA的体重" :list="listState.weightCondList"
    :list2="listState.weightCondList" :current="[Number(state.cond_weight1), Number(state.cond_weight2)]"
    @confirm="sendCondWeight" v-if="listState.weightCondList.length > 0"> </oeui-contrast>

  <oeui-more ref="condMarryPicker" title="你期望TA的婚姻状况" :list="listState.marryCondList" :current="state.cond_marry"
    v-if="listState.marryCondList.length > 0" @confirm="sendCondMarry"> </oeui-more>

  <oeui-more ref="condEducationPicker" title="你期望TA的学历" :list="listState.educationCondList"
    :current="state.cond_education" v-if="listState.educationCondList.length > 0" @confirm="sendCondEducation">
  </oeui-more>

  <oeui-more ref="condSalaryPicker" title="你期望TA的年收入" :list="listState.salaryCondList" :current="state.cond_salary"
    v-if="listState.salaryCondList.length > 0" @confirm="sendCondSalary"> </oeui-more>

  <oeui-more ref="condHousePicker" title="你期望TA的住房情况" :list="listState.houseCondList" :current="state.cond_house"
    v-if="listState.houseCondList.length > 0" @confirm="sendCondHouse"> </oeui-more>

  <oeui-more ref="condCarPicker" title="你期望TA的购车情况" :list="listState.carCondList" :current="state.cond_car"
    v-if="listState.carCondList.length > 0" @confirm="sendCondCar"> </oeui-more>

  <oeui-more ref="condChildPicker" title="你期望TA的小孩情况" :list="listState.childCondList" :current="state.cond_child"
    v-if="listState.childCondList.length > 0" @confirm="sendCondChild"> </oeui-more>

  <oeui-more ref="condLunarPicker" title="你期望TA的生肖" :list="listState.lunarCondList" :current="state.cond_lunar"
    v-if="listState.lunarCondList.length > 0" @confirm="sendCondLunar"> </oeui-more>

  <oeui-more ref="condAstroPicker" title="你期望TA的星座" :list="listState.astroCondList" :current="state.cond_astro"
    v-if="listState.astroCondList.length > 0" @confirm="sendCondAstro"> </oeui-more>

  <oeui-area ref="condDistPicker" :title="'你期望TA的'+config.dist_title||'居住地'" :list="listState.distCondList" :limit="areaLimit"
    :current="[state.cond_dist1, state.cond_dist2, state.cond_dist3, state.cond_dist4]" @change="sendCondArea"
    v-if="listState.distCondList.length > 0"></oeui-area>
</template>

<script setup>
import { ref, getCurrentInstance, reactive, nextTick, computed } from 'vue'
import oeuiArea from '@/oeui/area.vue'
import editText from '@/components/edit_text.vue'
import oeuiTime from '@/oeui/datetime.vue'
import { getPickerData, getDistPicker, getHomePicker } from '@/utils/main'
import { addCrmmemberUser } from '@/api/crm.js'
import oeuiPicker from '@/oeui/picker.vue'
import oeuiContrast from '@/oeui/contrast.vue'
import oeuiMore from '@/oeui/more.vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
const store = useStore()
const config = computed(() => store.state.config)
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const router = useRouter()

const areaLimit = ref({
  value: 0,
  text: '不限'
})
const listState = reactive({
  ageList: [],
  lunarList: [],
  astroList: [],
  marryList: [],
  educationList: [],
  salaryList: [],
  areaList: [],
  homeList: [],
  heightList: [],
  weightList: [],
  nationalList: [],

  ageCondList: [],
  heightCondList: [],
  weightCondList: [],
  marryCondList: [],
  educationCondList: [],
  salaryCondList: [],
  houseCondList: [],
  carCondList: [],
  childCondList: [],
  lunarCondList: [],
  astroCondList: [],
  distCondList: []
})
const state = reactive({
  nickname: '',
  mobile: '',
  gender: '',
  ageyear: '',
  agemonth: '',
  ageday: '',
  lunar: '',
  astro: '',
  dist1: '',
  dist2: '',
  dist3: '',
  dist4: '',
  home1: '',
  home2: '',
  home3: '',
  home4: '',
  marry: '',
  education: '',
  salary: '',
  height: '',
  weight: '',
  school: '',
  national: '',
  intro: '',
  cond_age1: '',
  cond_age2: '',
  cond_height1: '',
  cond_height2: '',
  cond_weight1: '',
  cond_weight2: '',
  cond_marry: '',
  cond_education: '',
  cond_house: '',
  cond_car: '',
  cond_child: '',
  cond_lunar: '',
  cond_astro: '',
  cond_dist1: '',
  cond_dist2: '',
  cond_dist3: '',
  cond_dist4: ''
})

const stateText = reactive({
  lunar: '',
  astro: '',
  marry: '',
  education: '',
  salary: '',
  dist1: '',
  dist2: '',
  dist3: '',
  dist4: '',
  home1: '',
  home2: '',
  home3: '',
  home4: '',
  national: '',
  cond_marry: '',
  cond_education: '',
  cond_salary: '',
  cond_house: '',
  cond_car: '',
  cond_child: '',
  cond_lunar: '',
  cond_astro: '',
  cond_dist1: '',
  cond_dist2: '',
  cond_dist3: '',
  cond_dist4: ''
})

const changeGender = val => {
  state.gender = val
}

const sendNickname = val => {
  state.nickname = val
}
const sendMobile = val => {
  state.mobile = val
}
const sendSchool = val => {
  state.school = val
}
const sendAge = val => {
  state.ageyear = val[0].value
  state.agemonth = val[1].value
  state.ageday = val[2].value
}
const sendLunar = val => {
  state.lunar = val[0].value
  stateText.lunar = val[0].text
}
const sendAstro = val => {
  state.astro = val[0].value
  stateText.astro = val[0].text
}
const sendMarry = val => {
  state.marry = val[0].value
  stateText.marry = val[0].text
}
const sendEducation = val => {
  state.education = val[0].value
  stateText.education = val[0].text
}
const sendSalary = val => {
  state.salary = val[0].value
  stateText.salary = val[0].text
}
const sendArea = val => {
  state.dist1 = val[0].value
  stateText.dist1 = val[0].text
  state.dist2 = val[1].value
  stateText.dist2 = val[1].text
  state.dist3 = val[2].value
  stateText.dist3 = val[2].text
  state.dist4 = val[3].value
  stateText.dist4 = val[3].text
}
const sendHome = val => {
  state.home1 = val[0].value
  stateText.home1 = val[0].text
  state.home2 = val[1].value
  stateText.home2 = val[1].text
  state.home3 = val[2].value
  stateText.home3 = val[2].text
  state.home4 = val[3].value
  stateText.home4 = val[3].text
}
const sendHeight = val => {
  state.height = val[0].value
}
const sendWeight = val => {
  state.weight = val[0].value
}
const sendNational = val => {
  state.national = val[0].value
  stateText.national = val[0].text
}

const sendCondAge = val => {
  state.cond_age1 = val[0].value
  state.cond_age2 = val[1].value
}
const sendCondHeight = val => {
  state.cond_height1 = val[0].value
  state.cond_height2 = val[1].value
}
const sendCondWeight = val => {
  state.cond_weight1 = val[0].value
  state.cond_weight2 = val[1].value
}
const sendCondMarry = val => {
  if (val[0].value == 0) {
    state.cond_marry = ''
    stateText.cond_marry = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_marry = arr.join(',')
  stateText.cond_marry = arr_text.join(' ')
}
const sendCondEducation = val => {
  if (val[0].value == 0) {
    state.cond_education = ''
    stateText.cond_education = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_education = arr.join(',')
  stateText.cond_education = arr_text.join(' ')
}
const sendCondSalary = val => {
  if (val[0].value == 0) {
    state.cond_salary = ''
    stateText.cond_salary = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_salary = arr.join(',')
  stateText.cond_salary = arr_text.join(' ')
}
const sendCondHouse = val => {
  if (val[0].value == 0) {
    state.cond_house = ''
    stateText.cond_house = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_house = arr.join(',')
  stateText.cond_house = arr_text.join(' ')
}
const sendCondCar = val => {
  if (val[0].value == 0) {
    state.cond_car = ''
    stateText.cond_car = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_car = arr.join(',')
  stateText.cond_car = arr_text.join(' ')
}
const sendCondChild = val => {
  if (val[0].value == 0) {
    state.cond_child = ''
    stateText.cond_child = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_child = arr.join(',')
  stateText.cond_child = arr_text.join(' ')
}
const sendCondLunar = val => {
  if (val[0].value == 0) {
    state.cond_lunar = ''
    stateText.cond_lunar = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_lunar = arr.join(',')
  stateText.cond_lunar = arr_text.join(' ')
}
const sendCondAstro = val => {
  if (val[0].value == 0) {
    state.cond_astro = ''
    stateText.cond_astro = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_astro = arr.join(',')
  stateText.cond_astro = arr_text.join(' ')
}
const sendCondArea = val => {
  state.cond_dist1 = val[0]?.value || ''
  stateText.cond_dist1 = val[0]?.text || ''
  state.cond_dist2 = val[1]?.value || ''
  stateText.cond_dist2 = val[1]?.text || ''
  state.cond_dist3 = val[2]?.value || ''
  stateText.cond_dist3 = val[2]?.text || ''
  state.cond_dist4 = val[3]?.value || ''
  stateText.cond_dist4 = val[3]?.text || ''
}

const is_send = ref(true)
const send = () => {
  if (!state.nickname) return OEUI.toast({ text: '请填写昵称' })
  if (!state.mobile) return OEUI.toast({ text: '请填写手机号' })
  if (!state.gender) return OEUI.toast({ text: '请选择性别' })
  if (!is_send.value) return
  is_send.value = false
  OEUI.loading.show()
  addCrmmemberUser({ ...state })
    .then(res => {
      OEUI.loading.hide()
      if (res.ret == 1) {
        OEUI.toast({ text: '录入成功' })
        setTimeout(() => {
          router.replace('/crmmember?flag=true')
        }, 400)
      } else {
        OEUI.toast({ text: res.msg || '操作失败,请检查!' })
      }
      setTimeout(() => {
        is_send.value = true
      }, 500)
    })
    .catch(() => {
      is_send.value = true
      OEUI.loading.hide()
    })
}

const openItemPicker = item => {
  let text = item.toLowerCase()
  let condText = text.replace('cond', '')
  if (text.includes('cond')) {
    if (condText == 'area') {
      return getDistDataCond()
    } else if (listState[condText + 'CondList'] && listState[condText + 'CondList'].length == 0) {
      return getItemDataCond(condText)
    }
    nextTick(() => {
      proxy.$refs[item + 'Picker'].open()
    })
    return
  }
  if (item == 'school' || item == 'nickname' || item == 'mobile' || item == 'age') {
    return proxy.$refs[item + 'Picker'].open()
  } else if (item == 'area') {
    return getDistData()
  } else if (item == 'home') {
    return getHomeData()
  } else if (listState[text + 'List'] && listState[text + 'List'].length == 0) {
    return getItemData(text)
  }
  proxy.$refs[item + 'Picker'].open()
}

const getItemData = (item, limit = false) => {
  //获取picker数据
  const p = getPickerData(item, limit)
  p.then(res => {
    if (res.ret == 1) {
      listState[item + 'List'] = res.result
      nextTick(() => {
        proxy.$refs[item + 'Picker'].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
  return p
}

const getItemDataCond = (item, limit = true) => {
  //获取picker数据
  const p = getPickerData(item, limit)
  p.then(res => {
    if (res.ret == 1) {
      listState[item + 'CondList'] = res.result
      nextTick(() => {
        proxy.$refs[`cond${titleUpperCase(item)}Picker`].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
  return p
}

const titleUpperCase = str => {
  return str.slice(0, 1).toUpperCase() + str.slice(1).toLowerCase()
}

const getDistData = () => {
  //获取居住地数据
  getDistPicker().then(res => {
    if (res.ret == 1) {
      listState.areaList = res.result
      nextTick(() => {
        proxy.$refs['areaPicker'].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
}
const getDistDataCond = () => {
  //获取居住地数据
  getDistPicker().then(res => {
    if (res.ret == 1) {
      listState.distCondList = res.result
      nextTick(() => {
        proxy.$refs['condDistPicker'].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
}
const getHomeData = () => {
  //获取户籍地数据
  getHomePicker().then(res => {
    if (res.ret == 1) {
      listState.homeList = res.result
      nextTick(() => {
        proxy.$refs['homePicker'].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
}
</script>

<style lang="scss" scoped>
.main {
  position: absolute;
  width: 100vw;
  min-height: 100vh;
  background: #f2f4f5;

  .content {
    padding: 0 0.4267rem;
    font-family: PingFang SC, PingFang SC;

    .tit {
      font-size: 0.4267rem;
      font-weight: 500;
      color: #31293b;
      line-height: 0.5867rem;
      padding: 0.32rem 0;
    }

    .module {
      background: #fff;
      padding: 0.32rem 0.4267rem;
      border-radius: 0.32rem;

      .item {
        height: 1.1733rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #c5c3c7;
        font-size: 0.3733rem;
        font-weight: normal;
        line-height: 0.5333rem;

        .name {
          color: #666666;
        }

        .value {
          color: #31293b;
          display: flex;
          align-items: center;

          p {
            max-width: 4.5333rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }

        input {
          text-align: right;
        }

        i {
          color: #000;
          position: relative;
          top: 0.0267rem;
        }

        .gender {
          font-size: 0.3733rem;
          font-weight: normal;
          color: #c5c3c7;
          line-height: 0.5333rem;
          border: 0.0267rem solid #f2f4f5;
          border-radius: 0.3733rem;
          overflow: hidden;

          span {
            padding: 0.1067rem 0.32rem;

            &.current {
              background: $color_main;
              color: #fff;
            }
          }
        }
      }
    }
  }
}</style>

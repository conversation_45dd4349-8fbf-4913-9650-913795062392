<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">潜在资源</div>
    <span class="more" @click="$router.push('/crmuser/add')">录入资源</span>
  </div>
  <div class="h44"></div>
  <div class="main">
    <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="filter flex_dc">
        <div class="flex_dc" @click="changTime">
          <span>全部时间</span>
          <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changOrderby">
          <span>{{ state.s_orderby == 'id_desc' ? '时间降序' : '时间升序' }}</span>
          <span class="flex_dc flex_v">
            <i :class="state.s_orderby == 'id_desc' ? 'color_c3' : ''" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; transform: rotate(180deg); top: 0.08rem"></i>
            <i :class="state.s_orderby == 'id_desc' ? '' : 'color_c3'" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; bottom: 0.0533rem"></i>
          </span>
        </div>
      </div>
      <div class="flex flex_jsb flex_1 data" style="padding: 0 0.4267rem">
        <div class="flex_dc flex_v flex_1">
          <p>{{ data.not_sign_total }}</p>
          <span class="fn_i">未签约</span>
        </div>
        <div class="flex_dc flex_v flex_1">
          <p>{{ data.has_sign_total }}</p>
          <span class="fn_i">已签约</span>
        </div>
        <div class="flex_dc flex_v flex_1">
          <p>{{ data.other_total }}</p>
          <span class="fn_i">其他</span>
        </div>
      </div>
      <!--<div class="tip">用户信息录入后，待平台核实为有效资源后将获得对应奖励</div>-->
      <div class="search flex flex_ac flex_jsb">
        <i class="iconfont icon-sousuo"></i>
        <input type="text" v-model="state.s_name" class="flex_1" placeholder="请输入用户昵称或编号" />
        <span class="btn" @click="searchName">搜索</span>
      </div>
      <div class="list">
        <template v-for="item in list" :key="item.userid">
          <div class="item_box" @click="$router.push('/crmuser/detail?id=' + item.mid)">
            <div class="tit flex flex_ac flex_jsb">
              <div class="time flex flex_ac">
                <i class="iconfont icon-shizhong"></i>
                <p>录入时间：{{ getTime(item?.addtime) }}</p>
              </div>
              <div class="status" v-if="item.signflag == 1">未签约</div>
              <div class="status color_green" v-else-if="item.signflag == 2">已签约</div>
              <div class="status" v-else-if="item.signflag == 3">到期未续约</div>
              <div class="status color_green" v-else-if="item.signflag == 4">已续约</div>
              <div class="status" v-else-if="item.signflag == 10">被动方</div>
              <div class="status" v-else-if="item.signflag == 11">已关单</div>
            </div>
            <div class="info flex flex_ac flex_jsb">
              <div class="flex_1 flex flex_ac">
                <div class="head">
                  <img v-if="item.headimg" type="user" v-lazy="item.headimg_m2_url" alt="" />
                  <template v-else>
                    <img v-if="item.gender == 1" src="@/assets/images/gender_1.png" alt="" />
                    <img v-else-if="item.gender == 2" src="@/assets/images/gender_2.png" alt="" />
                    <img v-else src="@/assets/images/gender_0.png" alt="" />
                  </template>
                </div>
                <div class="flex_1">
                  <div class="flex flex_ac">
                    <p class="name">{{ item.nickname }}</p>
                    <i class="iconfont icon-nanbiao-01" v-if="item.gender == 1"></i>
                    <i class="iconfont icon-nvbiao-01" v-else></i>
                  </div>
                  <p class="id">编号:{{ item.mid }}</p>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </oeui-list>
  </div>
  <search_item ref="searchTime" :list="timeList" :current="state.s_time" @changItem="selectTime" @close="closeTime"></search_item>
</template>

<script>
export default {
  name: 'Crmmember',
}
</script>

<script setup>
import { ref, getCurrentInstance, reactive, onMounted, nextTick, onActivated } from 'vue'
import oeuiList from '@/oeui/list.vue'
import search_item from '@/components/search_itme.vue'
import { getCrmmemberUser } from '@/api/crm.js'
import { getTime } from '@/utils/hooks.js'
import { useRoute } from 'vue-router'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const route = useRoute()

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')

onActivated(() => {
  if (route.query.flag) {
    page.value = 1
    getList(true)
  }
})

const timeList = [
  { name: '全部时间', val: 0 },
  { name: '今天', val: 1 },
  { name: '昨天', val: 2 },
  { name: '本周', val: 3 },
  { name: '上周', val: 4 },
  { name: '本月', val: 5 },
  { name: '上月', val: 6 },
  { name: '今年', val: 7 },
  { name: '去年', val: 8 },
]

const is_time = ref(false)

const state = reactive({
  s_name: '',
  s_time: 0,
  s_orderby: 'id_desc',
})

const data = reactive({
  total: 0,
  has_sign_total: 0,
  not_sign_total: 0,
  other_total: 0,
})

onMounted(() => {
  getList()
})

const searchName = () => {
  page.value = 1
  getList(true)
}

const changTime = () => {
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}

const closeTime = () => {
  is_time.value = false
}

const selectTime = val => {
  is_time.value = false
  state.s_time = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}
const changOrderby = () => {
  if (state.s_orderby == 'id_desc') {
    state.s_orderby = 'id_asc'
  } else {
    state.s_orderby = 'id_desc'
  }
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getCrmmemberUser({
    page: page.value,
    ...state,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (page.value == 1) {
        data.total = res.result.total
        data.has_sign_total = res.result.has_sign_total
        data.not_sign_total = res.result.not_sign_total
        data.other_total = res.result.other_total
      }
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}
.font_din {
  font-family: 'cpDin';
}
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.main {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.0533rem 0.4267rem;
  .filter {
    height: 1.0667rem;
    border-radius: 0.32rem;
    background: rgba(255, 255, 255, 0.5);
    div {
      flex: 1;
      color: #2a2546;
      text-align: center;
      span {
        margin-right: 0.1333rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }
      .color_c3 {
        color: #c5c3c7;
      }

      .hide {
        transform: rotate(90deg);
      }
      .show {
        transform: rotate(-90deg);
        position: relative;
        top: 0.0267rem;
      }
    }
  }
  .data {
    margin-top: 0.64rem;
    div {
      p {
        font-size: 0.64rem;
        font-weight: 700;
        color: #31293b;
        line-height: 0.7733rem;
        @extend .font_din;
      }
      span {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
    }
  }
  .tip {
    margin-top: 0.4267rem;
    border-radius: 0.32rem;
    background: #ececf9;
    padding: 0.32rem;
    font-size: 0.3733rem;
    font-weight: normal;
    color: #666666;
    line-height: 0.6133rem;
  }
  .search {
    margin-top: 0.3733rem;
    background: #fff;
    padding: 0.1067rem 0.2133rem;
    border-radius: 0.32rem;
    i {
      padding-left: 0.1067rem;
      color: #000;
      font-size: 0.48rem;
    }
    input {
      padding: 0 0.2133rem;
    }
    .btn {
      background: #7d68fe;
      color: #fff;
      font-size: 0.3467rem;
      line-height: 0.48rem;
      font-weight: normal;
      padding: 0.1867rem 0.4267rem;
      border-radius: 0.4267rem;
    }
  }
  .list {
    margin-top: 0.3733rem;
    .item_box {
      cursor: pointer;
      background: #fff;
      border-radius: 0.32rem;
      margin-bottom: 0.32rem;
      .tit {
        padding-top: 0.2667rem;
        padding: 0.2667rem 0.32rem 0 0.32rem;
        .time {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          color: #999999;
          i {
            position: relative;
            top: 0.0267rem;
            margin-right: 0.1067rem;
          }
        }
        .status {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          color: #999999;
        }
      }
      .info {
        border-bottom: 0.0267rem solid #f4f6f7;
        padding: 0.32rem;
        .head {
          width: 1.1733rem;
          height: 1.1733rem;
          border-radius: 0.32rem;
          overflow: hidden;
          margin-right: 0.32rem;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .name {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-weight: 500;
          color: #31293b;
        }
        .id {
          font-size: 0.32rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: normal;
          color: #666666;
          line-height: 0.4533rem;
        }
        i {
          position: relative;
          top: 0.0267rem;
          margin-left: 0.1333rem;
        }
        .icon-nanbiao-01 {
          color: #0570f1;
        }
        .icon-nvbiao-01 {
          color: #fe6897;
        }
        .money {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-weight: 500;
          color: #7d68fe;
        }
      }
    }
  }
}
</style>

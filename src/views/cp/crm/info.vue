<!-- @format -->

<template>
  <div class="content_box">
    <div class="item">
      <span class="name">昵称 </span>
      <span class="value">{{ item.nickname }}</span>
    </div>

    <div class="item">
      <span class="name">生日</span>
      <span class="value" v-if="item.ageyear > 0">{{ item.ageyear }}-{{ item.agemonth }}-{{ item.ageday }}</span>
      <span v-else>未填写</span>
    </div>

    <div class="item">
      <span class="name">婚况</span>
      <span class="value" v-if="item.marry > 0">{{ item.marry_t }}</span>
      <span v-else>未填写</span>
    </div>
    <div class="item">
      <span class="name">{{ config.dist_title || '居住地' }}</span>
      <span class="value" v-if="item.dist1 > 0">{{ item.dist1_t }}{{ item.dist2_t }}{{ item.dist3_t }}{{ item.dist4_t
      }}</span>
      <span v-else>未填写</span>
    </div>
    <div class="item">
      <span class="name">{{ config.home_title || '户籍地' }}</span>
      <span class="value" v-if="item.home1 > 0">{{ item.home1_t }}{{ item.home2_t }}{{ item.home3_t }}{{ item.home4_t
      }}</span>
      <span v-else>未填写</span>
    </div>

    <div class="item">
      <span class="name">学历</span>
      <span class="value" v-if="item.education > 0">{{ item.education_t }}</span>
      <span v-else>未填写</span>
    </div>
    <div class="item">
      <span class="name">年收入</span>
      <span class="value" v-if="item.salary > 0">{{ item.salary_t }}</span>
      <span v-else>未填写</span>
    </div>

    <div class="item">
      <span class="name">身高</span>
      <span class="value" v-if="item.height > 0">{{ item.height }}cm</span>
      <span v-else>未填写</span>
    </div>
    <div class="item">
      <span class="name">体重</span>
      <span class="value" v-if="item.weight > 0">{{ item.weight }}kg</span>
      <span v-else>未填写</span>
    </div>

    <div class="item">
      <span class="name">毕业院校</span>
      <span class="value" v-if="item.school">{{ item.school }}</span>
      <span v-else>未填写</span>
    </div>
    <div class="item">
      <span class="name">民族</span>
      <span class="value" v-if="item.national > 0">{{ item.national_t }}</span>
      <span v-else>未填写</span>
    </div>
  </div>
</template>

<script setup>
import {  computed } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const config = computed(() => store.state.config)
defineProps({
  item: {
    type: Object
  }
})
</script>

<style lang="scss" scoped>
.content_box {
  background: #fff;
  padding: 0.32rem;
  border-radius: 0.32rem;

  .item {
    height: 1.1733rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: PingFang SC, PingFang SC;
    font-size: 0.3733rem;
    line-height: 0.5333rem;

    span {
      color: #ccc;
    }

    .name {
      font-weight: normal;
      color: #999999;
    }

    .value {
      font-weight: 500;
      color: #31293b;
    }
  }
}
</style>

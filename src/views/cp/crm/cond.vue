<!-- @format -->

<template>
  <div class="content_box">
    <div class="item">
      <span class="name">年龄范围</span>
      <span class="value" v-if="item.age1 > 0 && item.age2 > 0">{{ item.age1 }}-{{ item.age2 }}岁</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">身高范围</span>
      <span class="value" v-if="item.height1 > 0 && item.height2 > 0">{{ item.height1 }}-{{ item.height2 }}cm</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">体重范围</span>
      <span class="value" v-if="item.weight1 > 0 && item.weight2 > 0">{{ item.weight1 }}-{{ item.weight2 }}kg</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">婚况</span>
      <span class="value" v-if="item.marry_arr && item.marry_arr.length">{{ item.marry_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">学历</span>
      <span class="value" v-if="item.education_arr && item.education_arr.length">{{ item.education_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">年收入</span>
      <span class="value" v-if="item.salary_arr && item.salary_arr.length">{{ item.salary_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">购房</span>
      <span class="value" v-if="item.house_arr && item.house_arr.length">{{ item.house_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">购车</span>
      <span class="value" v-if="item.car_arr && item.car_arr.length">{{ item.car_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">小孩情况</span>
      <span class="value" v-if="item.child_arr && item.child_arr.length">{{ item.child_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">生肖</span>
      <span class="value" v-if="item.lunar_arr && item.lunar_arr.length">{{ item.lunar_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">星座</span>
      <span class="value" v-if="item.astro_arr && item.astro_arr.length">{{ item.astro_t }}</span>
      <span v-else>不限</span>
    </div>
    <div class="item">
      <span class="name">{{ config.dist_title || '居住地' }}</span>
      <span class="value" v-if="item.area1 > 0">{{ item.area1_t }} {{ item.area2_t }} {{ item.area3_t }} {{ item.area4_t
      }}</span>
      <span v-else>不限</span>
    </div>
  </div>
</template>

<script setup>
import {  computed } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const config = computed(() => store.state.config)
defineProps({
  item: {
    type: Object
  }
})
</script>

<style lang="scss" scoped>
.content_box {
  background: #fff;
  padding: 0.32rem;
  border-radius: 0.32rem;

  .item {
    height: 1.1733rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: PingFang SC, PingFang SC;
    font-size: 0.3733rem;
    line-height: 0.5333rem;

    span {
      color: #ccc;
    }

    .name {
      font-weight: normal;
      color: #999999;
    }

    .value {
      font-weight: 500;
      color: #31293b;
      max-width: 5.0667rem;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}</style>

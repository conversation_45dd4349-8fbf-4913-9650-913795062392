<!-- @format -->

<template>
  <div class="main">
    <div class="msg_nav flex flex_ac">
      <span>消息</span>
    </div>
    <div class="h44"></div>
    <div class="list">
      <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
        <div style="padding-bottom: 1.8667rem">
          <msg_item :item="item" v-for="item in list" :key="item.msgid"></msg_item>
        </div>
      </oeui-list>
    </div>
  </div>
  <tabbars />
</template>

<script>
export default {
  name: 'Msg',
}
</script>
<script setup>
import { ref, getCurrentInstance } from 'vue'
import tabbars from '@/components/tabbars.vue'
import oeuiList from '@/oeui/list.vue'
import msg_item from './components/msg_item.vue'
import { getMsg } from '@/api/msg.js'

const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const s_readflag = ref(0)
const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')
const getList = (flag, callback) => {
  if (page.value == 0) return
  getMsg({
    page: page.value,
    s_readflag: s_readflag.value,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}
const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}
getList()
</script>

<style lang="scss" scoped>
.msg_nav {
  background: url('~@/assets/images/bg_main.png');
  background-size: cover;
  height: 1.1733rem;
  padding: 0 0.4267rem;
  top: 0;
  position: fixed;
  z-index: 500;
  width: 100vw;
  span {
    font-size: 0.5333rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #2a2546;
    line-height: 0.7467rem;
  }
}
.list {
  padding: 0.1067rem 0.4267rem;
}
.main {
  position: fixed;
  width: 100vw;
  min-height: 100%;
  box-sizing: border-box;
}
</style>

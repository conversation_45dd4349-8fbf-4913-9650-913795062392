<!-- @format -->

<template>
  <div class="item_box" v-if="false">
    <div class="title flex flex_ac flex_jsb">
      <p class="name">{{ item.title }}</p>
      <span class="time">{{ getTime(item.addtime, false, true) }}</span>
    </div>
    <div class="data flex flex_ac" v-if="item.fromunid > 0">
      <p>用户名：</p>
      <span class="head"></span>
      <p>{{ item.from_union.unionname }}({{ item.from_union.unionid }})</p>
    </div>
    <div class="data flex flex_ac" v-else>
      <p style="color: #999">用户名：</p>
      <span class="head"></span>
      <p>用户昵称(13245678999)</p>
    </div>
    <div class="type flex flex_ac flex_jsb">
      <p>注册奖励</p>
      <span>¥{{ item.rw }}</span>
    </div>
  </div>
  <p class="time">{{ item.addtime_t }}</p>
  <div class="item_box">
    <div class="title flex flex_ac">
      <p class="name">{{ item.title }}</p>
    </div>
    <p class="tips">{{ item.msgcont }}</p>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance } from 'vue'
  import { getTime } from '@/utils/hooks.js'
  import { useRouter } from 'vue-router'
  const { proxy } = getCurrentInstance()

  const router = useRouter()

  defineProps({
    item: {
      type: Object,
      required: true
    }
  })
</script>

<style lang="scss" scoped>
  .time {
    font-size: 0.32rem;
    font-weight: normal;
    color: #999999;
    line-height: 0.6133rem;
    font-family: PingFang SC, PingFang SC;
    text-align: center;
    margin-bottom: 0.1333rem;
  }
  .item_box {
    cursor: pointer;
    font-family: PingFang SC, PingFang SC;
    background: #fff;
    padding-top: 0.4267rem;
    border-radius: 0.32rem;
    margin-bottom: 0.32rem;
    .title {
      padding: 0 0.4267rem;
      .name {
        font-size: 0.4267rem;
        font-weight: 500;
        color: #31293b;
        line-height: 0.6133rem;
      }
    }
    .tips,
    .data {
      margin-top: 0.1067rem;
      padding: 0 0.4267rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #666666;
      line-height: 0.6133rem;
    }
    .tips {
      padding-bottom: 0.4267rem;
    }
    .data {
      margin-top: 0.2133rem;
      padding-bottom: 0.2133rem;
      border-bottom: 0.0267rem solid #f2f4f5;
      p {
        position: relative;
        top: 0.0267rem;
      }
      .head {
        width: 0.64rem;
        height: 0.64rem;
        border-radius: 50%;
        overflow: hidden;
        background: red;
        margin-right: 0.1067rem;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
    .type {
      padding: 0.2133rem 0.4267rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #999999;
      line-height: 0.6133rem;
      span {
        font-weight: 500;
        color: #7d68fe;
      }
    }
  }
</style>

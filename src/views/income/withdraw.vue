<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">金额提现</div>
      <span class="more" @click="$router.push('/account')">收款账户</span>
    </div>
    <div class="h44"></div>
    <div class="content_box">
      <p class="tips">可提现金额(元)</p>
      <div class="money">
        <!--<span>¥</span>-->
        <span class="price">{{ unionInfo.income }}</span>
      </div>
      <div class="sum">
        <p class="tip" style="color: #31293b; font-size: 0.3733rem">提现金额</p>
        <div class="nums flex flex_ac">
          <span class="flex_s flag">¥</span>
          <div class="flex_1 flex flex_ac pr">
            <input :class="is_money ? 'color_red' : ''" @input="inputMoney" v-model="money" type="number" style="width: 100%" placeholder="请输入金额" />
            <i v-if="money" @click="clearInput" class="iconfont icon-cuo flex_s pa" style="color: #c5c3c7; right: 0.2667rem"></i>
          </div>
        </div>
        <p class="tip" style="margin-top: 0.2133rem">手续费：¥{{ fee.toFixed(2) }} (每日最多提现{{ state.limit_times }}笔)</p>
      </div>
      <div class="entrance">
        <div :class="is_more ? '' : 'hide'">
          <p class="tip" style="color: #31293b; font-size: 0.3733rem">提现至</p>

          <template v-if="pay_type == 'wxredpack'">
            <div class="item flex flex_ac flex_jsb" @click="changPayType('wxredpack')">
              <span>
                <i class="iconfont icon-weixin vam" style="color: #28c445"></i>
                微信红包
              </span>
              <!-- <div class="flex flex_ac">
                <p></p>
                <i class="iconfont icon-redio_checked current" v-if="pay_type == 'wxredpack'"></i>
                <i class="iconfont icon-rediobtn_nor" v-else></i>
              </div> -->
            </div>
            <p class="tip">注：单笔{{ state.wx_minmoney }}元起，不超过{{ Number(state.wx_maxmoney) }}元</p>
          </template>

          <template v-if="pay_type == 'alipay'">
            <div class="item flex flex_ac flex_jsb" @click="changPayType('alipay')">
              <span>
                <i class="iconfont icon-zhifubao vam" style="color: #06b4fd"></i>
                支付宝
              </span>
              <!-- <div class="flex flex_ac">
                <i class="iconfont icon-redio_checked current" v-if="pay_type == 'alipay'"></i>
                <i class="iconfont icon-rediobtn_nor" v-else></i>
              </div> -->
            </div>
            <p class="tip">注：单笔{{ state.min_money }}元起，不超过{{ Number(state.max_money) }}元，7个工作日审批到账</p>
          </template>

          <template v-if="pay_type == 'bank'">
            <div class="item flex flex_ac flex_jsb" @click="changPayType('bank')">
              <span>
                <em class="vam">
                  <img src="@/assets/images/bank.png" alt="" />
                </em>
                银行卡
              </span>
              <!-- <div class="flex flex_ac">
                <i class="iconfont icon-redio_checked current" v-if="pay_type == 'bank'"></i>
                <i class="iconfont icon-rediobtn_nor" v-else></i>
              </div> -->
            </div>
            <p class="tip">注：单笔{{ state.min_money }}元起，不超过{{ Number(state.max_money) }}元，7个工作日审批到账</p>
          </template>
        </div>
        <div class="more flex_dc" @click="setIsMore" v-if="false">
          <p v-if="is_more">收起更多提现方式</p>
          <p v-else>更多提现方式</p>
          <i class="iconfont icon-shuangjiantou-dowm1" :class="is_more ? 'show' : ''"></i>
        </div>
      </div>
    </div>
  </div>
  <div class="btn" :class="is_next ? 'current' : ''" @click="is_next ? send() : ''">下一步</div>

  <!--未实名-->
  <oe_popup ref="needRz_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" />
      </div>
      <p class="title">提现失败</p>
      <p class="tips">尊敬的用户，您还未进行实名认证，无法进行提现操作</p>
      <div @click="goIdrz" class="event">去认证</div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="needWeixin_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，你还未预留微信相关提现账户信息，完善后方可进行提现操作</p>
      <div
        @click="
          () => {
            proxy.$refs.needWeixin_dialog.close()
            router.push('/account')
          }
        "
        class="event"
      >
        去完善
      </div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="needAlipay_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，你还未预留支付宝相关提现账户信息，完善后方可进行提现操作</p>
      <div
        @click="
          () => {
            proxy.$refs.needAlipay_dialog.close()
            router.push('/account')
          }
        "
        class="event"
      >
        去完善
      </div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>
  <oe_popup ref="needBank_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" alt="" />
      </div>
      <p class="title">温馨提示</p>
      <p class="tips">尊敬的用户，你还未预留银行卡相关提现账户信息，完善后方可进行提现操作</p>
      <div
        @click="
          () => {
            proxy.$refs.needBank_dialog.close()
            router.push('/account')
          }
        "
        class="event"
      >
        去完善
      </div>
      <i @click="proxy.$refs.needRz_dialog.close()" class="close iconfont icon-guanbi2 pa"></i>
    </div>
  </oe_popup>

  <!--失败弹窗-->
  <oe_popup ref="fail_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="fail_dialog">
      <div class="tips_img">
        <img src="@/assets/images/fail_tips.png" />
      </div>
      <p class="title">提现失败</p>
      <p class="tips">{{ fail_result }}</p>
      <div @click="proxy.$refs.fail_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <!--短信验证-->
  <oe_popup ref="noteCodeRz" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="noteCodeRz">
      <p class="title" style="font-weight: 600">短信认证</p>
      <p class="tips">
        提现至
        <em v-if="pay_type == 'wxredpack'">微信红包</em>
        <em v-if="pay_type == 'alipay'">支付宝</em>
        <em v-if="pay_type == 'bank'">银行卡</em>
      </p>
      <p class="price">¥{{ Number(money).toFixed(2) }}</p>
      <div class="mobile flex flex_ac">
        <span>手机号：</span>
        <p>{{ filterMobile(unionInfo.mobile) }}</p>
      </div>
      <div class="code flex flex_ac flex_jsb">
        <div class="flex flex_ac">
          <span class="flex_s" style="color: #666">验证码：</span>
          <input style="width: 2.4rem" type="number" maxlength="6" v-model="mobilecode" placeholder="短信验证码" />
        </div>
        <span class="getcode flex_s" @click="getCode" v-if="codeStatus">获取验证码</span>
        <span class="time flex_s" v-else>({{ codeSecond }}s后重新获取)</span>
      </div>
      <div @click="sendWithdraw" class="event">确定</div>
      <span @click="proxy.$refs.noteCodeRz.close()" class="close iconfont icon-guanbi"></span>
    </div>
  </oe_popup>

  <!--提现结果-->
  <oe_popup ref="withdraw_result" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="withdraw_result">
      <p class="title">提现结果</p>
      <div class="icon flex_dc">
        <i class="iconfont icon-duihao"></i>
      </div>
      <p class="tips" style="margin-top: 0.1067rem">
        提现至
        <em v-if="pay_type == 'wxredpack'">微信红包</em>
        <em v-if="pay_type == 'alipay'">支付宝</em>
        <em v-if="pay_type == 'bank'">银行卡</em>
      </p>
      <p class="price">¥{{ Number(money).toFixed(2) }}</p>
      <p class="tips" style="color: #c5c3c7; margin-bottom: 0.4267rem; margin-top: 0.4267rem">请留意你的提现账户金额变动信息，系统将在三个交易日内完成转账</p>

      <div @click="proxy.$refs.withdraw_result.close()" class="event">确定</div>
      <span @click="proxy.$refs.withdraw_result.close()" class="close iconfont icon-guanbi"></span>
    </div>
  </oe_popup>

  <!--审核中-->
  <oe_popup ref="audit_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="withdraw_result">
      <p class="title">提现结果</p>
      <div class="icon flex_dc">
        <i class="iconfont icon-duihao"></i>
      </div>
      <p class="tips" style="margin-top: 0.1067rem">成功提交申请</p>
      <p class="price">¥{{ Number(money).toFixed(2) }}</p>
      <p class="tips" style="color: #c5c3c7; margin-bottom: 0.4267rem; margin-top: 0.4267rem">请耐心等待管理员审核提现申请，审核通过后系统将在三个工作日内完成转账</p>
      <div @click="proxy.$refs.audit_dialog.close()" class="event">知道了</div>
      <span @click="proxy.$refs.audit_dialog.close()" class="close iconfont icon-guanbi"></span>
    </div>
  </oe_popup>

  <oe_hint ref="hint_dialog" />
  <ver-fication ref="oeVerFicat" @callback="smsCallback"></ver-fication>
</template>

<script setup>
import { ref, getCurrentInstance, computed, watch } from 'vue'
import oe_popup from '@/oeui/popup.vue'
import oe_hint from '@/components/hint.vue'
import verFication from '@/components/verification.vue'
import { initWithdraw, countFee, referWithdraw, getWithdrawQrcode, getWeixinToken, cancelWithdrawAjax } from '@/api/withdraw.js'

import wx from 'weixin-js-sdk'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { filterMobile } from '@/utils/hooks.js'
import { codeSend } from '@/api/login.js'

const store = useStore()
const router = useRouter()

const unionInfo = ref({})
const config = ref({})

const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const state = ref({})
// 初始化
const init = () => {
  initWithdraw().then(res => {
    if (res.ret == 1) {
      state.value = res.result
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_more = ref(false)
const setIsMore = () => {
  is_more.value = !is_more.value
}

const pay_type = ref('')
const changPayType = val => {
  pay_type.value = val
  inputMoney()
}
const clearInput = () => {
  money.value = null
  is_next.value = false
  fee.value = 0
}
const is_next = ref(false)
const money = ref(null)
const timer = ref(null)
const inputMoney = () => {
  is_money.value = false
  clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    if (money.value) computerFee()
  }, 500)
  return
  if (money.value >= state.value.min_money) {
    is_next.value = true
    if (pay_type.value == 'wxredpack') {
      if (state.value.max_money <= 200) {
        if (money.value > state.value.max_money) {
          OEUI.toast({
            text: `最大提现金额为${state.value.max_money}`,
          })
          money.value = state.value.max_money
        }
      } else {
        if (money.value > 200) {
          OEUI.toast({
            text: `最大提现金额为200`,
          })
          money.value = 200
        }
      }
    } else {
      if (money.value > state.value.max_money) {
        OEUI.toast({
          text: `最大提现金额为${state.value.max_money}`,
        })
        money.value = state.value.max_money
      }
    }
    if (money.value >= state.value.min_money) computerFee()
    else fee.value = 0
  } else is_next.value = false
}

// 计算手续费
const fee = ref(0)
const is_money = ref(false)
const computerFee = () => {
  is_next.value = false
  countFee({
    withtype: pay_type.value,
    money: money.value,
  }).then(res => {
    if (res.ret == 1) {
      is_money.value = false
      is_next.value = true
      fee.value = res.result
    } else {
      // money.value = null
      is_money.value = true
      is_next.value = false
      fee.value = 0
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

// 短信验证
const send = () => {
  proxy.$refs.noteCodeRz.open()
}
const mobilecode = ref(null)
const codeSecond = ref(60)
const codeStatus = ref(true)
const smsCallback = code => {
  //防刷回调
  sendCode(code)
}
const getCode = () => {
  //判断是否开启了防刷机制
  if (unionInfo.value.mobile == '') {
    OEUI.toast({
      text: '请填写手机号',
    })
    return
  }
  proxy.$refs.oeVerFicat.start()
}
let time = null
const sendCode = code => {
  //发送验证码
  OEUI.loading.show()

  codeSend({
    type: 'withdraw',
    mobile: unionInfo.value.mobile,
    brushcode: code || '',
  }).then(res => {
    OEUI.loading.hide()
    if (res.ret == 1) {
      OEUI.toast({ text: '发送验证码成功' })
      codeStatus.value = false
      if (code) {
        proxy.$refs.oeVerFicat.success()
      }
      if (time != null) return
      time = setInterval(() => {
        codeSecond.value--
        if (codeSecond.value == 0) {
          clearInterval(time)
          time = null
          codeStatus.value = true
          codeSecond.value = 60
        }
      }, 1000)
    } else {
      if (code) {
        proxy.$refs.oeVerFicat.error()
      }
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

// 提交申请
const fail_result = ref('')
const withid = ref('')
const sendWithdraw = () => {
  if (!mobilecode.value) {
    return OEUI.toast({
      text: '请输入短信验证码',
    })
  }
  proxy.$refs.noteCodeRz.close()
  codeSecond.value = 60
  codeStatus.value = true
  OEUI.loading.show()
  withid.value = ''
  referWithdraw({
    withtype: pay_type.value,
    money: money.value,
    mobilecode: mobilecode.value,
  })
    .then(res => {
      console.log(res)
      mobilecode.value = null
      OEUI.loading.hide()
      store.dispatch('getUserInfo')
      if (res.ret == 1) {
        proxy.$refs.withdraw_result.open()
      } else if (res.ret == 2) {
        proxy.$refs.audit_dialog.open()
      } else if (res.ret == 3) {
        if (res.result == 'need_rz') {
          proxy.$refs.needRz_dialog.open()
        } else if (res.result == 'need_mobilerz') {
          OEUI.modal({
            text: '您还未进行手机号认证，无法进行提现操作',
            cancelShow: false,
            confirmText: '知道了',
            confirm: () => {
              router.back()
            },
          })
        } else if (res.result == 'need_wx') {
          getWithdrawQrcode().then(res => {
            if (res.ret == 1) {
              proxy.$refs.hint_dialog.open({
                title: '提示',
                tips: '尊敬的用户，提现金额将会转到您的微信，须先关注公众号',
                tap: '长按识别二维码关注公众号',
                qrcode: res.result.data,
              })
            } else {
              OEUI.toast({
                text: res.msg || '系统繁忙，请稍后再试',
              })
            }
          })
        } else if (res.result == 'need_wxinfo') {
          proxy.$refs.needWeixin_dialog.open()
        } else if (res.result == 'need_alipay') {
          proxy.$refs.needAlipay_dialog.open()
        } else if (res.result == 'need_bank') {
          proxy.$refs.needBank_dialog.open()
        }
      } else if (res.ret == 4) {
        //微信自动转账
        startWeixinWithdraw(res.result)
      } else {
        fail_result.value = res.msg
        proxy.$refs.fail_dialog.open()
      }
    })
    .catch(() => {
      withid.value = ''
      OEUI.loading.hide()
      OEUI.toast('系统繁忙，请稍后再试')
    })
}

const startWeixinWithdraw = data => {
  withid.value = data.withid
  initWeixinPay().then(() => {
    wx.checkJsApi({
      jsApiList: ['requestMerchantTransfer'],
      success: function (res) {
        if (res.checkResult['requestMerchantTransfer']) {
          WeixinJSBridge.invoke(
            'requestMerchantTransfer',
            {
              mchId: data.mchid,
              appId: data.appId,
              package: data.package_info,
            },
            function (res) {
              if (res.err_msg === 'requestMerchantTransfer:ok') {
                //提现成功
                OEUI.toast('提现成功')
                setTimeout(() => {
                  location.reload()
                }, 500)
              }
              if (res.err_msg === 'requestMerchantTransfer:fail') {
                //提现失败
                OEUI.toast('调整微信失败，请转人工')
                cancelWithdraw()
              }
              if (res.err_msg === 'requestMerchantTransfer:cancel') {
                //取消
                OEUI.toast('取消操作')
                cancelWithdraw()
              }
            },
          )
        } else {
          alert('你的微信版本过低，请更新至最新版本。')
          cancelWithdraw()
        }
      },
      fail: () => {
        cancelWithdraw()
      },
    })
  })
}

const initWeixinPay = () => {
  return new Promise((resolve, inject) => {
    getWeixinToken().then(res => {
      if (res.ret == 1) {
        wx.config({
          debug: false,
          appId: res.result.appid, // 必填，公众号的唯一标识
          timestamp: res.result.timestamp, // 必填，生成签名的时间戳
          nonceStr: res.result.noncestr, // 必填，生成签名的随机串
          signature: res.result.signature, // 必填，签名
          jsApiList: ['checkJsApi'],
        })
        wx.ready(() => {
          resolve()
        })
      }
    })
  })
}

const cancelWithdraw = () => {
  if (!withid.value) return
  cancelWithdrawAjax(withid.value).then(() => {
    location.reload()
  })
}

//去实名
const goIdrz = () => {
  proxy.$refs.needRz_dialog.close()
  if (state.idrz_type == 1) {
    // 三要素
    router.push('/idrz')
  } else {
    // 人工核验
    router.push('/idrz2')
  }
}

init()

watch(
  () => store.state.unionInfo,
  newInfo => {
    if (newInfo) {
      unionInfo.value = newInfo
    }
  },
  { immediate: true, deep: true },
)
watch(
  () => store.state.config,
  newInfo => {
    if (newInfo) {
      pay_type.value = newInfo.union_withtype || 'wxredpack'
      config.value = newInfo
    }
  },
  { immediate: true, deep: true },
)
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'cpDin';
  src: url('~@/assets/font/din.ttf') format('truetype');
}

.font_din {
  font-family: 'cpDin';
}

.main {
  font-family: PingFang SC-Regular, PingFang SC;

  .top_nav {
    background: url('~@/assets/images/bg_main.png') no-repeat;
    background-size: cover;
    height: 1.1733rem;
    top: 0;
    width: 100%;
    z-index: 100;

    span {
      width: 1.1733rem;
      height: 1.1733rem;

      i {
        font-size: 0.64rem;
      }
    }

    .back {
      position: absolute;
      left: 0;
    }

    .search {
      position: absolute;
      right: 0;
    }

    .title {
      font-size: 0.48rem;
      line-height: 0.64rem;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #2a2546;
    }
  }

  .content_box {
    margin-top: 0.4267rem;

    .tips {
      padding: 0 0.2133rem;
      font-size: 0.32rem;
      line-height: 0.4533rem;
      font-weight: normal;
      color: #666;
      text-align: center;
    }

    .money {
      padding: 0 0.2133rem;
      color: #31293b;
      text-align: center;

      em {
        font-size: 0.3733rem;
        line-height: 0.4533rem;
        font-family: DIN Condensed-Bold;
        font-weight: 700;
      }

      .price {
        font-size: 1.1733rem;
        @extend .font_din;
        line-height: 1.4133rem;
      }
    }

    .sum {
      background: rgba(255, 255, 255, 0.5);
      margin-top: 0.5333rem;
      border-radius: 0.64rem 0.64rem 0 0;
      padding: 0.5333rem 0.4267rem;
      padding-bottom: 1.3333rem;

      .tip {
        font-size: 0.32rem;
        line-height: 0.4533rem;
        font-weight: normal;
        color: #999999;
      }

      .nums {
        padding: 0.1067rem 0;
        margin-top: 0.1067rem;
        border-bottom: 0.0267rem solid #f2f4f5;
        box-sizing: border-box;

        .flag {
          font-size: 0.3733rem;
          font-weight: 500;
          color: #31293b;
          line-height: 0.9067rem;
          margin-right: 0.16rem;
          position: relative;
          top: 0.1067rem;
        }

        input {
          font-size: 0.64rem !important;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          color: #31293b;
          line-height: 0.9067rem;

          &::placeholder {
            font-weight: normal;
          }
        }

        .fee {
          margin-left: 0.2667rem;
          color: $color_main;
        }
      }
    }

    .entrance {
      margin-top: -0.6933rem;
      background: #fff;
      border-radius: 0.64rem 0.64rem 0 0;
      padding: 0.5333rem 0.4267rem;
      overflow: hidden;
      box-shadow: 0px -0.1067rem 0.2133rem 0px rgba(0, 16, 117, 0.04);
      min-height: calc(100vh - 7.5467rem);

      > div {
        height: 100%;
        overflow: hidden;

        &.hide {
          height: 2.6667rem;
        }

        .tip {
          font-size: 0.32rem;
          font-weight: normal;
          color: #999;
          line-height: 0.4533rem;
        }

        .item {
          background: #f2f4f5;
          border-radius: 0.32rem;
          padding: 0.32rem;
          margin: 0.2133rem 0;
          margin-top: 0.32rem;

          > span {
            font-size: 0.32rem;
            line-height: 0.4533rem;
            font-weight: normal;
            color: #3d3d3d;

            i {
              font-size: 0.64rem;
              margin-right: 0.1067rem;
            }

            em {
              margin-right: 0.1067rem;
              width: 0.5867rem;
              height: 0.5867rem;
              vertical-align: middle;

              img {
                vertical-align: middle;
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
          }

          > div {
            p {
              font-size: 0.32rem;
              line-height: 0.4533rem;
              font-weight: normal;
              color: #999;
              position: relative;
              top: 0.0267rem;
            }

            i {
              font-size: 0.5867rem;
              margin-left: 0.1067rem;
              position: relative;
              top: 0.0267rem;
              color: #999;

              &.current {
                color: $color_main;
              }
            }
          }
        }
      }
    }

    .more {
      margin-top: 0.4533rem;
      font-size: 0.32rem;
      line-height: 0.4533rem;
      font-weight: normal;
      color: #999999;

      i {
        transition: all 0.4s;

        &.show {
          transform: rotate(180deg);
        }
      }
    }
  }
}

.btn {
  position: fixed;
  background: #ccc;
  bottom: 0.6933rem;
  z-index: 500;
  width: calc(100vw - 1.0667rem);
  text-align: center;
  color: #fff;
  left: 50%;
  left: 0.5333rem;
  font-size: 0.4267rem;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: normal;
  line-height: 0.5867rem;
  padding: 0.2933rem 0;
  border-radius: 0.5867rem;

  &.current {
    background: $color_main;
  }
}

.noteCodeRz {
  border-radius: 0.64rem 0.64rem 0 0;
  padding: 0.64rem 0.4267rem;

  .title {
    text-align: center;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
  }

  .iconfont {
    margin: 0 auto;
  }

  .tips {
    text-align: center;
    margin-top: 0.5333rem;
    font-size: 0.3733rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    color: #666666;
  }

  .price {
    text-align: center;
    margin-top: 0.1067rem;
    font-size: 0.64rem;
    line-height: 0.9067rem;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #31293b;
  }

  .mobile {
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    color: #666666;
    padding: 0.32rem 0;
  }

  .code {
    padding: 0.32rem 0;
    margin-bottom: 0.5333rem;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;

    .getcode {
      color: #0570f1;
    }

    .time {
      color: #999;
    }
  }

  .event {
    text-align: center;
    background: $color_main;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin: 0 0.7733rem;
    padding: 0.24rem 0;
  }

  .close {
    position: absolute;
    font-size: 0.7467rem;
    color: #fff;
    left: 50%;
    transform: translateX(-50%);
    bottom: -1.3333rem;
  }
}

.withdraw_result {
  border-radius: 0.64rem 0.64rem 0 0;
  padding: 0.64rem 0.4267rem;

  .title {
    text-align: center;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
  }

  .icon {
    margin-top: 0.4267rem;
    height: 1.3867rem;

    i {
      font-size: 1.3867rem;
      color: #15ce7b;
    }
  }

  .tips {
    text-align: center;
    font-size: 0.3733rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    color: #666666;
  }

  .price {
    text-align: center;
    font-size: 0.64rem;
    line-height: 0.9067rem;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #31293b;
  }

  .event {
    text-align: center;
    background: $color_main;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin: 0 0.7733rem;
    padding: 0.24rem 0;
  }

  .close {
    position: absolute;
    font-size: 0.7467rem;
    color: #fff;
    left: 50%;
    transform: translateX(-50%);
    bottom: -1.3333rem;
  }
}

.fail_dialog {
  position: relative;

  .close {
    top: 0.2667rem;
    right: 0.4rem;
    font-size: 0.48rem;
  }
}
</style>

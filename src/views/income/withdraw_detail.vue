<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="nav flex_dc">
        <span @click="goPage('/rwincome')" style="margin-right: 0.64rem">收入</span>
        <span class="current">提现</span>
      </div>
      <span class="search_box flex_dc" @click="$router.push('/withdraw/search')">
        <i class="iconfont icon-sousuo"></i>
      </span>
    </div>
    <div class="h44"></div>
    <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="filter flex flex_ac flex_jsb">
        <div class="flex_dc" @click="changFlag">
          <span>{{ flagList[state.s_flag].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_flag ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changTime">
          <span>{{ timeList[state.s_time].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
        </div>
      </div>
      <div class="list" v-if="list.length">
        <royalty_item :item="item" v-for="item in list" :key="item.withid" />
      </div>
    </oeui-list>
  </div>
  <search_item ref="searchTime" type="time" :list="timeList" :current="state.s_time" @changItem="selectTime" @close="closeItem"></search_item>

  <search_item ref="searchFlag" type="flag" :list="flagList" :current="state.s_flag" @changItem="selectFlag" @close="closeItem"></search_item>
</template>

<script setup>
import { ref, getCurrentInstance, reactive, nextTick } from 'vue'
import oeuiList from '@/oeui/list.vue'
import royalty_item from './components/royalty_item.vue'
import search_item from '@/components/search_itme.vue'
import { useRouter } from 'vue-router'
import { getWithdraw } from '@/api/withdraw.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const router = useRouter()

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')

const timeList = [
  { name: '全部时间', val: 0 },
  { name: '今天', val: 1 },
  { name: '昨天', val: 2 },
  { name: '本周', val: 3 },
  { name: '上周', val: 4 },
  { name: '本月', val: 5 },
  { name: '上月', val: 6 },
  { name: '今年', val: 7 },
  { name: '去年', val: 8 },
]
const flagList = [
  { name: '全部状态', val: 0 },
  { name: '成功', val: 1 },
  { name: '失败', val: 2 },
  { name: '拒绝', val: 3 },
  { name: '待处理', val: 4 },
]

const is_time = ref(false)
const is_flag = ref(false)
const state = reactive({
  s_time: 0,
  s_flag: 0,
})

const changTime = () => {
  proxy.$refs.searchFlag.close()
  is_flag.value = false
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}

const closeItem = () => {
  is_time.value = false
  is_flag.value = false
}

const selectTime = val => {
  is_time.value = false
  state.s_time = val
  page.value = 1
  getList(true)
}

const changFlag = () => {
  proxy.$refs.searchTime.close()
  is_time.value = false
  nextTick(() => {
    if (is_flag.value) {
      is_flag.value = false
      proxy.$refs.searchFlag.close()
    } else {
      is_flag.value = true
      proxy.$refs.searchFlag.open()
    }
  })
}

const selectFlag = val => {
  is_flag.value = false
  state.s_flag = val
  page.value = 1
  getList(true)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getWithdraw({
    page: page.value,
    ...state,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'no_data'
  getList(true, done)
}
const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

const goPage = url => {
  router.replace({ path: url })
}
getList()
</script>

<style lang="scss" scoped>
.main {
  .top_nav {
    background: url('~@/assets/images/bg_main.png') no-repeat;
    background-size: cover;
    height: 1.1733rem;
    top: 0;
    width: 100%;
    z-index: 100;

    span {
      width: 1.1733rem;
      height: 1.1733rem;
      i {
        font-size: 0.64rem;
      }
    }
    .back {
      position: absolute;
      left: 0;
    }
    .search_box {
      position: absolute;
      right: 0;
    }
    .nav {
      line-height: 1.1733rem;
      span {
        text-align: center;
        font-size: 0.3733rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        position: relative;
        top: 0.0533rem;
        &.current {
          font-size: 0.5333rem;
          font-weight: 500;
          color: #2a2546;
          top: 0;
          &::after {
            position: absolute;
            content: '';
            width: 0.32rem;
            height: 0.08rem;
            border-radius: 0.0533rem;
            background: #7d68fe;
            left: 50%;
            transform: translateX(-50%);
            bottom: 0.1067rem;
          }
        }
      }
    }
  }
  .filter {
    padding: 0.2667rem 0.4267rem;
    div {
      flex: 1;
      color: #2a2546;
      text-align: center;
      span {
        margin-right: 0.1333rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }

      .hide {
        transform: rotate(90deg);
      }
      .show {
        transform: rotate(-90deg);
        position: relative;
        top: 0.0267rem;
      }
    }
  }
  .list {
    margin-top: 0.1067rem;
    padding: 0 0.4267rem;
  }
}
</style>

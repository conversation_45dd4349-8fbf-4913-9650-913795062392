<!-- @format -->

<template>
  <div class="content">
    <div class="data flex flex_ac flex_jsb">
      <div class="flex_dc flex_v">
        <span>{{ data.total0 }}</span>
        <p>待审人数(人)</p>
      </div>
      <div class="flex_dc flex_v">
        <span> {{ Number(data.audit_income) >= 10000 ? (Number(data.audit_income) / 10000).toFixed(2) : data.audit_income }}</span>
        <p>审核收益({{ Number(data.audit_income) >= 10000 ? '万' : '元' }})</p>
      </div>
    </div>
    <div style="margin-top: 0.3733rem">
      <list_item @show="showEvent" @audit="auditEvent" :item="item" v-for="(item, index) in list" :key="index" />
    </div>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, inject } from 'vue'

  import list_item from '@/views/index/components/audit_item.vue'
  const { proxy } = getCurrentInstance()

  defineProps({
    list: {
      type: Array
    },
    data: {
      type: Object
    }
  })

  const emits = defineEmits(['select'])

  const event = inject('selectAudit')
  const auditEvent = id => {
    event(id)
  }

  const show = inject('showFailAudit')
  const showEvent = val => {
    show(val)
  }
</script>

<style lang="scss" scoped>
  @font-face {
    font-family: 'cpDin';
    src: url('~@/assets/font/din.ttf') format('truetype');
  }
  .font_din {
    font-family: 'cpDin';
  }
  .content {
    //min-height: calc(100vh - 8.6667rem);
    background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 45%);
    padding: 0 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    .data {
      padding-top: 0.8rem;
      & > div {
        flex: 1;
        span {
          font-size: 0.6933rem;
          line-height: 0.7733rem;
          font-weight: 700;
          color: #31293b;
          @extend .font_din;
        }
        p {
          font-size: 0.32rem;
          line-height: 0.4533rem;
          color: #999;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          margin-top: 0.1067rem;
        }
      }
    }
    .type {
      margin-top: 0.64rem;
      padding-top: 0.32rem;
      padding-bottom: 0.16rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #999999;
      line-height: 0.5333rem;
      span {
        margin-right: 0.64rem;
        &.current {
          font-weight: 500;
          color: #7d68fe;
        }
      }
    }
  }
</style>

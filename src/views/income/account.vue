<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">个人收款账户</div>
    </div>
    <div class="h44"></div>
    <div class="content_box">
      <p class="tips">
        <font color="red">·</font> 请完善收款资料，收款人姓名和收款账号必须一致，以免影响提现。
      </p>

      <div class="weixin_box">
        <div class="tit flex flex_ac flex_jsb">
          <p>微信账户</p>
          <span @click="editWeixin">编辑</span>
        </div>
        <!-- <div class="data flex flex_ac flex_jsb">
          <span class="flex_s">真实姓名</span>
          <span class="content">{{ unionInfo.truename }}</span>
        </div> -->
        <div class="data flex flex_ac flex_jsb">
          <span class="flex_s">微信帐号</span>
          <span class="content">{{ unionInfo.weixin }}</span>
        </div>
        <div class="data flex flex_ac flex_jsb" style="padding-bottom: .2667rem;">
          <span class="flex_s">微信二维码</span>
          <span class="content">
            <div class="oh" style="width: 1.0667rem;height: 1.0667rem;border-radius: .1333rem;">
              <img :src="unionInfo.wxcode_url" class="ob_c">
            </div>
          </span>
        </div>
      </div>
      <div class="alipay_box">
        <div class="tit flex flex_ac flex_jsb">
          <p>支付宝账户</p>
          <span @click="editAlipay">编辑</span>
        </div>
        <div class="data flex flex_ac flex_jsb">
          <span class="flex_s">真实姓名</span>
          <span class="content">{{ unionInfo.alipaycontact }}</span>
        </div>
        <div class="data flex flex_ac flex_jsb">
          <span class="flex_s">支付宝帐号</span>
          <span class="content">{{ unionInfo.alipayaccount }}</span>
        </div>
      </div>
      <div class="card_box pb12">
        <div class="tit flex flex_ac flex_jsb">
          <p class="flex_s">银行卡</p>
          <span @click="editBank">编辑</span>
        </div>
        <div class="card flex flex_ac flex_jsb" v-if="unionInfo.bankaccount">
          <span class="flex_s">{{ unionInfo.bankname }}</span>
          <p class="flex_1 ws" style="margin-left: .2667rem;text-align: right;">{{ unionInfo.bankaccount }}</p>
        </div>
      </div>
    </div>
  </div>

  <oe_popup ref="add_weixin" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="add_card">
      <p class="title">微信账号</p>
      <div class="content">
        <p class="color_red fz12">请务必填写正确的微信号，否则无法正常转账打款</p>
        <div class="item">
          <span class="flex_s">微信号<font color="red">*</font></span>
          <input type="text" maxlength="30" v-model="weixin" placeholder="请填写微信号" />
        </div>
        <div class="item">
          <span class="flex_s">微信二维码</span>
          <div class="oh upload" @click="openHeadimgUp">
            <img v-if="wxcode" :src="wxcode" class="ob_c">
            <i v-else class="iconfont icon-fabu-01"></i>
          </div>
        </div>
      </div>
      <div @click="sendWeixin" class="event">保存</div>
      <span @click="proxy.$refs.add_weixin.close()" class="close iconfont icon-guanbi"></span>
    </div>
  </oe_popup>

  <oe_popup ref="add_card" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="add_card">
      <p class="title">添加银行卡</p>
      <div class="content">
        <div class="item" @click="proxy.$refs.distPicker.open()">
          <span class="flex_s">开户行<font color="red">*</font></span>
          <p>{{ bankname }}</p>
        </div>
        <div class="item">
          <span class="flex_s">支行<font color="red">*</font></span>
          <input type="text" v-model="banksub" placeholder="请填写支行名称" />
        </div>
        <div class="item">
          <span class="flex_s">开户人<font color="red">*</font></span>
          <input type="text" placeholder="请填写开户人" v-model="bankcontact" />
        </div>
        <div class="item">
          <span class="flex_s">银行卡号<font color="red">*</font></span>
          <input type="text" maxlength="50" v-model="bankaccount" placeholder="请填写银行卡号" />
        </div>
      </div>
      <div @click="sendBank" class="event">保存</div>
      <span @click="proxy.$refs.add_card.close()" class="close iconfont icon-guanbi"></span>
    </div>
  </oe_popup>

  <oe_popup ref="add_alipay" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="add_card">
      <p class="title">支付宝账户</p>
      <div class="content">
        <div class="item">
          <span class="flex_s">姓名<font color="red">*</font></span>
          <input type="text" placeholder="请填写支付宝姓名" v-model="alipaycontact" />
        </div>
        <div class="item">
          <span class="flex_s">账号<font color="red">*</font></span>
          <input type="text" maxlength="19" v-model="alipayaccount" placeholder="请填写支付宝账号" />
        </div>
      </div>
      <div @click="sendAlipay" class="event">保存</div>
      <span @click="proxy.$refs.add_alipay.close()" class="close iconfont icon-guanbi"></span>
    </div>
  </oe_popup>

  <oe_picker ref="distPicker" title="请选择开户行" :current="[banktype]" :list="bankList" @confirm="confirmBankName">
  </oe_picker>
  <upload-headimg ref="uploadBox" is_square @callback="uploadHead"></upload-headimg>
</template>

<script setup>
import { ref, getCurrentInstance, computed, nextTick, watch } from 'vue'
import oe_popup from '@/oeui/popup.vue'
import { useStore } from 'vuex'
import oe_picker from '@/oeui/picker.vue'
import { saveweixin, savebank, savealipay } from '@/api/withdraw.js'
import { uploadImage } from '@/api/edit.js'
import uploadHeadimg from '@/components/upload_headimg.vue'
import { identity } from '@vueuse/core'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const unionInfo = ref({})

const bankList = ref([
  {
    i: 0,
    value: 1,
    text: '中国银行'
  },
  {
    i: 1,
    value: 2,
    text: '工商银行'
  },
  {
    i: 2,
    value: 3,
    text: '农业银行'
  },
  {
    i: 3,
    value: 4,
    text: '建设银行'
  },
  {
    i: 4,
    value: 5,
    text: '招商银行'
  },
  {
    i: 5,
    value: 6,
    text: '交通银行'
  }
])
const bankname = ref('请选择开户行')
const banksub = ref('')
const bankcontact = ref('')
const bankaccount = ref('')
const banktype = ref(0)

const alipaycontact = ref('')
const alipayaccount = ref()





const weixin = ref('')
const wxcode = ref('')
const openHeadimgUp = () => {
  //打开图片上传
  proxy.$refs.uploadBox.open(true)

}
const uploadHead = obj => {
  if (!obj.src) return OEUI.toast({ text: '图片获取失败，请检查!' })
  try {
    uploadImage({
      base64img: obj.src,
      module: 'union',
    }).then(res => {
      if (res.ret == 1) {
        wxcode.value = res.result.drawimg
      } else {
        OEUI.toast({ text: '图片上传失败，请检查!' })
      }
    })
  } catch (error) {
    OEUI.toast({ text: '系统繁忙，请检查!' })
  }
}


const editWeixin = () => {
  weixin.value = unionInfo.value.weixin
  wxcode.value = unionInfo.value.wxcode
  nextTick(() => {
    proxy.$refs.add_weixin.open()
  })
}
const editBank = () => {
  bankname.value = unionInfo.value.bankname
  banksub.value = unionInfo.value.banksub
  bankcontact.value = unionInfo.value.bankcontact
  bankaccount.value = unionInfo.value.bankaccount
  nextTick(() => {
    proxy.$refs.add_card.open()
  })
}
const editAlipay = () => {
  alipaycontact.value = unionInfo.value.alipaycontact
  alipayaccount.value = unionInfo.value.alipayaccount
  nextTick(() => {
    proxy.$refs.add_alipay.open()
  })
}


const confirmBankName = obj => {
  bankname.value = obj[0].text
  initBankType(obj[0].text)
}

const initBankType = name => {
  banktype.value = bankList.value.find(v => v.text == name)?.value
}

const is_send = ref(true)
const sendWeixin = () => {
  if (!is_send.value) return
  if (!weixin.value)
    return OEUI.toast({
      text: '请填写微信号'
    })
  is_send.value = false
  saveweixin({
    weixin: weixin.value,
    wxcode:wxcode.value
  }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
      store.commit('setUnionInfo', res.result.info)
      proxy.$refs.add_weixin.close()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试'
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}

const sendBank = () => {
  if (!is_send.value) return
  if (!bankname.value)
    return OEUI.toast({
      text: '请选择开户行'
    })
  if (!banksub.value)
    return OEUI.toast({
      text: '请填写支行名称'
    })
  if (!bankcontact.value)
    return OEUI.toast({
      text: '请填写开户人'
    })
  if (!bankaccount.value)
    return OEUI.toast({
      text: '请填写银行卡号'
    })
  is_send.value = false
  savebank({
    bankname: bankname.value,
    banksub: banksub.value,
    bankcontact: bankcontact.value,
    bankaccount: bankaccount.value
  }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
      store.commit('setUnionInfo', res.result.info)
      proxy.$refs.add_card.close()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试'
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}

const sendAlipay = () => {
  if (!is_send.value) return
  if (!alipaycontact.value)
    return OEUI.toast({
      text: '请填写支付宝账号姓名'
    })
  if (!alipayaccount.value)
    return OEUI.toast({
      text: '请填写支付宝账号'
    })

  is_send.value = false
  savealipay({
    alipaycontact: alipaycontact.value,
    alipayaccount: alipayaccount.value
  }).then(res => {
    if (res.ret == 1) {
      OEUI.toast({ text: '保存成功' })
      store.commit('setUnionInfo', res.result.info)
      proxy.$refs.add_alipay.close()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试'
      })
    }
    setTimeout(() => {
      is_send.value = true
    }, 500)
  })
}
watch(
  () => store.state.unionInfo,
  newInfo => {
    if (newInfo) {
      console.log(newInfo);
      unionInfo.value = newInfo
      nextTick(() => {
        initBankType(newInfo.bankname)
      })
    }
  },
  { immediate: true, deep: true }
)
</script>

<style lang="scss" scoped>
.main {
  position: absolute;
  width: 100vw;
  min-height: 100vh;
  background: #f2f4f5;

  .top_nav {
    background: #f2f4f5;
    height: 1.1733rem;
    top: 0;
    width: 100%;
    z-index: 100;

    span {
      width: 1.1733rem;
      height: 1.1733rem;

      i {
        font-size: 0.64rem;
      }
    }

    .back {
      position: absolute;
      left: 0;
    }

    .search {
      position: absolute;
      right: 0.4267rem;
      background: #7d68fe;
      color: #fff;
      font-size: 0.3467rem;
      line-height: 0.48rem;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: normal;
      padding: 0.16rem 0.4267rem;
      border-radius: 0.4rem;
      cursor: pointer;
    }

    .title {
      font-size: 0.48rem;
      line-height: 0.64rem;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #2a2546;
    }
  }

  .content_box {
    padding: 0.4267rem;

    .tips {
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      color: #999;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: normal;
      margin-bottom: 0.64rem;
    }

    >div {
      background: #fff;
      padding: 0 0.32rem;
      border-radius: 0.32rem;
      margin-bottom: 0.32rem;

      .tit {
        height: 1.1733rem;

        p {
          font-size: 0.32rem;
          line-height: 0.4533rem;
          color: #999;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
        }

        span {
          background: #7d68fe;
          color: #fff;
          font-size: .32rem;
          line-height: .4533rem;
          padding: .1067rem .4rem;
          border-radius: 0.4267rem;
        }
      }

      .data {
        height: 1.1733rem;

        span {
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          color: #666;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
        }

        .content {
          color: #31293b;
          font-size: 0.3733rem;
          line-height: 0.5333rem;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: 500;
        }
      }
    }

    .card_box {
      &.pb12 {
        padding-bottom: 0.32rem;
      }

      .tit {
        margin-bottom: 0.2133rem;
      }

      .card {
        background: #f2f4f5;
        padding: 0.2667rem 0.32rem;
        border-radius: 0.32rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        margin-bottom: 0.32rem;

        p {
          color: #31293b;
          font-weight: 500;
        }

        span {
          color: #666;

          font-weight: normal;
        }
      }

      .card:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.add_card {
  border-radius: 0.64rem 0.64rem 0 0;
  padding: 0.64rem 0.4267rem;

  .title {
    text-align: center;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    color: #3d3d3d;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
  }

  .content {
    margin-top: 0.32rem;
    margin-bottom: 0.4267rem;

    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 1.1733rem;
      font-size: 0.3733rem;
      color: #666;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: normal;

      p,
      input {
        text-align: right;
      }
    }
  }

  .event {
    text-align: center;
    background: #7d68fe;
    color: #fff;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    border-radius: 0.5333rem;
    margin: 0 0.7733rem;
    padding: 0.24rem 0;
  }

  .close {
    position: absolute;
    font-size: 0.7467rem;
    color: #fff;
    left: 50%;
    transform: translateX(-50%);
    bottom: -1.3333rem;
  }
}

.upload {
  width: 1.0667rem;
  height: 1.0667rem;
  border-radius: .1333rem;
  background: #ebebeb;
  display: flex;
  align-items: center;
  justify-content: center;

  .iconfont {
    font-size: .6933rem;
    color: #bbb2b2;
  }
}
</style>

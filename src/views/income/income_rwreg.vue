<!-- @format -->

<template>
  <div class="content">
    <div class="data flex flex_ac flex_jsb">
      <div class="flex_dc flex_v">
        <span>{{ data.total0 }}</span>
        <p>待奖励人数</p>
      </div>
      <div class="flex_dc flex_v">
        <span>{{ data.total1 }}</span>
        <p>已奖励人数</p>
      </div>
      <div class="flex_dc flex_v">
        <span> {{ Number(data.rw_amount) >= 10000 ? (Number(data.rw_amount) / 10000).toFixed(2) : data.rw_amount }}</span>
        <p>奖励金额({{ Number(data.rw_amount) >= 10000 ? '万' : '元' }})</p>
      </div>
    </div>
    <div class="tip flex flex_ac">
      <i class="iconfont icon-tishi1 flex_s"></i>
      <span class="tit flex_s">奖励标准</span>
      <div class="flex flex_ac flex_1">
        <span class="flex flex_ac flex_s">
          <i class="iconfont icon-nanbiao-01"></i>
          <em>{{ power.user_money1 }}元/人</em>
        </span>
        <span class="flex flex_ac flex_s">
          <i class="iconfont icon-nvbiao-01"></i>
          <em>{{ power.user_money2 }}元/人</em>
        </span>
      </div>
    </div>
    <div class="search flex flex_ac flex_jsb">
      <i class="iconfont icon-sousuo"></i>
      <input type="text" v-model="s_name" class="flex_1" placeholder="请输入用户昵称或编号" />
      <span class="btn" @click="searchName">搜索</span>
    </div>
    <div style="margin-top: 0.3733rem">
      <list_item :item="item" v-for="(item, index) in list" :key="index" />
    </div>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, reactive } from 'vue'

  import list_item from './components/rwreg_item.vue'
  import { getMyPower } from '@/api/cp.js'

  const { proxy } = getCurrentInstance()
  const emit = defineEmits(['search'])

  const props = defineProps({
    list: {
      type: Array
    },
    name: {
      type: String,
      default: ''
    },
    data: {
      type: Object
    }
  })

  const s_name = ref(props.name)

  const power = reactive({
    user_money1: 0,
    user_money2: 0
  })
  const getPower = () => {
    getMyPower({
      type: 1
    }).then(res => {
      if (res.ret == 1) {
        power.user_money1 = res.result.data.user_money1
        power.user_money2 = res.result.data.user_money2
      }
    })
  }
  getPower()

  const searchName = () => {
    //if (!s_name.value.length) return
    emit('search', s_name.value)
  }
</script>

<style lang="scss" scoped>
  @font-face {
    font-family: 'cpDin';
    src: url('~@/assets/font/din.ttf') format('truetype');
  }
  .font_din {
    font-family: 'cpDin';
  }
  .content {
    //min-height: calc(100vh - 8.6667rem);
    background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 45%);
    padding: 0 0.4267rem;
    .data {
      padding-top: 0.8rem;
      & > div {
        flex: 1;
        span {
          font-size: 0.6933rem;
          line-height: 0.7733rem;
          font-weight: 700;
          color: #31293b;
          @extend .font_din;
        }
        p {
          font-size: 0.32rem;
          line-height: 0.4533rem;
          color: #999;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          margin-top: 0.1067rem;
        }
      }
    }
    .tip {
      margin-top: 0.4267rem;
      background: rgba(125, 104, 254, 0.06);
      padding: 0.2667rem 0.3733rem;
      border-radius: 0.32rem;
      & > i {
        margin-right: 0.2133rem;
        font-size: 0.4rem;
        position: relative;
        top: 1px;
        font-weight: 500;
        color: #000000;
      }
      .tit {
        font-size: 0.4267rem;
        line-height: 0.5867rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        color: #31293b;
        margin-right: 0.64rem;
      }
      & > div {
        span {
          margin-right: 0.4267rem;
          .icon-nanbiao-01 {
            color: #0570f1;
            margin-right: 0.1067rem;
            position: relative;
            top: 1px;
          }
          .icon-nvbiao-01 {
            margin-right: 0.1067rem;
            color: #fe6897;
            position: relative;
            top: 1px;
          }
          em {
            font-size: 0.4267rem;
            line-height: 0.5867rem;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: normal;
            color: #666666;
          }
        }
      }
    }
    .search {
      margin-top: 0.48rem;
      background: #fff;
      padding: 0.1067rem;
      border-radius: 0.32rem;
      box-shadow: 1px 1px 1px 0px #e1dfdf85;
      i {
        padding-left: 0.1067rem;
        color: #000;
        font-size: 0.48rem;
      }
      input {
        padding: 0 0.2133rem;
      }
      .btn {
        background: #7d68fe;
        color: #fff;
        font-size: 0.3467rem;
        line-height: 0.48rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        padding: 0.1867rem 0.4267rem;
        border-radius: 0.4267rem;
      }
    }
  }
</style>

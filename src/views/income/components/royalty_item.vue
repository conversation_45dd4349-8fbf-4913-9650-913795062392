<!-- @format -->

<template>
  <div class="item_box" v-if="item.withid">
    <div class="tit flex flex_ac flex_jsb">
      <div class="odd">
        <p>{{ item.withno }}</p>
      </div>
      <div class="status color_green" v-if="item.flag == 1">提现成功</div>
      <div class="status color_red" v-else-if="item.flag == 2">提现失败</div>
      <div class="status color_red" v-else-if="item.flag == 3">审核失败</div>
      <div class="status" v-else>待处理</div>
    </div>
    <div class="handle flex flex_ac flex_jsb">
      <p>提现金额</p>
      <span>¥{{ item.total }}</span>
    </div>
    <div class="handle flex flex_ac flex_jsb">
      <p>手续费</p>
      <span>¥{{ item.poundage }}</span>
    </div>
    <div class="handle flex flex_ac flex_jsb">
      <p>提现方式</p>
      <span v-if="item.withtype == 'wxredpack'">微信红包</span>
      <span v-else-if="item.withtype == 'alipay'">支付宝</span>
      <span v-else-if="item.withtype == 'bank'">银行卡</span>
    </div>
    <div class="handle flex flex_ac flex_jsb">
      <p>实际到账金额</p>
      <span>¥{{ item.amount }}</span>
    </div>
    <div class="handle flex flex_ac flex_jsb">
      <p>申请时间</p>
      <span>{{ getTime(item.addtime) }}</span>
    </div>
    <div class="handle flex flex_ac flex_jsb" v-if="item.flag == 1">
      <p>到账时间</p>
      <span>{{ getTime(item.dealtime) }}</span>
    </div>
    <div class="handle flex flex_ac flex_jsb" v-else-if="item.flag == 2">
      <p>退回时间</p>
      <span>{{ getTime(item.reftime) }}</span>
    </div>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance } from 'vue'
  import { getTime } from '@/utils/hooks.js'
  const { proxy } = getCurrentInstance()

  defineProps({
    item: {
      type: Object
    }
  })
</script>

<style lang="scss" scoped>
  .item_box {
    cursor: pointer;
    background: #fff;
    border-radius: 0.32rem;
    margin-bottom: 0.32rem;
    padding: 0 0.32rem;
    .tit {
      height: 1.0667rem;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: normal;
      font-size: 0.3733rem;
      .odd {
        color: #31293b;
      }
      .status {
        color: #999999;
      }
    }
    .handle {
      height: 1.0667rem;
      font-size: 0.3733rem;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: normal;
      p {
        color: #999;
      }
      span {
        color: #31293b;
        em {
          font-size: 0.32rem;
          color: #999;
          vertical-align: bottom;
        }
      }
    }
  }
</style>

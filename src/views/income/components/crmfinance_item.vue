<!-- @format -->

<template>
  <div v-if="item.member.mid" class="item_box" @click="$router.push('/crmuser/detail?type=crm&id=' + item.member.mid)">
    <div class="tit flex flex_ac flex_jsb">
      <div class="time flex flex_ac">
        <i class="iconfont icon-shizhong"></i>
        <p>收款时间：{{ getTime(item?.addtime) }}</p>
      </div>
      <div class="status color_green" v-if="item.settle_flag == 1">已结算</div>
      <div class="status color_red" v-else-if="item.settle_flag == 2">不结算</div>
      <div class="status" v-else-if="item.settle_flag == 3">待结算</div>
    </div>
    <div class="info flex flex_ac flex_jsb">
      <div class="flex_1 flex flex_ac">
        <div class="head">
          <img v-lazy="item?.member.headimg_url" type="user" alt="" />
        </div>
        <div class="flex_1">
          <div class="flex flex_ac">
            <p class="name">{{ item?.member.nickname }}</p>
            <i class="iconfont icon-nanbiao-01" v-if="item?.member.gender == 1"></i>
            <i class="iconfont icon-nvbiao-01" v-else></i>
          </div>
          <p class="id">编号:{{ item?.member.mid }}</p>
        </div>
      </div>
      <span class="money" v-if="item.settle_flag == 1"
        >佣金：+¥<template v-if="item.be_leaderid > 0 && item.leader_rw > 0"> {{ +item.rw + +item.leader_rw }}</template>
        <template v-else>{{ item.rw }}</template>
      </span>
      <span class="money fail" v-else
        >佣金： ¥
        <template v-if="item.be_leaderid > 0 && item.leader_rw > 0"> {{ +item.rw + +item.leader_rw }}</template>
        <template v-else>{{ item.rw }}</template>
      </span>
    </div>
    <div class="handle flex flex_ac flex_jsb" v-if="(item.be_leaderid > 0 && item.leader_rw > 0) || item.rw > 0">
      <div v-if="item.be_leaderid > 0 && item.leader_rw > 0">
        <p>团长佣金</p>
        <p class="value">¥{{ item.leader_rw }}</p>
      </div>
      <div v-if="item.rw > 0">
        <p>线下合作佣金</p>
        <p class="value">¥{{ item.rw }}</p>
      </div>
    </div>
    <div class="handle flex flex_ac flex_jsb">
      <div>
        <p>收款单编号</p>
        <p class="value">{{ item.fnno }}</p>
      </div>
      <div>
        <p>收款金额</p>
        <p class="value">￥{{ item.amount }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance, computed } from 'vue'

  import { getTime } from '@/utils/hooks.js'
  import { useStore } from 'vuex'
  const { proxy } = getCurrentInstance()

  const store = useStore()

  const unionInfo = computed(() => store.state.unionInfo)

  defineProps({
    item: {
      type: Object
    }
  })
</script>

<style lang="scss" scoped>
  .item_box {
    cursor: pointer;
    background: #fff;
    border-radius: 0.32rem;
    margin-bottom: 0.32rem;
    font-family: PingFang SC, PingFang SC;
    .tit {
      padding-top: 0.2667rem;
      padding: 0.2667rem 0.32rem 0 0.32rem;
      .time {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        color: #999999;
        i {
          position: relative;
          top: 0.0267rem;
          margin-right: 0.1067rem;
        }
      }
      .status {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        color: #999999;
      }
    }
    .info {
      border-bottom: 0.0267rem solid #f4f6f7;
      padding: 0.32rem;
      .head {
        width: 1.1733rem;
        height: 1.1733rem;
        border-radius: 0.2133rem;
        overflow: hidden;
        margin-right: 0.32rem;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .id {
        font-size: 0.32rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #666666;
        line-height: 0.4533rem;
      }
      .name {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: 500;
        color: #31293b;
      }
      i {
        position: relative;
        top: 0.0267rem;
        margin-left: 0.1333rem;
      }
      .icon-nanbiao-01 {
        color: #0570f1;
      }
      .icon-nvbiao-01 {
        color: #fe6897;
      }
      .money {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: 500;
        color: #7d68fe;
        em {
          color: #31293b;
        }
        &.fail {
          color: #ccc;
        }
      }
    }
    .handle {
      padding: 0.2133rem 0.32rem;
      p {
        font-size: 0.32rem;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
      .value {
        margin-top: 0.0533rem;
        font-size: 0.3733rem;
        font-weight: normal;
        color: #31293b;
        line-height: 0.5333rem;
      }
    }
  }
</style>

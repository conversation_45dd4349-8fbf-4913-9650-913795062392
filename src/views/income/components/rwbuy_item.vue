<!-- @format -->

<template>
  <div class="item_box" @click="$router.push('/user/detail?id=' + item.from_user.userid)">
    <div class="tit flex flex_ac flex_jsb">
      <div class="time flex flex_ac">
        <i class="iconfont icon-shizhong"></i>
        <p>消费时间：{{ getTime(item?.actiontime) }}</p>
      </div>
    </div>
    <div class="info flex flex_ac flex_jsb">
      <div class="flex_1 flex flex_ac">
        <div class="head">
          <img v-lazy="item?.from_user.headimg_m2_url" type="user" alt="" />
        </div>
        <div class="flex_1">
          <div class="flex flex_ac">
            <p class="name">{{ item?.from_user.username }}</p>
            <i class="iconfont icon-nanbiao-01" v-if="item?.from_user.gender == 1"></i>
            <i class="iconfont icon-nvbiao-01" v-else></i>
          </div>
          <p class="id">编号:{{ item?.from_user.userid }}</p>
        </div>
      </div>
      <span class="money" :class="item.actiontype == 1 ? '' : 'fail'">佣金：{{ item.actiontype == 1 ? '+' : '-' }}¥{{ item.quantity }}</span>
    </div>
    <div class="handle flex flex_ac flex_jsb" v-if="item.logcontent">
      <p>消费业务：{{ item.logcontent }}</p>
    </div>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance } from 'vue'

  import { getTime } from '@/utils/hooks.js'
  const { proxy } = getCurrentInstance()

  defineProps({
    item: {
      type: Object
    }
  })
</script>

<style lang="scss" scoped>
  .item_box {
    font-family: PingFang SC-Regular, PingFang SC;

    cursor: pointer;
    background: #fff;
    border-radius: 0.32rem;
    margin-bottom: 0.32rem;
    .tit {
      padding-top: 0.2667rem;
      padding: 0.2667rem 0.32rem 0 0.32rem;
      .time {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: normal;
        color: #999999;
        i {
          position: relative;
          top: 0.0267rem;
          margin-right: 0.1067rem;
        }
      }
      .status {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: normal;
        color: #999999;
      }
    }
    .info {
      border-bottom: 0.0267rem solid #f4f6f7;
      padding: 0.32rem;
      .head {
        width: 1.1733rem;
        height: 1.1733rem;
        border-radius: .32rem;
        overflow: hidden;
        margin-right: 0.32rem;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .id {
        font-size: 0.32rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #666666;
        line-height: 0.4533rem;
      }
      .name {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: 500;
        color: #31293b;
      }
      i {
        position: relative;
        top: 0.0267rem;
        margin-left: 0.1333rem;
      }
      .icon-nanbiao-01 {
        color: #0570f1;
      }
      .icon-nvbiao-01 {
        color: #fe6897;
      }
      .money {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: 500;
        color: #7d68fe;
        &.fail {
          color: #ed1616;
        }
      }
    }
    .handle {
      padding: 0.32rem;
      p {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: normal;
        color: #3d3d3d;
      }
      span {
        cursor: pointer;
        background: #7d68fe;
        color: #fff;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: normal;
        padding: 0.16rem 0.32rem;
        border-radius: 0.2133rem;
      }
    }
  }
</style>

<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="back" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="nav flex_dc">
        <span class="current" style="margin-right: 0.64rem">收入</span>
        <span @click="goPage('/withdraw/detail')">提现</span>
      </div>
      <span class="search_box flex_dc" @click="$router.push('/rwincome/search')">
        <i class="iconfont icon-sousuo"></i>
      </span>
    </div>
    <div class="h44"></div>
    <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="filter flex flex_ac flex_jsb">
        <div class="flex_dc" @click="changType" v-if="typeList.length">
          <span>{{ typeList.find(v => v.val == state.s_mdtype).name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_type ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changOrderby">
          <span>{{ state.s_orderby == 'id_desc' ? '时间降序' : '时间升序' }}</span>
          <span class="flex_dc flex_v">
            <i :class="state.s_orderby == 'id_desc' ? 'color_c3' : ''" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; transform: rotate(180deg); top: 0.08rem"></i>
            <i :class="state.s_orderby == 'id_desc' ? '' : 'color_c3'" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; bottom: 0.0533rem"></i>
          </span>
        </div>
        <div class="flex_dc" @click="changTime">
          <span>{{ timeList[state.s_time].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
        </div>
      </div>
      <div class="list">
        <rwincome_item :item="item" v-for="(item, index) in list" :key="index" />
      </div>
    </oeui-list>
  </div>

  <search_item ref="searchTime" type="time" :list="timeList" :current="state.s_time" @changItem="selectTime" @close="closeItem"></search_item>
  <search_item ref="searchType" type="type" :list="typeList" :current="state.s_mdtype" @changItem="selectType" @close="closeItem"></search_item>
</template>

<script>
export default {
  name: 'Rwincome',
}
</script>

<script setup>
import { ref, getCurrentInstance, onMounted, reactive, nextTick } from 'vue'
import oeuiList from '@/oeui/list.vue'
import rwincome_item from './components/rwincome_item.vue'
import { useRouter } from 'vue-router'

import search_item from '@/components/search_itme.vue'

import { getRwincome } from '@/api/income.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const router = useRouter()

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')
const typeList = ref([])

const timeList = [
  { name: '全部时间', val: 0 },
  { name: '今天', val: 1 },
  { name: '昨天', val: 2 },
  { name: '本周', val: 3 },
  { name: '上周', val: 4 },
  { name: '本月', val: 5 },
  { name: '上月', val: 6 },
  { name: '今年', val: 7 },
  { name: '去年', val: 8 },
]

const state = reactive({
  s_time: 0,
  s_orderby: 'id_desc',
  s_mdtype: '',
})
const is_time = ref(false)
const is_type = ref(false)

const back = () => {
  if (window.history.length <= 1) {
    router.push('/home')
    return
  } else if (!window.history.state.back) {
    router.replace({ path: '/home' })
  } else {
    router.back()
  }
}

onMounted(() => {
  getList()
})

const changTime = () => {
  proxy.$refs.searchType.close()
  is_type.value = false
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}

const closeItem = () => {
  is_time.value = false
  is_type.value = false
}

const selectTime = val => {
  is_time.value = false
  state.s_time = val
  page.value = 1
  getList(true)
}

const changType = () => {
  proxy.$refs.searchTime.close()
  is_time.value = false
  nextTick(() => {
    if (is_type.value) {
      is_type.value = false
      proxy.$refs.searchType.close()
    } else {
      is_type.value = true
      proxy.$refs.searchType.open()
    }
  })
}

const selectType = val => {
  is_type.value = false
  state.s_mdtype = val
  page.value = 1
  getList(true)
}

const changOrderby = () => {
  if (state.s_orderby == 'id_desc') {
    state.s_orderby = 'id_asc'
  } else {
    state.s_orderby = 'id_desc'
  }
  page.value = 1
  getList(true)
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(null, done)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getRwincome({
    page: page.value,
    ...state,
  }).then(res => {
    if (res.ret == 1) {
      if (page.value == 1) {
        const arr = []
        res.result.typelist.forEach((v, i) => {
          arr.push({
            name: v.text,
            val: v.mark,
          })
        })
        typeList.value = arr
        typeList.value.unshift({ name: '全部类型', val: '' })
      }
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}
const goPage = url => {
  router.replace({ path: url })
}
</script>

<style lang="scss" scoped>
.color_c3 {
  color: #c5c3c7;
}
.main {
  .top_nav {
    background: url('~@/assets/images/bg_main.png') no-repeat;
    background-size: cover;
    height: 1.1733rem;
    top: 0;
    width: 100%;
    z-index: 100;

    span {
      width: 1.1733rem;
      height: 1.1733rem;
      i {
        font-size: 0.64rem;
      }
    }
    .back {
      position: absolute;
      left: 0;
    }
    .search_box {
      position: absolute;
      right: 0;
    }
    .nav {
      line-height: 1.1733rem;
      span {
        text-align: center;
        font-size: 0.3733rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        position: relative;
        top: 0.0533rem;
        &.current {
          font-size: 0.5333rem;
          font-weight: 500;
          color: #2a2546;
          top: 0;
          &::after {
            position: absolute;
            content: '';
            width: 0.32rem;
            height: 0.08rem;
            border-radius: 0.0533rem;
            background: #7d68fe;
            left: 50%;
            transform: translateX(-50%);
            bottom: 0.1067rem;
          }
        }
      }
    }
  }
  .filter {
    padding: 0.2667rem 0.4267rem;
    div {
      flex: 1;
      color: #2a2546;
      text-align: center;
      span {
        margin-right: 0.1333rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        max-width: 1.8667rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .hide {
        transform: rotate(90deg);
      }
      .show {
        transform: rotate(-90deg);
        position: relative;
        top: 0.0267rem;
      }
    }
  }
  .list {
    margin-top: 0.1067rem;
    padding: 0 0.4267rem;
  }
}
</style>

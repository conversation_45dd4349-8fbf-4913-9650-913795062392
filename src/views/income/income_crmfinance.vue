<!-- @format -->

<template>
  <div class="content">
    <div class="data flex flex_ac flex_jsb">
      <div class="flex_dc flex_v">
        <span>{{ data.total_orders }}</span>
        <p>合同数</p>
      </div>
      <div class="flex_dc flex_v">
        <span> {{ Number(data.crmfinance_amount) >= 10000 ? (Number(data.crmfinance_amount) / 10000).toFixed(2) : data.crmfinance_amount }}</span>
        <p>累计收款({{ Number(data.crmfinance_amount) >= 10000 ? '万' : '元' }})</p>
      </div>
      <div class="flex_dc flex_v">
        <span> {{ Number(data.crmorder_income) >= 10000 ? (Number(data.crmorder_income) / 10000).toFixed(2) : data.crmorder_income }}</span>
        <p>累计分成({{ Number(data.crmorder_income) >= 10000 ? '万' : '元' }})</p>
      </div>
    </div>
    <div style="margin-top: 0.64rem">
      <list_item :item="item" v-for="(item, index) in list" :key="index" />
    </div>
  </div>
</template>

<script setup>
  import { ref, getCurrentInstance } from 'vue'

  import list_item from '@/views/income/components/crmfinance_item.vue'
  const { proxy } = getCurrentInstance()

  defineProps({
    list: {
      type: Array
    },
    data: {
      type: Object
    }
  })
</script>

<style lang="scss" scoped>
  @font-face {
    font-family: 'cpDin';
    src: url('~@/assets/font/din.ttf') format('truetype');
  }
  .font_din {
    font-family: 'cpDin';
  }
  .content {
    //min-height: calc(100vh - 8.6667rem);
    background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 45%);
    padding: 0 0.4267rem;
    font-family: PingFang SC, PingFang SC;
    .data {
      padding-top: 0.8rem;
      & > div {
        flex: 1;
        span {
          font-size: 0.6933rem;
          line-height: 0.7733rem;
          font-weight: 700;
          color: #31293b;
          @extend .font_din;
        }
        p {
          font-size: 0.32rem;
          line-height: 0.4533rem;
          color: #999;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          margin-top: 0.1067rem;
        }
      }
    }
    .type {
      margin-top: 0.64rem;
      padding-top: 0.32rem;
      padding-bottom: 0.16rem;
      font-size: 0.3733rem;
      font-weight: normal;
      color: #999999;
      line-height: 0.5333rem;
      span {
        margin-right: 0.64rem;
        &.current {
          font-weight: 500;
          color: #7d68fe;
        }
      }
    }
  }
</style>

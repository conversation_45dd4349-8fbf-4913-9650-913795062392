<template>
  <div class="main">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="nav"></div>
    <div class="head">
      <img src="@/assets/images/loveeval/lottery.png" />
    </div>
    <div class="content">
      <div class="info">
        <p class="title">月老灵签</p>
        <div class="monery flex flex_ac">
          <div class="data">佣金：¥{{ lotteryData.commission }}</div>
          <span class="price">测评费用：¥{{ lotteryData.price }}</span>
        </div>
      </div>
      <div class="bmdata">
        <div class="tit flex flex_ac flex_jsb">
          <div class="flex flex_ac">
            <span class="fb">已参与评测：</span>
            <p>
              <em>{{ lotteryData.usercount }}</em>
              人
            </p>
          </div>
        </div>
        <div class="nums flex flex_ac" v-if="lotteryData.usercount">
          <template v-for="(item, index) in lotteryData.userlist" :key="item.userid">
            <span v-if="index < 7">
              <img v-lazy="item?.headimg_0" type="user" />
            </span>
          </template>
        </div>
        <p v-else class="color_9" style="margin-top: 0.2667rem">暂无报名</p>
      </div>
      <div class="intro flex flex_ac flex_jsb">
        <div class="flex flex_ac flex_1 ws">
          <i class="iconfont icon-tishi"></i>
          <span>佣金说明：</span>
          <p class="ws">推广用户报名参与活动/互选后且成功支付相关费用</p>
        </div>
        <span class="flex_s" @click="showIntro">查看</span>
      </div>
      <!--<div class="detail">
        <p class="tit">活动详情</p>
        <div class="value" v-if="info.content" v-html="info.content"></div>
        <div class="value" v-else>暂无详情</div>
      </div>-->
    </div>
    <div class="h80"></div>
    <div class="btn flex_dc">
      <span class="flex_dc" @click="btnShare">
        <i class="iconfont icon-fenxiangfangshi"></i>
        推广分享
      </span>
    </div>
  </div>

  <oe_popup ref="intro_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">佣金说明</p>
      <p class="tip">推广用户报名参与活动/互选后且成功支付相关费用，系统根据对应的平台用户报名费用将对应佣金在活动/互选结束后结算至绑定推广红娘账户。如系统设置用户报名费用为0元，则推广红娘对应获取佣金也为0元。</p>
      <div @click="proxy.$refs.intro_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>
</template>
<script>
export default {
  name: 'Lottery',
}
</script>
<script setup>
import { ref, getCurrentInstance, computed } from 'vue'
import { initLoveeval } from '@/api/loveeval.js'
import oe_popup from '@/oeui/popup.vue'
import { useStore } from 'vuex'

const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()
const config = computed(() => store.state.config)
const unionInfo = computed(() => store.state.unionInfo)
const lotteryData = ref('')
const init = () => {
  initLoveeval()
    .then(res => {
      if (res.ret == 1) {
        res.result = res.result || []
        lotteryData.value = res.result.find(v => v.tag == 'lottery') || ''
      } else {
        OEUI.toast({ text: res.msg || '加载数据失败，请检查' })
      }
    })
    .catch(() => {
      OEUI.toast({ text: '加载数据失败，请检查' })
    })
}

const copy = data => {
  let elInput = document.createElement('input')
  elInput.value = data
  document.body.appendChild(elInput)
  elInput.select()
  document.execCommand('Copy')
  OEUI.modal({
    title: '复制成功',
    text: '分享链接复制成功,你可以将链接发给您的微信好友!',
    cancelShow: false,
    confirm: () => {},
  })
  elInput.remove()
}

const btnShare = () => {
  const shareUrl = config.value.siteurl + 'index.php?m=wap&c=lottery&tg_unionid=' + unionInfo.value.unionid
  copy(shareUrl)
}

init()

const showIntro = () => {
  proxy.$refs.intro_dialog.open()
}
</script>

<style lang="scss" scoped>
.main {
  position: relative;
  background: #f2f4f5;
  width: 100%;
  min-height: 100vh;
  .back {
    width: 0.64rem;
    height: 0.64rem;
    border-radius: 0.1067rem;
    background: #00000081;
    color: #fff;
    position: fixed;
    top: 0.2667rem;
    left: 0.4267rem;
    z-index: 300;
  }
  .nav {
    font-size: 0.48rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    color: #2a2546;
    line-height: 1.1733rem;
    text-align: center;
    position: absolute;
    width: 100%;
  }
  .head {
    height: 4.84rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .content {
    padding: 0 0.2667rem;
    margin-top: -0.5333rem;
    position: relative;
    z-index: 100;
    .info {
      background: #fff;
      padding: 0.4267rem;
      border-radius: 0.32rem;
      .title {
        font-size: 0.48rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        color: #31293b;
        line-height: 0.6667rem;
      }
      .monery {
        margin-top: 0.32rem;
        font-family: PingFang SC-Regular, PingFang SC;
        .data {
          color: $color_main;
          font-size: 0.48rem;
          line-height: 0.6667rem;
          font-weight: 500;
        }
        .price {
          color: #999;
          margin-left: 0.4267rem;
          font-size: 0.32rem;
          line-height: 0.4533rem;
          font-weight: normal;
        }
      }
    }
    .bmdata {
      margin-top: 0.32rem;
      background: #fff;
      padding: 0.4267rem;
      border-radius: 0.32rem;
      font-family: PingFang SC, PingFang SC;
      .tit {
        > div {
          span {
            font-size: 0.4267rem;
            font-weight: 500;
            color: #31293b;
            line-height: 0.5867rem;
            font-weight: 600;
          }
          p {
            font-size: 0.3733rem;
            font-weight: normal;
            color: #31293b;
            position: relative;
            top: 0.0267rem;
          }
          em {
            color: $color_main;
          }
        }
        > span {
          font-size: 0.3733rem;
          font-weight: normal;
          color: #666666;
          line-height: 0.5333rem;
          i {
            position: relative;
            top: 0.0267rem;
          }
        }
      }
      .nums {
        margin-top: 0.4267rem;
        > span:first-child {
          margin-left: 0;
        }
        span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 1.3333rem;
          height: 1.3333rem;
          border: 0.0667rem solid #fff;
          background: #fff;
          border-radius: 50%;
          overflow: hidden;
          box-sizing: border-box;
          margin-left: -0.2667rem;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          i {
            color: #000;
            font-size: 0.5333rem;
          }
        }
      }
    }
    .intro {
      margin-top: 0.32rem;
      background: #fff;
      border-radius: 0.32rem;
      padding: 0.4267rem 0.32rem;
      font-family: PingFang SC, PingFang SC;
      > div {
        margin-right: 0.4rem;
        font-size: 0.3733rem;
        font-weight: 400;
        line-height: 0.5333rem;
        i {
          color: #000;
          margin-right: 0.2133rem;
          position: relative;
          top: 0.0267rem;
        }
        span {
          color: #31293b;
        }
        p {
          color: #666666;
        }
      }

      > span {
        cursor: pointer;
        background: $color_main;
        color: #fff;
        font-size: 0.3733rem;
        font-weight: normal;
        line-height: 0.5333rem;
        padding: 0.0533rem 0.32rem;
        border-radius: 0.32rem;
      }
    }
    .detail {
      margin-top: 0.32rem;
      font-family: PingFang SC, PingFang SC;
      .tit {
        padding: 0.2667rem;
        font-size: 0.48rem;
        font-weight: 500;
        color: #2a2546;
        line-height: 0.6667rem;
      }
    }
  }
  .btn {
    position: fixed;
    width: 100%;
    height: 1.6rem;
    bottom: 0.2667rem;
    z-index: 300;
    font-family: PingFang SC, PingFang SC;
    span {
      i {
        margin-right: 0.2133rem;
        font-size: 0.48rem;
      }
      width: fit-content;
      cursor: pointer;
      background: $color_main;
      font-size: 0.4267rem;
      font-weight: normal;
      color: #ffffff;
      line-height: 0.5867rem;
      padding: 0.2667rem 0.64rem;
      border-radius: 0.5867rem;
      margin: 0 0.8533rem;
    }
  }
}
</style>

<style lang="scss">
.detail {
  .value {
    padding: 0 0.2667rem;
    margin-top: 0.32rem;
    font-size: 0.3733rem;
    line-height: 0.64rem;
    color: #2a2546;
    font-weight: normal;
    white-space: pre-line;
    white-space: normal;
    word-break: break-word;

    img {
      margin: 0.2667rem 0;
      max-width: 100%;
      object-fit: cover;
      border-radius: 0.32rem;
    }
  }
}
</style>

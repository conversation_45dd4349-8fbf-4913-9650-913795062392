<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">测评素材</div>
    </div>
    <div class="content">
      <div class="list flex flex_v">
        <div v-if="config.loveeval_tag == 'lottery'" @click="$router.push('/lottery')" class="item">
          <div class="img">
            <img src="@/assets/images/loveeval/lottery.png" />
            <div class="mask"></div>
            <p class="title">月老灵签</p>
            <p class="tips">情感类</p>
          </div>
          <div class="box">
            <div class="flex flex_ac line">
              <span class="price">佣金：¥{{ lotteryData.commission }}</span>
              <span class="price1">测评费用：¥{{ lotteryData.price }}</span>
            </div>
            <div class="join">
              <div class="people" v-if="lotteryData.usercount">
                <div class="users">
                  <template v-for="(v, i) in lotteryData.userlist" :key="i">
                    <span v-if="i < 5">
                      <img v-lazy="v.headimg_0" type="user" />
                    </span>
                  </template>
                  <span></span>
                </div>
                <span class="nums">已报名：{{ lotteryData.usercount }}</span>
              </div>
              <span v-else></span>
              <span class="btn">邀请报名</span>
            </div>
          </div>
        </div>
        <div v-if="config.loveeval_tag == 'bazi'" @click="$router.push('/bazi')" class="item">
          <div class="img">
            <img src="@/assets/images/loveeval/bazi.png" />
            <div class="mask"></div>
            <p class="title">八字姻缘</p>
            <p class="tips">情感类</p>
          </div>
          <div class="box">
            <div class="flex flex_ac line">
              <span class="price">佣金：¥{{ baziData.commission }}</span>
              <span class="price1">测评费用：¥{{ baziData.price }}</span>
            </div>
            <div class="join">
              <div class="people" v-if="baziData.usercount">
                <div class="users">
                  <template v-for="(v, i) in baziData.userlist" :key="i">
                    <span v-if="i < 5">
                      <img v-lazy="v.headimg_0" type="user" />
                    </span>
                  </template>
                  <span></span>
                </div>
                <span class="nums">已报名：{{ baziData.usercount }}</span>
              </div>
              <span v-else></span>
              <span class="btn">邀请报名</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Loveeval',
}
</script>
<script setup>
import { ref, getCurrentInstance, computed } from 'vue'
import { useStore } from 'vuex'
import { initLoveeval } from '@/api/loveeval.js'
const { proxy } = getCurrentInstance()
const store = useStore()
const config = computed(() => store.state.config)

const lotteryData = ref('')
const baziData = ref('')

const init = () => {
  initLoveeval()
    .then(res => {
      if (res.ret == 1) {
        res.result = res.result || []
        lotteryData.value = res.result.find(v => v.tag == 'lottery') || ''
        baziData.value = res.result.find(v => v.tag == 'bazi') || ''
      } else {
        OEUI.toast({ text: res.msg || '加载数据失败，请检查' })
      }
    })
    .catch(() => {
      OEUI.toast({ text: '加载数据失败，请检查' })
    })
}

init()
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.content {
  width: 100%;
  position: absolute;
  top: 1.1733rem;
  .list {
    padding: 0.1067rem 0.4267rem;
    .item {
      border-radius: 0.32rem;
      overflow: hidden;
      font-family: PingFang SC;
      margin-bottom: 0.32rem;
      .img {
        height: 4.88rem;
        width: 100%;
        position: relative;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .mask {
          position: absolute;
          width: 100%;
          height: 1.6rem;
          left: 0;
          bottom: 0;
          background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 97%);
        }
        .title {
          position: absolute;
          left: 0.32rem;
          bottom: 0.4267rem;
          color: #fff;
          font-size: 0.5333rem;
          font-weight: 600;
          line-height: 0.7467rem;
          z-index: 10;
        }
        .tips {
          position: absolute;
          right: 0.32rem;
          top: 0.32rem;
          color: #fff;
          font-size: 0.32rem;
          line-height: 0.4533rem;
          z-index: 10;
        }
      }
      .box {
        background: #fff;
        padding: 0.32rem;
        .line {
          border-bottom: 1px solid #f2f4f5;
          padding-bottom: 0.32rem;
          .price {
            color: $color_main;
            font-size: 0.48rem;
            line-height: 0.6667rem;
          }
          .price1 {
            color: #999;
            font-size: 0.32rem;
            line-height: 0.4533rem;
            font-weight: normal;
            margin-left: 0.4267rem;
          }
        }
        .join {
          padding-top: 0.32rem;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .people {
            display: flex;
            align-items: center;
            .users {
              display: flex;
              align-items: center;
              padding-left: 0.2133rem;
              span {
                width: 0.5867rem;
                height: 0.5867rem;
                border-radius: 50%;
                margin-left: -0.2667rem;
                border: 0.0533rem solid #fff;
                box-sizing: border-box;
                overflow: hidden;
                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }
              span:last-child {
                background: #fff;
              }
            }
            .silder {
              margin-left: -0.2667rem;
              width: 2.0267rem;
              height: 0.2133rem;
              background: #f2f4f5;
              border-radius: 0.1067rem;
              position: relative;
              div {
                position: absolute;
                height: 100%;
                background: #7d68fe;
                border-radius: 0.1067rem;
              }
            }
            .nums {
              margin-left: -0.2133rem;
              color: #3d3d3d;
              font-size: 0.32rem;
              line-height: 0.4533rem;
            }
          }
          .btn {
            background: #7d68fe;
            color: #fff;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: normal;
            font-size: 0.3733rem;
            line-height: 0.5333rem;
            padding: 0.16rem 0.4267rem;
            border-radius: 0.4267rem;
            &.fail {
              background: #ccc;
            }
          }
        }
      }
    }
  }
}
</style>

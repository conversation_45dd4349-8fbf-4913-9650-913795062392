<!-- @format -->

<template>
  <div class="pf top_nav flex_dc">
    <span @click="$router.back()" class="back flex_dc">
      <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
    </span>
    <div class="title flex_dc">主动推荐</div>
  </div>
  <div class="h44"></div>
  <div class="main">
    <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
      <div class="filter flex_dc">
        <div class="flex_dc" @click="changTime">
          <span>{{ timeList[state.s_time].name }}</span>
          <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
        </div>
        <div class="flex_dc" @click="changOrderby">
          <span>{{ state.s_orderby == 'matchtime_desc' ? '时间降序' : '时间升序' }}</span>
          <span class="flex_dc flex_v">
            <i :class="state.s_orderby == 'matchtime_desc' ? 'color_c3' : ''" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; transform: rotate(180deg); top: 0.08rem"></i>
            <i :class="state.s_orderby == 'matchtime_desc' ? '' : 'color_c3'" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; bottom: 0.0533rem"></i>
          </span>
        </div>
      </div>
      <div class="list">
        <template v-if="list.length">
          <div class="item_box" v-for="item in list" :key="item.afterunid">
            <p class="time">{{ item.match_lasttime_t }}</p>
            <div class="remark">
              <i class="iconfont icon-black-board130"></i>
              红娘荐语:
              {{ item.match_remark || '我为你找到了一些合适的异性用户，看看吧！' }}
            </div>
            <div class="info" @click="$router.push('/afteruser/detail?id=' + item.userid)">
              <span>推荐用户：</span>
              <span class="head">
                <img v-lazy="item.headimg_m1_url" type="user" alt="" />
              </span>
              <span>{{ item.username }}</span>
              <span>(编号:{{ item.userid }})</span>
            </div>
            <div class="info" @click="$router.push('/match/log?id=' + item.userid)">
              <span>推荐异性：</span>
              <span>{{ item.match_nums }}位</span>
              <span class="more">
                查看
                <i class="iconfont icon-youshuangjiantou"></i>
              </span>
            </div>
          </div>
        </template>
        <div v-else-if="!list.length && listStatus != 'no_data'" class="list_skelecton flex flex_ac">
          <div class="head"></div>
          <div class="flex_1">
            <p></p>
            <span></span>
          </div>
        </div>
      </div>
    </oeui-list>
  </div>

  <search_item ref="searchTime" type="time" :list="timeList" :current="state.s_time" @changItem="selectTime" @close="closeItem"></search_item>
</template>

<script>
export default {
  name: 'MyAfterUser',
}
</script>

<script setup>
import { ref, getCurrentInstance, reactive, onMounted, nextTick } from 'vue'
import oeuiList from '@/oeui/list.vue'
import search_item from '@/components/search_itme.vue'
import { getAfterUser } from '@/api/afteruser.js'
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')

const timeList = [
  { name: '全部时间', val: 0 },
  { name: '今天', val: 1 },
  { name: '昨天', val: 2 },
  { name: '本周', val: 3 },
  { name: '上周', val: 4 },
  { name: '本月', val: 5 },
  { name: '上月', val: 6 },
  { name: '今年', val: 7 },
  { name: '去年', val: 8 },
]

const state = reactive({
  s_time: 0,
  s_orderby: 'matchtime_desc',
  s_match: 1,
  s_timetype: 'matchtime',
})
const is_time = ref(false)

onMounted(() => {
  getList(true)
})

const changOrderby = () => {
  if (state.s_orderby == 'matchtime_desc') {
    state.s_orderby = 'matchtime_asc'
  } else {
    state.s_orderby = 'matchtime_desc'
  }
  page.value = 1
  //list.value = []
  //listStatus.value = 'loading'
  getList(true)
}
const changTime = () => {
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}

const selectTime = val => {
  is_time.value = false
  state.s_time = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const closeItem = () => {
  is_time.value = false
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getAfterUser({
    page: page.value,
    ...state,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}

.main {
  font-family: PingFang SC-Regular, PingFang SC;
  padding: 0.0533rem 0.4267rem;

  .filter {
    height: 1.0667rem;
    border-radius: 0.32rem;
    margin-bottom: 0.32rem;
    background: rgba(255, 255, 255, 0.5);

    div {
      flex: 1;
      color: #2a2546;
      text-align: center;

      span {
        margin-right: 0.1333rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }

      .color_c3 {
        color: #c5c3c7;
      }

      .hide {
        transform: rotate(90deg);
      }

      .show {
        transform: rotate(-90deg);
        position: relative;
        top: 0.0267rem;
      }
    }
  }

  .list {
    padding: 0.1067rem;

    .num {
      margin: 0.4267rem 0;
      border-radius: 0.32rem;
      padding: 0.32rem;
      font-size: 0.3733rem;
      font-weight: normal;
      line-height: 0.5333rem;
      font-family: PingFang SC, PingFang SC;
      background: rgba(255, 255, 255, 0.32);
      border: 0.0267rem solid #ffffff;

      em {
        font-weight: 600;
      }
    }

    .item_box {
      font-family: PingFang SC, PingFang SC;
      margin-bottom: 0.32rem;
      background: #fff;
      padding: 0.32rem;
      border-radius: 0.32rem;

      .time {
        font-size: 0.32rem;
        text-align: center;
        line-height: 0.5333rem;
        font-weight: normal;
        color: #999;
      }

      .remark {
        margin-top: 0.1333rem;

        i {
          font-size: 0.5333rem;
          vertical-align: bottom;
        }

        font-size: 0.32rem;
        line-height: 0.5333rem;
        color: $color_main;
        font-weight: normal;
      }

      .info {
        display: flex;
        align-items: center;
        margin-top: 0.2667rem;

        .head {
          width: 0.64rem;
          height: 0.64rem;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 0.1333rem;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        span {
          font-size: 0.32rem;
          line-height: 0.64rem;
          font-weight: normal;
          color: #333;
          margin-right: 0.08rem;
        }

        .more {
          display: flex;
          align-items: center;
          color: $color_main;
          margin-left: 0.2667rem;

          i {
            position: relative;
            top: 0.0267rem;
          }
        }
      }
    }
  }
}
</style>

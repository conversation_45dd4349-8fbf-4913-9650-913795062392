<!-- @format -->

<template>
  <div class="mian">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">用户资料</div>
    </div>
    <div class="h44"></div>
    <div class="content">
      <div class="user flex flex_ac">
        <div class="head flex_s">
          <img v-lazy="infoData.headimg_m1_url" type="user" v-if="infoData.headimg" />
          <template v-else>
            <img v-if="infoData.gender == 1" src="@/assets/images/gender_1.png" alt="" />
            <img v-else-if="infoData.gender == 2" src="@/assets/images/gender_2.png" />
            <img v-else src="@/assets/images/gender_0.png" />
          </template>
        </div>
        <div class="flex_1">
          <p class="name">{{ infoData.username }}</p>
          <div class="flex flex_ac flex_jsb" style="margin-top: 0.0533rem">
            <div class="id flex flex_ac">
              <i class="iconfont icon-nanbiao-01" v-if="infoData.gender == 1"></i>
              <i class="iconfont icon-nvbiao-01" v-else></i>
              编号: {{ infoData.userid }}
            </div>
            <p class="addtime">注册时间：{{ getTime(infoData.addtime, true) }}</p>
          </div>
        </div>
      </div>
      <div class="lab flex flex_ac">
        <span @click="changView('info')" :class="is_active == 'info' ? 'current' : ''">基本资料</span>
        <span @click="changView('cond')" :class="is_active == 'cond' ? 'current' : ''">择偶条件</span>
      </div>
      <div>
        <user_info :item="infoData" v-if="is_active == 'info'" />
        <user_cond :item="infoCond" v-if="is_active == 'cond'" />
      </div>
    </div>
  </div>
  <div class="h70"></div>
  <div class="share flex_dc" v-if="infoData.match_nums > 0">
    <span @click="$router.push('/match/log?id=' + infoData.userid)" class="flex_dc flex_s"
      style="margin-right: 0.5333rem">
      <i>
        <img src="@/assets/images/btn_after_log.png" />
      </i>
      推荐记录
    </span>
    <span @click="$router.push('/match/result?id=' + infoData.userid)" class="flex_dc flex_s">
      <i>
        <img src="@/assets/images/btn_after_match.png" />
      </i>
      再次推荐
    </span>
  </div>
  <div class="share flex_dc" v-else>
    <span class="flex_dc flex_s" @click="$router.push('/match/result?id=' + infoData.userid)">
      <i>
        <img src="@/assets/images/btn_after_match.png" a />
      </i>
      开始推荐
    </span>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import { getAfterUserDetail } from '@/api/afteruser.js'
import { getTime } from '@/utils/hooks.js'
import user_info from '@/views/user/info.vue'
import user_cond from '@/views/user/cond.vue'

const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const store = useStore()

const route = useRoute()
const infoData = ref({})
const infoCond = ref({})

const getDetail = () => {
  getAfterUserDetail({
    id: route.query.id,
  }).then(res => {
    if (res.ret == 1) {
      infoData.value = res.result.data
      infoCond.value = res.result.cond
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const is_active = ref('info')

const changView = val => {
  is_active.value = val
}

getDetail()
</script>

<style lang="scss" scoped>
.mian {
  .top_nav {
    background: url('~@/assets/images/bg_main.png') no-repeat;
    background-size: cover;
  }

  .content {
    padding: 0 0.4267rem;

    .user {
      padding-left: 0.2133rem;

      .head {
        width: 1.3867rem;
        height: 1.3867rem;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 0.32rem;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .name {
        font-size: 0.4267rem;
        line-height: 0.5867rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        color: #31293b;
      }

      .id {
        font-size: 0.32rem;
        line-height: 0.4533rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #666666;

        i {
          position: relative;
          font-size: 0.4267rem;
          margin-right: 0.0533rem;
        }

        .icon-nanbiao-01 {
          color: #0570f1;
        }

        .icon-nvbiao-01 {
          color: #fe6897;
        }
      }

      .addtime {
        font-size: 0.32rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
      }
    }

    .lab {
      margin-top: 0.4267rem;
      margin-bottom: 0.2133rem;
      height: 1.1733rem;

      span {
        margin-right: 0.64rem;
        font-size: 0.3733rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #666666;
        line-height: 0.5333rem;
        position: relative;
        top: 2px;

        &.current {
          font-size: 0.48rem;
          font-weight: 500;
          color: #3d3d3d;
          line-height: 0.6667rem;
          top: 0;
        }
      }
    }
  }
}

.share {
  position: fixed;
  width: 100%;
  height: 1.6rem;
  bottom: 0.2667rem;
  z-index: 300;
  padding: 0 0.8rem;
  box-sizing: border-box;

  span {
    width: 50%;
    cursor: pointer;

    i {
      margin-right: .1333rem;
      width: .64rem;
      height: .64rem;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    background: $color_main;
    font-size: 0.4267rem;
    font-family: PingFang SC,
    PingFang SC;
    font-weight: normal;
    color: #ffffff;
    line-height: 0.5867rem;
    padding: 0.2933rem 0;
    border-radius: 0.5867rem;
  }
}
</style>

<!-- @format -->

<template>
  <oeui-popup class="popSearch" ref="popSearch" width="85%">
    <div class="main">
      <div class="h44 flex_dc top">
        <span class="title">匹配条件</span>
        <span @click="proxy.$refs.popSearch.close()" class="back flex_dc iconfont icon-guanbi2"></span>
      </div>
      <div class="content">
        <div class="list">
          <div class="item" @click="openItemPicker('condAge')">
            <span class="name">年龄范围</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.cond_age1 > 0 && state.cond_age2 > 0">{{ state.cond_age1 }}- {{ state.cond_age2 }}岁</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('condHeight')">
            <span class="name">身高范围</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.cond_height1 > 0 && state.cond_height2 > 0">{{ state.cond_height1 }}- {{ state.cond_height2 }}cm</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('condWeight')">
            <span class="name">体重范围</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.cond_weight1 > 0 && state.cond_weight2 > 0">{{ state.cond_weight1 }}- {{ state.cond_weight2 }}kg</span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('condMarry')">
            <span class="name">婚况</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.cond_marry && state.cond_marry != '0'">
                <p>{{ stateText.cond_marry }}</p>
              </span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('condEducation')">
            <span class="name">学历</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.cond_education && state.cond_education != '0'">
                <p>{{ stateText.cond_education }}</p>
              </span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('condSalary')">
            <span class="name">年收入</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.cond_salary && state.cond_salary != '0'">
                <p>{{ stateText.cond_salary }}</p>
              </span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('condHouse')">
            <span class="name">购房</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.cond_house && state.cond_house != '0'">
                <p>{{ stateText.cond_house }}</p>
              </span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('condCar')">
            <span class="name">购车</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.cond_car && state.cond_car != '0'">
                <p>{{ stateText.cond_car }}</p>
              </span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('condChild')">
            <span class="name">小孩情况</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.cond_child && state.cond_child != '0'">
                <p>{{ stateText.cond_child }}</p>
              </span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <!-- <div class="item" @click="openItemPicker('condLunar')">
            <span class="name">生肖</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.cond_lunar && state.cond_lunar != '0'">
                <p>{{ stateText.cond_lunar }}</p>
              </span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
          <div class="item" @click="openItemPicker('condAstro')">
            <span class="name">星座</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.cond_astro && state.cond_astro != '0'">
                <p>{{ stateText.cond_astro }}</p>
              </span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div> -->
          <div class="item" @click="openItemPicker('condArea')">
            <span class="name">{{ config.dist_title || '居住地' }}</span>
            <div class="flex flex_ac">
              <span class="value" v-if="state.cond_dist1 > 0">
                <p>{{ stateText.cond_dist1 }}{{ stateText.cond_dist2 }} {{ stateText.cond_dist3 }} {{ stateText.cond_dist4 }}</p>
              </span>
              <span v-else>不限</span>
              <i class="iconfont icon-youjiantou-01"></i>
            </div>
          </div>
        </div>
      </div>
      <div style="height: 1.6rem"></div>
      <div class="flex_dc btn">
        <span class="reset" @click="reset">重置</span>
        <span @click="searchData">搜索</span>
      </div>
    </div>
    <oeui-contrast ref="condAgePicker" title="你期望TA的年龄" :list="listState.ageCondList" :list2="listState.ageCondList" :current="[Number(state.cond_age1), Number(state.cond_age2)]" @confirm="sendCondAge" v-if="listState.ageCondList.length > 0" />
    <oeui-contrast ref="condHeightPicker" title="你期望TA的身高" :list="listState.heightCondList" :list2="listState.heightCondList" :current="[Number(state.cond_height1), Number(state.cond_height2)]" @confirm="sendCondHeight" v-if="listState.heightCondList.length > 0" />
    <oeui-contrast ref="condWeightPicker" title="你期望TA的体重" :list="listState.weightCondList" :list2="listState.weightCondList" :current="[Number(state.cond_weight1), Number(state.cond_weight2)]" @confirm="sendCondWeight" v-if="listState.weightCondList.length > 0" />
    <oeui-more ref="condMarryPicker" title="你期望TA的婚姻状况" :list="listState.marryCondList" :current="state.cond_marry" v-if="listState.marryCondList.length > 0" @confirm="sendCondMarry" />
    <oeui-more ref="condEducationPicker" title="你期望TA的学历" :list="listState.educationCondList" :current="state.cond_education" v-if="listState.educationCondList.length > 0" @confirm="sendCondEducation" />
    <oeui-more ref="condSalaryPicker" title="你期望TA的年收入" :list="listState.salaryCondList" :current="state.cond_salary" v-if="listState.salaryCondList.length > 0" @confirm="sendCondSalary" />
    <oeui-more ref="condHousePicker" title="你期望TA的住房情况" :list="listState.houseCondList" :current="state.cond_house" v-if="listState.houseCondList.length > 0" @confirm="sendCondHouse" />
    <oeui-more ref="condCarPicker" title="你期望TA的购车情况" :list="listState.carCondList" :current="state.cond_car" v-if="listState.carCondList.length > 0" @confirm="sendCondCar" />
    <oeui-more ref="condChildPicker" title="你期望TA的小孩情况" :list="listState.childCondList" :current="state.cond_child" v-if="listState.childCondList.length > 0" @confirm="sendCondChild" />
    <oeui-more ref="condLunarPicker" title="你期望TA的生肖" :list="listState.lunarCondList" :current="state.cond_lunar" v-if="listState.lunarCondList.length > 0" @confirm="sendCondLunar" />
    <oeui-more ref="condAstroPicker" title="你期望TA的星座" :list="listState.astroCondList" :current="state.cond_astro" v-if="listState.astroCondList.length > 0" @confirm="sendCondAstro" />
    <oeui-area ref="condDistPicker" :title="'你期望TA的' + config.dist_title || '居住地'" :list="listState.distCondList" :limit="areaLimit" :current="[state.cond_dist1, state.cond_dist2, state.cond_dist3, state.cond_dist4]" @change="sendCondArea" v-if="listState.distCondList.length > 0" />
  </oeui-popup>
</template>

<script setup>
import { getCurrentInstance, ref, reactive, nextTick , watch, computed } from 'vue'
import { useStore } from 'vuex'
import { getPickerData, getDistPicker } from '@/utils/main'
import oeuiArea from '@/oeui/area.vue'
import oeuiContrast from '@/oeui/contrast.vue'
import oeuiPopup from '@/oeui/popup.vue'
import oeuiMore from '@/oeui/more.vue'
const store = useStore()
const config = computed(() => store.state.config)

const props = defineProps({
  current: {
    type: Object,
  },
})

const emit = defineEmits(['sendSearch'])
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const areaLimit = ref({
  value: 0,
  text: '不限',
})
const state = reactive({
  cond_age1: '',
  cond_age2: '',
  cond_height1: '',
  cond_height2: '',
  cond_weight1: '',
  cond_weight2: '',
  cond_marry: '',
  cond_education: '',
  cond_salary: '',
  cond_house: '',
  cond_car: '',
  cond_child: '',
  // cond_lunar: '',
  // cond_astro: '',
  cond_dist1: '',
  cond_dist2: '',
  cond_dist3: '',
  cond_dist4: '',
})

const stateText = reactive({
  cond_marry: '',
  cond_education: '',
  cond_salary: '',
  cond_house: '',
  cond_car: '',
  cond_child: '',
  // cond_lunar: '',
  // cond_astro: '',
  cond_dist1: '',
  cond_dist2: '',
  cond_dist3: '',
  cond_dist4: '',
})
const listState = reactive({
  ageCondList: [],
  heightCondList: [],
  weightCondList: [],
  marryCondList: [],
  educationCondList: [],
  salaryCondList: [],
  houseCondList: [],
  carCondList: [],
  childCondList: [],
  lunarCondList: [],
  astroCondList: [],
  distCondList: [],
})

const openItemPicker = item => {
  //打开各项 picker
  let text = item.toLowerCase()
  let condText = text.replace('cond', '')
  if (text.includes('cond')) {
    if (condText == 'area') {
      return getDistDataCond()
    } else if (listState[condText + 'CondList'] && listState[condText + 'CondList'].length == 0) {
      return getItemDataCond(condText)
    }
    nextTick(() => {
      proxy.$refs[item + 'Picker'].open()
    })
    return
  }
}

const getItemDataCond = (item, limit = true) => {
  //获取picker数据
  const p = getPickerData(item, limit)
  p.then(res => {
    if (res.ret == 1) {
      listState[item + 'CondList'] = res.result
      nextTick(() => {
        proxy.$refs[`cond${titleUpperCase(item)}Picker`].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
  return p
}

const getDistDataCond = () => {
  //获取居住地数据
  getDistPicker().then(res => {
    if (res.ret == 1) {
      listState.distCondList = res.result
      nextTick(() => {
        proxy.$refs['condDistPicker'].open()
      })
    } else {
      OEUI.toast({ text: '加载数据失败，请检查' })
    }
  })
}

const titleUpperCase = str => {
  return str.slice(0, 1).toUpperCase() + str.slice(1).toLowerCase()
}

const sendCondAge = val => {
  state.cond_age1 = val[0].value
  state.cond_age2 = val[1].value
}
const sendCondHeight = val => {
  state.cond_height1 = val[0].value
  state.cond_height2 = val[1].value
}
const sendCondWeight = val => {
  state.cond_weight1 = val[0].value
  state.cond_weight2 = val[1].value
}
const sendCondMarry = val => {
  if (val[0].value == 0) {
    state.cond_marry = ''
    stateText.cond_marry = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_marry = arr.join(',')
  stateText.cond_marry = arr_text.join(' ')
}
const sendCondEducation = val => {
  if (val[0].value == 0) {
    state.cond_education = ''
    stateText.cond_education = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_education = arr.join(',')
  stateText.cond_education = arr_text.join(' ')
}
const sendCondSalary = val => {
  if (val[0].value == 0) {
    state.cond_salary = ''
    stateText.cond_salary = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_salary = arr.join(',')
  stateText.cond_salary = arr_text.join(' ')
}
const sendCondHouse = val => {
  if (val[0].value == 0) {
    state.cond_house = ''
    stateText.cond_house = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_house = arr.join(',')
  stateText.cond_house = arr_text.join(' ')
}
const sendCondCar = val => {
  if (val[0].value == 0) {
    state.cond_car = ''
    stateText.cond_car = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_car = arr.join(',')
  stateText.cond_car = arr_text.join(' ')
}
const sendCondChild = val => {
  if (val[0].value == 0) {
    state.cond_child = ''
    stateText.cond_child = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_child = arr.join(',')
  stateText.cond_child = arr_text.join(' ')
}
const sendCondLunar = val => {
  if (val[0].value == 0) {
    state.cond_lunar = ''
    stateText.cond_lunar = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_lunar = arr.join(',')
  stateText.cond_lunar = arr_text.join(' ')
}
const sendCondAstro = val => {
  if (val[0].value == 0) {
    state.cond_astro = ''
    stateText.cond_astro = '不限'
    return
  }
  let arr = []
  let arr_text = []
  val.forEach(v => {
    arr.push(v.value)
    arr_text.push(v.text)
  })
  state.cond_astro = arr.join(',')
  stateText.cond_astro = arr_text.join(' ')
}
const sendCondArea = val => {
  state.cond_dist1 = val[0]?.value || ''
  stateText.cond_dist1 = val[0]?.text || ''
  state.cond_dist2 = val[1]?.value || ''
  stateText.cond_dist2 = val[1]?.text || ''
  state.cond_dist3 = val[2]?.value || ''
  stateText.cond_dist3 = val[2]?.text || ''
  state.cond_dist4 = val[3]?.value || ''
  stateText.cond_dist4 = val[3]?.text || ''
}

const searchData = () => {
  //点击搜索
  proxy.$refs.popSearch.close()
  emit('sendSearch', state)
}
const open = () => {
  proxy.$refs.popSearch.open()
}
const close = () => {
  proxy.$refs.popSearch.close()
}

const oldCurrent = ref({})
const reset = () => {
  initCurrent(oldCurrent.value)
}

defineExpose({ close, open, searchData })

const initCurrent = data => {
  state.cond_age1 = Number(data.age1) || ''
  state.cond_age2 = Number(data.age2) || ''
  state.cond_height1 = Number(data.height1) || ''
  state.cond_height2 = Number(data.height2) || ''
  state.cond_weight1 = Number(data.weight1) || ''
  state.cond_weight2 = Number(data.weight2) || ''
  state.cond_marry = data.marry || ''
  state.cond_education = data.education || ''
  state.cond_salary = data.salary || ''
  state.cond_house = data.house || ''
  state.cond_car = data.car || ''
  state.cond_child = data.child || ''
  // state.cond_lunar = data.lunar || ''
  // state.cond_astro = data.astro || ''
  state.cond_dist1 = data.area1 || ''
  state.cond_dist2 = data.area2 || ''
  state.cond_dist3 = data.area3 || ''
  state.cond_dist4 = data.area3 || ''

  stateText.cond_marry = data.marry_arr?.length ? data.marry_t : ''
  stateText.cond_education = data.education ? data.education_t : ''
  stateText.cond_salary = data.salary ? data.salary_t : ''
  stateText.cond_house = data.house ? data.house_t : ''
  stateText.cond_car = data.car ? data.car_t : ''
  stateText.cond_child = data.child ? data.child_t : ''
  // stateText.cond_lunar = Number(data.lunar) ? data.lunar_t : ''
  // stateText.cond_astro = Number(data.astro) ? data.astro_t : ''
  stateText.cond_dist1 = data.area1 ? data.area1_t : ''
  stateText.cond_dist2 = data.area2 ? data.area2_t : ''
  stateText.cond_dist3 = data.area3 ? data.area3_t : ''
  stateText.cond_dist4 = data.area4 ? data.area3_t : ''
}

watch(
  () => props.current,
  newdata => {
    if (newdata) {
      oldCurrent.value = newdata
      initCurrent(newdata)
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
</script>
<style lang="scss" scoped>
.main {
  height: 100vh;
  width: 100%;
  background: #f2f4f5;
  overflow-y: scroll;

  .top {
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 300;
    box-shadow: 0.0267rem -0.0267rem 0.24rem 0px #f3e7e7;

    .back {
      width: 1.1733rem;
      height: 1.1733rem;
      position: absolute;
      right: 0;
      font-size: 0.5333rem;
    }

    .title {
      font-size: 0.4267rem;
      line-height: 0.64rem;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #2a2546;
    }
  }

  .content {
    padding: 0.32rem;
    font-family: PingFang SC-Medium, PingFang SC;

    .list {
      background: #fff;
      border-radius: 0.32rem;
      padding: 0.32rem;

      .item_range {
        .title {
          height: 0.4rem;
          color: #333;
          margin-bottom: 0.2667rem;

          .name {
            font-weight: bold;
            font-size: 0.3733rem;
          }

          .value {
            font-size: 0.3733rem;
            font-weight: 500;
          }
        }

        .slider {
          padding: 0.2667rem 0;
          padding-bottom: 0.4rem;
        }
      }

      .item_more {
        .title {
          height: 0.4rem;
          color: #333;
          margin-bottom: 0.2667rem;

          .name {
            font-weight: bold;
            font-size: 0.3733rem;
          }
        }

        .data {
          span {
            border: 0.0267rem solid $color_main;
            color: $color_main;
            height: 0.5867rem;
            line-height: 0.5867rem;
            border-radius: 0.5333rem;
            padding: 0 0.3733rem;
            margin-right: 0.4rem;
            font-size: 0.32rem;
            margin-bottom: 0.32rem;
            font-weight: normal;
            float: left;

            &.current {
              background: $color_main;
              color: #fff;
            }
          }
        }
      }

      .item {
        line-height: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .name {
          font-weight: bold;
          font-size: 0.3733rem;
          color: #333;
        }

        div {
          span {
            font-size: 0.32rem;
            color: #ccc;
            font-weight: normal;
          }

          .value {
            color: #000;
          }

          i {
            color: #888;
            margin-left: 0.08rem;
          }
        }
      }
    }
  }

  .btn {
    position: fixed;
    width: 85vw;
    bottom: 0;
    height: 1.6rem;

    span {
      background: $color_main;
      color: #fff;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      padding: 0.2133rem 1.76rem;
      border-radius: 0.48rem;
      cursor: pointer;
    }

    .reset {
      background: #ccc;
      padding: 0.2133rem 0.5333rem;
      margin-right: 0.4rem;
    }
  }
}
</style>

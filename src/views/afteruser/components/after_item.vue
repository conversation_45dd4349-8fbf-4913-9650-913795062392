<!-- @format -->

<template>
  <div class="box" @click="$router.push('/afteruser/detail?id=' + item.userid)" v-if="item.userid">
    <div v-if="item.match_nums > 0" style="height: 0.64rem"></div>
    <div class="tips pa" v-if="item.match_nums > 0">上次推荐: {{ item.match_lasttime_t }}，匹配{{ item.match_nums }}个用户</div>
    <div class="item_box">
      <div class="data">
        <div class="head">
          <img v-if="item.headimg" v-lazy="item.headimg_m1_url" type="user" alt="" />
          <template v-else>
            <img v-if="item.gender == 1" src="@/assets/images/gender_1.png" alt="" />
            <img v-else-if="item.gender == 2" src="@/assets/images/gender_2.png" alt="" />
            <img v-else src="@/assets/images/gender_0.png" alt="" />
          </template>
        </div>
        <div class="info">
          <p class="job">
            昵称:&nbsp;
            <em class="username ws">{{ item.username }}</em>
            <em class="id">(编号:&nbsp;{{ item.userid }})</em>
          </p>
          <p class="base">
            {{ item.age }}岁
            <template v-if="item.job > 0">/{{ item.job_t }}</template>
          </p>
          <p class="base" v-if="item.marry > 0 || item.education > 0 || item.height > 0">
            <template v-if="item.marry > 0">{{ item.marry_t }}</template>
            <template v-if="item.marry > 0">·</template>
            <template v-if="item.education > 0">{{ item.education_t }}</template>
            <template v-if="item.education > 0">·</template>
            <template v-if="item.height > 0">{{ item.height }}cm</template>
          </p>
          <div class="dist">
            <span v-if="item.dist1 > 0 && item.list_dist_t">现居：{{ item.list_dist_t }}</span>
            <span style="text-align: right" v-if="item.salary > 0">年收入：{{ item.salary_t }}</span>
          </div>
        </div>
      </div>
      <div class="share">
        <template v-if="item.match_nums > 0">
          <div class="show flex_1 flex_dc" @click.stop="$router.push('/match/log?id=' + item.userid)">
            <span>
              <img src="@/assets/images/after_log.png" alt="" />
            </span>
            <p>推荐记录</p>
          </div>
          <div class="show flex_1 flex_dc" @click.stop="$router.push('/match/result?id=' + item.userid)">
            <span>
              <img src="@/assets/images/after_match.png" alt="" />
            </span>
            <p>再次推荐</p>
          </div>
        </template>
        <div class="show flex_1 flex_dc" v-else @click.stop="$router.push('/match/result?id=' + item.userid)">
          <span>
            <img src="@/assets/images/after_match.png" alt="" />
          </span>
          <p>开始推荐</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { getTime } from '@/utils/hooks.js'
import { useStore } from 'vuex'
const store = useStore()
const config = computed(() => store.state.config)
const emits = defineEmits([])
defineProps({
  item: {
    type: Object,
  },
})
</script>

<style lang="scss" scoped>
.box {
  margin-bottom: 0.32rem;
  position: relative;

  .tips {
    background: $color_main;
    color: #fff;
    font-weight: normal;
    font-family: PingFang SC, PingFang SC;
    font-size: 0.32rem;
    line-height: 0.48rem;
    width: fit-content;
    border-radius: 0.32rem;
    padding: 0.08rem 0.7467rem 0.5333rem 0.2133rem;
    left: 0;
    top: 0;
  }

  .item_box {
    cursor: pointer;
    background: #fff;
    border-radius: 0.32rem;
    box-sizing: border-box;
    font-family: PingFang SC, PingFang SC;
    position: relative;
    overflow: hidden;

    .data {
      padding: 0.32rem;
      box-sizing: border-box;
      display: flex;
      align-content: center;
      border-bottom: 0.0267rem solid #f2f4f5;

      .head {
        width: 2.0533rem;
        height: 2.0533rem;
        border-radius: 0.2133rem;
        overflow: hidden;
        margin-right: 0.2133rem;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .job {
          font-size: 0.4267rem;
          line-height: 0.6667rem;
          color: #3d3d3d;
          font-weight: 500;
          display: flex;
          align-items: center;

          .id {
            font-size: 0.32rem;
            color: #999;
            position: relative;
            top: 2px;
          }

          .username {
            max-width: 100px;
          }
        }

        .name {
          margin-top: 0.1067rem;
          font-size: 0.32rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: normal;
          color: #999999;
          line-height: 0.4533rem;
          display: flex;
          align-items: center;

          em {
            max-width: 2.6667rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }

        .base {
          margin-top: 0.2133rem;
          font-size: 0.3467rem;
          line-height: 0.48rem;
          color: #999;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
        }

        .dist {
          display: none !important;
          width: 100%;
          margin-top: 0.2133rem;
          font-size: 0.3467rem;
          line-height: 0.48rem;
          color: #666;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          display: flex;
          align-items: center;
          justify-content: space-between;

          span {
            flex: 1;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
    }

    .share {
      padding: 0.32rem;
      box-sizing: border-box;
      display: flex;
      align-content: center;
      justify-content: space-between;

      .show {
        color: #7d68fe;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        display: flex;
        align-items: center;
        flex-shrink: 0;

        span {
          width: 0.64rem;
          height: 0.64rem;
          margin-right: 0.1067rem;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        p {
          position: relative;
          top: 0.0267rem;
          font-weight: normal;
        }
      }
    }

    .tips {
      position: absolute;
      background: $color_main;
      color: #fff;
      right: -0.6133rem;
      top: -0.1067rem;
      padding: 0.4rem 0.5333rem 0.0533rem 0.5333rem;
      font-size: 0.2667rem;
      font-weight: normal;
      line-height: 0.3733rem;
      transform: rotate(45deg);

      &.success {
        background: #15ce7b;
      }

      &.audit {
        background: #ccc;
      }

      &.fail {
        background: #f40;
      }
    }
  }
}
</style>

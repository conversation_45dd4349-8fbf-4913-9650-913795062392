<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">推荐记录</div>
    </div>
    <div class="h44"></div>
    <div class="list">
      <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
        <div v-if="info.userid" class="data" @click="$router.push('/user/detail?id=' + info.userid)">
          <div class="head">
            <img v-if="info.headimg" v-lazy="info.headimg_m1_url" type="user" alt="" />
            <template v-else>
              <img v-if="info.gender == 1" src="@/assets/images/gender_1.png" alt="" />
              <img v-else-if="info.gender == 2" src="@/assets/images/gender_2.png" alt="" />
              <img v-else src="@/assets/images/gender_0.png" alt="" />
            </template>
          </div>
          <div class="info">
            <p class="job">
              {{ info.age }}岁
              <template v-if="info.job > 0">/{{ info.job_t }}</template>
            </p>
            <p class="name">
              昵称: &nbsp;
              <em>{{ info.username }}</em>
              &emsp; (编号:&nbsp;{{ info.userid }})
            </p>
            <p class="base" v-if="info.marry > 0 || info.education > 0 || info.height > 0">
              <template v-if="info.marry > 0">{{ info.marry_t }}</template>
              <template v-if="info.marry > 0">·</template>
              <template v-if="info.education > 0">{{ info.education_t }}</template>
              <template v-if="info.education > 0">·</template>
              <template v-if="info.height > 0">{{ info.height }}cm</template>
            </p>
          </div>
        </div>
        <div v-else class="list_skelecton flex flex_ac" style="background: transparent">
          <div class="head"></div>
          <div class="flex_1">
            <p></p>
            <span></span>
          </div>
        </div>
        <div class="num">
          已向ta推荐了
          <em>{{ total }}位</em>
          异性用户
        </div>
        <template v-if="list.length">
          <div class="item_box" v-for="item in list" :key="item.afterunid">
            <p class="time" v-if="item.bat_time > 0">{{ item.addtime_t }}</p>
            <div class="remark" v-if="item.bat_time > 0">
              <div class="tit">
                <i class="iconfont icon-black-board130"></i>
                <span>红娘荐语</span>
              </div>
              <p>{{ item.remark || '我为你找到了一些合适的异性用户，看看吧！' }}</p>
            </div>
            <div class="data" @click="$router.push('/user/detail?id=' + item.user2.userid)">
              <div class="head">
                <img v-if="item.user2.headimg" v-lazy="item.user2.headimg_m1_url" type="user" alt="" />
                <template v-else>
                  <img v-if="item.user2.gender == 1" src="@/assets/images/gender_1.png" alt="" />
                  <img v-else-if="item.user2.gender == 2" src="@/assets/images/gender_2.png" alt="" />
                  <img v-else src="@/assets/images/gender_0.png" alt="" />
                </template>
              </div>
              <div class="info">
                <p class="job">
                  {{ item.user2.age }}岁
                  <template v-if="item.user2.job > 0">/{{ item.user2.job_t }}</template>
                </p>
                <p class="name">
                  昵称: &nbsp;
                  <em>{{ item.user2.username }}</em>
                  &emsp; (编号:&nbsp;{{ item.user2.userid }})
                </p>
                <p class="base" v-if="item.user2.marry > 0 || item.user2.education > 0 || item.user2.height > 0">
                  <template v-if="item.user2.marry > 0">{{ item.user2.marry_t }}</template>
                  <template v-if="item.user2.marry > 0">·</template>
                  <template v-if="item.user2.education > 0">{{ item.user2.education_t }}</template>
                  <template v-if="item.user2.education > 0">·</template>
                  <template v-if="item.user2.height > 0">{{ item.user2.height }}cm</template>
                </p>
                <div class="dist">
                  <span v-if="item.user2.dist1 > 0 && item.user2.list_dist_t">现居：{{ item.user2.list_dist_t }}</span>
                  <span style="text-align: right" v-if="item.user2.salary > 0">年收入：{{ item.user2.salary_t }}</span>
                </div>
              </div>
            </div>
          </div>
        </template>
        <div v-else-if="!list.length && listStatus != 'no_data'" class="list_skelecton flex flex_ac">
          <div class="head"></div>
          <div class="flex_1">
            <p></p>
            <span></span>
          </div>
        </div>
      </oeui-list>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, computed } from 'vue'
import { useRoute } from 'vue-router'
import { getAfterMatch } from '@/api/afteruser.js'
import oeuiList from '@/oeui/list.vue'
import { useStore } from 'vuex'
const store = useStore()
const config = computed(() => store.state.config)
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const route = useRoute()

const list = ref([])

const info = ref({})
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')
const total = ref(0)

const getList = (flag, callback) => {
  if (page.value == 0) return
  getAfterMatch({
    s_uid1: route.query.id,
    page: page.value,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      info.value = res.result.user1
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      total.value = res.result.total
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}
const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

getList()
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}

.list {
  padding: 0.1067rem 0.4267rem;

  .data {
    box-sizing: border-box;
    display: flex;
    align-content: center;
    margin-top: 0.2133rem;

    .head {
      width: 2.0533rem;
      height: 2.0533rem;
      border-radius: 0.2133rem;
      overflow: hidden;
      margin-right: 0.2133rem;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .job {
        font-size: 0.48rem;
        line-height: 0.6667rem;
        color: #3d3d3d;
        font-weight: 500;
      }

      .name {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
        display: flex;
        align-items: center;

        em {
          max-width: 2.6667rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }

      .base {
        margin-top: 0.2133rem;
        font-size: 0.3467rem;
        line-height: 0.48rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }

      .dist {
        width: 100%;
        margin-top: 0.2133rem;
        font-size: 0.3467rem;
        line-height: 0.48rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }

  .num {
    margin: 0.4267rem 0;
    border-radius: 0.32rem;
    padding: 0.32rem;
    font-size: 0.3733rem;
    font-weight: normal;
    line-height: 0.5333rem;
    font-family: PingFang SC, PingFang SC;
    background: rgba(255, 255, 255, 0.32);
    border: 0.0267rem solid #ffffff;

    em {
      font-weight: 600;
    }
  }

  .item_box {
    font-family: PingFang SC, PingFang SC;
    margin-bottom: 0.32rem;

    .time {
      color: #999;
      text-align: center;
      font-size: 0.32rem;
      line-height: 0.4533rem;
      font-weight: normal;
      margin-top: 0.64rem;
      margin-bottom: 0.4267rem;
    }

    .remark {
      // background: url('~@/assets/images/remark_bg.png') no-repeat;
      // background-size: cover;
      padding: 0.32rem;
      border-radius: 0.32rem 0.32rem 0 0;
      margin-bottom: 0.32rem;
      background: linear-gradient(180deg, #f4f1ff, #fff);

      .tit {
        display: flex;
        align-items: center;
        color: $color_main;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: 500;

        span {
          margin-left: 0.1333rem;
        }

        i {
          font-size: 0.5333rem;
        }
      }

      p {
        margin-top: 0.2667rem;
        font-size: 0.3733rem;
        font-weight: normal;
        line-height: 0.5333rem;
        color: #31293b;
      }
    }

    .data {
      margin-top: -0.32rem;
      box-sizing: border-box;
      display: flex;
      align-content: center;
      padding: 0.32rem;
      background: #fff;
      border-bottom: 1px solid #f7f7f7;

      // border-radius: 0.32rem;
      .head {
        width: 2.0533rem;
        height: 2.0533rem;
        border-radius: 0.2133rem;
        overflow: hidden;
        margin-right: 0.2133rem;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .job {
          font-size: 0.48rem;
          line-height: 0.6667rem;
          color: #3d3d3d;
          font-weight: 500;
        }

        .name {
          margin-top: 0.1067rem;
          font-size: 0.32rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: normal;
          color: #999999;
          line-height: 0.4533rem;
          display: flex;
          align-items: center;

          em {
            max-width: 2.6667rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }

        .base {
          margin-top: 0.2133rem;
          font-size: 0.3467rem;
          line-height: 0.48rem;
          color: #666;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
        }

        .dist {
          width: 100%;
          margin-top: 0.2133rem;
          font-size: 0.3467rem;
          line-height: 0.48rem;
          color: #666;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          display: flex;
          align-items: center;
          justify-content: space-between;

          span {
            flex: 1;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
    }
  }
}

.main {
  position: fixed;
  width: 100vw;
  min-height: 100%;
  box-sizing: border-box;
}
</style>

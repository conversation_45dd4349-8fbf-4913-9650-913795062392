<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">匹配结果</div>
      <span class="more" @click="proxy.$refs.matchIntro_dialog.open()">操作说明</span>
    </div>
    <div class="h44"></div>
    <div class="list">
      <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
        <div v-if="info.userid" class="data" @click="$router.push('/user/detail?id=' + info.userid)">
          <div class="head">
            <img v-if="info.headimg" v-lazy="info.headimg_m1_url" type="user" alt="" />
            <template v-else>
              <img v-if="info.gender == 1" src="@/assets/images/gender_1.png" alt="" />
              <img v-else-if="info.gender == 2" src="@/assets/images/gender_2.png" alt="" />
              <img v-else src="@/assets/images/gender_0.png" alt="" />
            </template>
          </div>
          <div class="info">
            <p class="job">
              昵称:&nbsp;
              <em class="username ws">{{ info.username }}</em>
              <em class="id">(编号:&nbsp;{{ info.userid }})</em>
            </p>
            <p class="base">
              {{ info.age }}岁
              <template v-if="info.job > 0">/{{ info.job_t }}</template>
            </p>
            <p class="base" v-if="info.marry > 0 || info.education > 0 || info.height > 0">
              <template v-if="info.marry > 0">{{ info.marry_t }}</template>
              <template v-if="info.marry > 0">·</template>
              <template v-if="info.education > 0">{{ info.education_t }}</template>
              <template v-if="info.education > 0">·</template>
              <template v-if="info.height > 0">{{ info.height }}cm</template>
            </p>
            <div class="dist">
              <span v-if="info.dist1 > 0 && info.list_dist_t">现居：{{ info.list_dist_t }}</span>
              <span style="text-align: right" v-if="info.salary > 0">年收入：{{ info.salary_t }}</span>
            </div>
          </div>
        </div>
        <div v-else class="list_skelecton flex flex_ac" style="background: transparent">
          <div class="head"></div>
          <div class="flex_1">
            <p></p>
            <span></span>
          </div>
        </div>
        <div class="tips">
          系统已根据用户【{{ info.username }}】择偶条件匹配出
          <em>{{ total }}位</em>
          异性用户，修改
          <em @click="btn_search" class="color_main cursor" style="text-decoration: underline">匹配条件</em>
        </div>
        <template v-if="list.length">
          <div @click="changeUser(item.userid)" class="item_box" v-for="item in list" :key="item.userid">
            <div class="data_box" :class="{ current: checkList.includes(item.userid) }">
              <div class="flex flex_ac result">
                <i v-if="checkList.includes(item.userid)" class="iconfont icon-redio_checked"></i>
                <i v-else class="iconfont icon-rediobtn_nor"></i>
                <span>匹配度: {{ item.degree + '%' }}</span>
                <span v-if="item.match_times > 0">&nbsp;,该用户已匹配推送{{ item.match_times }}次</span>
              </div>
              <div class="flex">
                <div class="head" @click="$router.push('/user/detail?id=' + item.userid)">
                  <img v-if="item.headimg" v-lazy="item.headimg_m1_url" type="user" />
                  <template v-else>
                    <img v-if="item.gender == 1" src="@/assets/images/gender_1.png" />
                    <img v-else-if="item.gender == 2" src="@/assets/images/gender_2.png" />
                    <img v-else src="@/assets/images/gender_0.png" alt="" />
                  </template>
                </div>
                <div class="info">
                  <p class="job">
                    {{ item.age }}岁
                    <template v-if="item.job > 0">/{{ item.job_t }}</template>
                  </p>
                  <p class="name">
                    昵称: &nbsp;
                    <em>{{ item.username }}</em>
                    &emsp; (编号:&nbsp;{{ item.userid }})
                  </p>
                  <p class="base" v-if="item.marry > 0 || item.education > 0 || item.height > 0">
                    <template v-if="item.marry > 0">{{ item.marry_t }}</template>
                    <template v-if="item.marry > 0">·</template>
                    <template v-if="item.education > 0">{{ item.education_t }}</template>
                    <template v-if="item.education > 0">·</template>
                    <template v-if="item.height > 0">{{ item.height }}cm</template>
                  </p>
                  <div class="dist">
                    <span v-if="item.dist1 > 0 && item.list_dist_t">现居：{{ item.list_dist_t }}</span>
                    <span style="text-align: right" v-if="item.salary > 0">年收入：{{ item.salary_t }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <div v-else-if="!list.length && listStatus != 'no_data'" class="list_skelecton flex flex_ac">
          <div class="head"></div>
          <div class="flex_1">
            <p></p>
            <span></span>
          </div>
        </div>
      </oeui-list>
    </div>

    <div class="handle">
      <span class="num">
        已选
        <em class="color_main">{{ checkList.length }}位</em>
        异性
      </span>
      <span :class="{ dail: !checkList.length }" class="btn" @click="btn_matchSend">推送匹配结果</span>
    </div>
  </div>
  <oe_popup ref="matchResult_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="matchResult_dialog">
      <p class="title">推送匹配结果</p>
      <div class="content">
        <div class="remark">
          <div class="box">
            <textarea v-model="remark" maxlength="100" placeholder="请输入红娘推荐语"></textarea>
            <span class="nums">{{ remark.length }}/100</span>
          </div>
          <p>
            <font color="#f40">*</font>
            已选择推送
            <em class="color_main">{{ checkList.length }}位</em>
            异性用户 ,请确认
          </p>
        </div>
      </div>
      <div class="btn flex_dc">
        <span
          @click="
            proxy.$refs.matchResult_dialog.close(() => {
              remark = ''
            })
          "
          class="fail"
          style="background: #f2f4f5; color: #666"
        >
          取消
        </span>
        <span @click="checkSend()">确定</span>
      </div>
    </div>
  </oe_popup>

  <oe_popup ref="matchSuccess_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="matchSuccess_dialog">
      <div class="tips_img">
        <img src="@/assets/images/chat_tips.png" alt="" />
      </div>
      <p class="tit">推荐成功</p>
      <p class="tips">已成功将{{ macth_nums }}位异性用户推送给 【{{ info.username }}】</p>
      <div class="flex flex_ac flex_jsb btn">
        <div v-if="isWeiXin()" style="margin-right: 0.5333rem" @click="btn_weixinShare" class="weixin">
          <i class="iconfont icon-weixin"></i>
          微信分享
        </div>
        <div @click="proxy.$refs.matchSuccess_dialog.close()" class="event">知道了</div>
      </div>
    </div>
  </oe_popup>
  <oe_popup ref="matchIntro_dialog" :maskClose="false" mode="center" round="true" roundStyle=".64rem">
    <div class="intro_dialog">
      <p class="title">操作说明</p>
      <div class="contnet">
        <p class="tip">什么是主动推荐？</p>
        <p class="tips">服务红娘主动挑选异性用户推荐给C端用户，并且提供帮约服务，该帮约服务将直接关联推送服务红娘。</p>
        <p class="tip" style="margin-top: 0.32rem">操作流程是什么样的？</p>
        <p class="tips">
          1.系统将按照用户帮约申请次数倒序展示用户列表信息，服务红娘可根据用户择偶条件（红娘可主动修改择偶条件进行匹配），使用系统主动生成对应匹配异性用户。
          <br />
          2.服务红娘挑选确认匹配结果，推送给C端服务用户，用户可在信息列表及我的-红娘帮约中查阅推送信息。
          <br />
          3.C端用户查阅信息后，若选择帮约对象则该次服务直接关联该服务红娘，期间产生的服务奖励与正常帮约流程一致。
        </p>
      </div>
      <div @click="proxy.$refs.matchIntro_dialog.close()" class="event">知道了</div>
    </div>
  </oe_popup>

  <pop_custom ref="wxsharepopup" :bgrount="false" bg="none">
    <div @click="proxy.$refs.wxsharepopup.close()" style="height: 100vh; background: rgba(0, 0, 0, 0.45)">
      <img style="width: 50vw; right: 0.2667rem; top: 0.2667rem" class="pa" src="@/assets/images/jian.png" alt="" />
    </div>
  </pop_custom>

  <cond_search ref="condSearch" :current="infoCond" @sendSearch="sendSearch" />
</template>

<script setup>
import { ref, getCurrentInstance, nextTick, computed } from 'vue'
import { useRoute } from 'vue-router'
import { getAfterMatchResult, saveAfterMatch, getAfterUserDetail } from '@/api/afteruser.js'
import oeuiList from '@/oeui/list.vue'
import oe_popup from '@/oeui/popup.vue'
import pop_custom from '@/components/pop_custom.vue'
import cond_search from '@/views/afteruser/components/cond_search.vue'
import { saveShareAfter } from '@/api/afteruser.js'
import { isWeiXin, setWechatShare } from '@/utils/jssdk'
import { useStore } from 'vuex'
const store = useStore()
const config = computed(() => store.state.config)
const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI
const route = useRoute()

const list = ref([])

const info = ref({})
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')
const total = ref(0)
const state = ref({})

const checkList = ref([])

const changeUser = id => {
  if (!id) return
  if (checkList.value.includes(id)) checkList.value = checkList.value.filter(v => v != id)
  else checkList.value.push(id)
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getAfterMatchResult({
    s_uid1: route.query.id,
    page: page.value,
    ...state.value,
  }).then(res => {
    if (res.ret == 1) {
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      info.value = res.result.user1
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      total.value = res.result.total

      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const infoCond = ref({})
const getDetail = () => {
  getAfterUserDetail({
    id: route.query.id,
  }).then(res => {
    if (res.ret == 1) {
      infoCond.value = res.result.cond
      initCurrent(res.result.cond, () => {
        getList()
      })
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const initCurrent = (data, callback) => {
  state.value.s_age1 = Number(data.age1) || ''
  state.value.s_age2 = Number(data.age2) || ''
  state.value.s_height1 = Number(data.height1) || ''
  state.value.s_height2 = Number(data.height2) || ''
  state.value.s_weight1 = Number(data.weight1) || ''
  state.value.s_weight2 = Number(data.weight2) || ''
  state.value.s_marry = data.marry || ''
  state.value.s_education = data.education || ''
  state.value.s_salary = data.salary || ''
  state.value.s_house = data.house || ''
  state.value.s_car = data.car || ''
  state.value.s_child = data.child || ''
  // state.value.s_lunar = data.lunar || ''
  // state.value.s_astro = data.astro || ''
  state.value.s_dist1 = data.area1 || ''
  state.value.s_dist2 = data.area2 || ''
  state.value.s_dist3 = data.area3 || ''

  if (callback) callback()
}
getDetail()

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}
const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

// 搜索
const btn_search = () => {
  proxy.$refs.condSearch.open()
}

const sendSearch = data => {
  for (let key in data) {
    state.value['s_' + key.replace('cond_', '')] = data[key]
  }
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

// 推送操作
const remark = ref('')
const is_send = ref(true)
const btn_matchSend = () => {
  if (!checkList.value.length)
    return OEUI.toast({
      text: '请选择推送用户',
    })
  is_send.value = true
  proxy.$refs.matchResult_dialog.open()
}
const macth_nums = ref(0)

const checkSend = () => {
  if (!is_send.value) return
  is_send.value = false
  OEUI.loading.show()
  saveAfterMatch({
    uid1: route.query.id,
    ids: checkList.value.join(','),
    remark: remark.value || '我为你找到了一些合适的异性用户，看看吧！',
  })
    .then(res => {
      OEUI.loading.hide()
      if (res.ret == 1) {
        proxy.$refs.matchResult_dialog.close(() => {
          checkList.value = []
          macth_nums.value = res.result
          remark.value = ''
          proxy.$refs.matchSuccess_dialog.open()
          page.value = 1
          getList(true)
        })
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙,请联系客服!',
        })
      }
      setTimeout(() => {
        is_send.value = true
      }, 500)
    })
    .catch(() => {
      OEUI.loading.hide()
      is_send.value = true
      OEUI.toast({
        text: '系统繁忙,请联系客服!',
      })
    })
}

const btn_weixinShare = () => {
  saveShareAfter()
    .then(res => {
      if (res.ret == 1) {
        if (!isWeiXin()) return
        setWechatShare(res.result.data)
        nextTick(() => {
          proxy.$refs.matchSuccess_dialog.close(() => {
            proxy.$refs.wxsharepopup.open()
          })
        })
      } else {
        OEUI.toast({
          text: res.msg || '系统繁忙,请联系客服!',
        })
      }
    })
    .catch(() => {
      OEUI.toast({
        text: '系统繁忙,请联系客服!',
      })
    })
}
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}
.list {
  padding: 0.1067rem 0.4267rem;
  .data {
    box-sizing: border-box;
    display: flex;
    align-content: center;
    margin-top: 0.2133rem;
    .head {
      width: 2.0533rem;
      height: 2.0533rem;
      border-radius: 0.2133rem;
      overflow: hidden;
      margin-right: 0.2133rem;
      flex-shrink: 0;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .job {
        font-size: 0.4267rem;
        line-height: 0.6667rem;
        color: #3d3d3d;
        font-weight: 500;
        display: flex;
        align-items: center;
        .id {
          font-size: 0.32rem;
          color: #999;
          position: relative;
          top: 2px;
        }
        .username {
          max-width: 100px;
        }
      }
      .name {
        margin-top: 0.1067rem;
        font-size: 0.32rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: normal;
        color: #999999;
        line-height: 0.4533rem;
        display: flex;
        align-items: center;
        em {
          max-width: 2.6667rem;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .base {
        margin-top: 0.2133rem;
        font-size: 0.3467rem;
        line-height: 0.48rem;
        color: #999;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }
      .dist {
        display: none !important;
        width: 100%;
        margin-top: 0.2133rem;
        font-size: 0.3467rem;
        line-height: 0.48rem;
        color: #666;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
        display: flex;
        align-items: center;
        justify-content: space-between;
        span {
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
  }
  .tips {
    margin: 0.4267rem 0;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    em {
      font-weight: 600;
    }
  }

  .item_box {
    font-family: PingFang SC, PingFang SC;
    margin-bottom: 0.4267rem;
    box-sizing: border-box;
    .result {
      i {
        font-size: 0.48rem;
        margin-right: 0.1333rem;
        position: relative;
        top: 0.0267rem;
      }
      .icon-rediobtn_nor {
        color: #999;
      }
      font-weight: normal;
      font-size: 0.3733rem;
      line-height: 0.5333rem;
      color: $color_main;
      margin-bottom: 0.2133rem;
    }
    .data_box {
      box-sizing: border-box;
      margin-top: 0.32rem;
      padding: 0.32rem;
      background: #fff;
      border-radius: 0.32rem;

      .head {
        width: 2.0533rem;
        height: 2.0533rem;
        border-radius: 0.2133rem;
        overflow: hidden;
        margin-right: 0.2133rem;
        flex-shrink: 0;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .job {
          font-size: 0.48rem;
          line-height: 0.6667rem;
          color: #3d3d3d;
          font-weight: 500;
        }
        .name {
          margin-top: 0.1067rem;
          font-size: 0.32rem;
          font-family: PingFang SC, PingFang SC;
          font-weight: normal;
          color: #999999;
          line-height: 0.4533rem;
          display: flex;
          align-items: center;
          em {
            max-width: 2.6667rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
        .base {
          margin-top: 0.2133rem;
          font-size: 0.3467rem;
          line-height: 0.48rem;
          color: #666;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
        }
        .dist {
          width: 100%;
          margin-top: 0.2133rem;
          font-size: 0.3467rem;
          line-height: 0.48rem;
          color: #666;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: normal;
          display: flex;
          align-items: center;
          justify-content: space-between;
          span {
            flex: 1;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
      &.current {
        border: 0.0267rem solid $color_main;
      }
    }
  }
}
.main {
  position: fixed;
  width: 100vw;
  min-height: 100%;
  box-sizing: border-box;
}
.handle {
  .num {
    em {
      font-weight: 600;
    }
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #666;
  }
  height: 1.1733rem;
  position: fixed;
  width: 100vw;
  bottom: 0;
  left: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 0.1333rem 0.2667rem 0 #bfbfbf;
  padding: 0 0.4267rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  .btn {
    background: $color_main;
    color: #fff;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    padding: 0.16rem 0.4267rem;
    border-radius: 0.5333rem;
    cursor: pointer;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    &.dail {
      background: #ccc;
    }
  }
}

.matchResult_dialog {
  padding: 0.64rem 0;
  font-family: PingFang SC, PingFang SC;
  .title {
    font-size: 0.4267rem;
    font-weight: 500;
    color: #3d3d3d;
    line-height: 0.5867rem;
    text-align: center;
  }
  .content {
    padding: 0 0.64rem;
    overflow-y: scroll;
    .remark {
      margin-top: 0.64rem;
      .box {
        margin-top: 0.2133rem;
        background: #f2f4f5;
        height: 3.4667rem;
        border-radius: 0.32rem;
        padding: 0.32rem;
        box-sizing: border-box;
        position: relative;
        textarea {
          resize: none;
          border: none;
          width: 100%;
          height: 100%;
          background: none;
          &::placeholder {
            font-size: 0.3733rem;
            font-weight: normal;
            color: #c5c3c7;
            line-height: 0.5333rem;
          }
        }
        .nums {
          position: absolute;
          font-size: 0.32rem;
          font-weight: normal;
          color: #c5c3c7;
          line-height: 0.4533rem;
          right: 0.4rem;
          bottom: 0.0533rem;
        }
      }
      p {
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-weight: normal;
        color: #666;
        margin-top: 0.2667rem;
      }
    }
  }
  .btn {
    margin-top: 0.8533rem;
    padding: 0 0.64rem;
    span {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.4267rem;
      font-weight: normal;
      line-height: 0.5867rem;
      padding: 0.24rem 0;
      cursor: pointer;
      background: $color_main;
      border-radius: 0.5333rem;
      color: #fff;
    }
    .fail {
      margin-right: 0.5867rem;
      color: $color_main;
      background: rgba(125, 104, 254, 0.24);
    }
  }
}

.matchSuccess_dialog {
  text-align: center;
  padding: 0.64rem;
  background: url('../../assets/images/dailog_bg.png');
  background-size: cover;
  border-radius: 0.64rem;
  overflow: hidden;

  .tips_img {
    position: absolute;
    left: 50%;
    top: -1.7333rem;
    transform: translateX(-50%);
    width: 4.2667rem;
    height: 2.6133rem;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .tit {
    margin-top: 0.4267rem;
    text-align: center;
    font-size: 0.4267rem;
    line-height: 0.5867rem;
    font-weight: 500;
    color: #3d3d3d;
  }
  .tips {
    padding: 0 1.2533rem;
    margin-top: 0.4267rem;
    font-size: 0.3733rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #666;
    line-height: 0.5867rem;
  }
  .btn {
    margin-top: 0.64rem;
    font-size: 0.3733rem;
    line-height: 0.5333rem;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: normal;
    .weixin {
      flex: 1;
      border: 0.0267rem solid $color_main;
      box-sizing: border-box;
      color: $color_main;
      border-radius: 0.5333rem;
      padding: 0.24rem 0;
      .icon-weixin {
        color: #28c445;
        position: relative;
        top: 0.0267rem;
      }
    }
    .event {
      flex: 1;
      border: 0.0267rem solid $color_main;
      box-sizing: border-box;
      background: $color_main;
      color: #fff;
      border-radius: 0.5333rem;
      padding: 0.24rem 0;
    }
  }
}
.intro_dialog {
  .contnet {
    max-height: 40vh;
    overflow-y: auto;
  }
  .title {
    font-weight: bold;
    margin-bottom: 0.4267rem;
  }
  .tip {
    margin-top: 0;
    font-weight: bold;
    color: #000;
  }
  .tips {
    color: #3d3d3d;
  }
}
</style>

<!-- @format -->

<template>
  <div class="main">
    <div class="pf top_nav flex_dc">
      <span @click="$router.back()" class="back flex_dc">
        <i class="iconfont icon-a-fenxiangheibeifen-01"></i>
      </span>
      <div class="title flex_dc">主动推荐</div>
      <span @click="btn_search" class="more iconfont icon-shaixuan" style="font-size: 0.48rem"></span>
    </div>
    <div class="h44"></div>
    <div class="list">
      <oeui-list @refresh="refresh" :top="0" :bottom="1.3333" @scrollBottom="scrollBottom" company="rem" text="已加载完全部数据" :status="listStatus">
        <div class="filter flex_dc">
          <div class="flex_dc" @click="changTime">
            <span>{{ timeList[state.s_match].name }}</span>
            <i class="iconfont icon-youjiantou-01" :class="is_time ? 'show' : 'hide'"></i>
          </div>
          <div class="flex_dc" @click="changOrderby">
            <span>{{ state.s_orderby == 'matchtime_desc' ? '时间降序' : '时间升序' }}</span>
            <span class="flex_dc flex_v">
              <i :class="state.s_orderby == 'matchtime_desc' ? 'color_c3' : ''" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; transform: rotate(180deg); top: 0.08rem"></i>
              <i :class="state.s_orderby == 'matchtime_desc' ? '' : 'color_c3'" class="iconfont icon-sanjiao pr" style="font-size: 12px; line-height: 1; bottom: 0.0533rem"></i>
            </span>
          </div>
        </div>
        <template v-if="list.length">
          <after_item :item="item" v-for="item in list" :key="item.userid" />
        </template>
        <template v-else-if="listStatus != 'no_data'">
          <div class="list_skelecton flex flex_ac" v-for="item in 3" :key="item">
            <div class="head"></div>
            <div class="flex_1">
              <p></p>
              <span></span>
            </div>
          </div>
        </template>
      </oeui-list>
    </div>
  </div>
  <user_search ref="userSearch" @sendSearch="sendSearch" />
  <search_item ref="searchTime" type="time" :list="timeList" :current="state.s_match" @changItem="selectTime" @close="closeItem"></search_item>
</template>

<script>
export default {
  name: 'AfterUser',
}
</script>

<script setup>
import { ref, getCurrentInstance, nextTick } from 'vue'
import oeuiList from '@/oeui/list.vue'
import search_item from '@/components/search_itme.vue'
import after_item from '@/views/afteruser/components/after_item.vue'
import user_search from '@/views/user/components/user_search.vue'
import { getAfterUser } from '@/api/afteruser.js'

const { proxy } = getCurrentInstance()
const OEUI = proxy.OEUI

const list = ref([])
const page = ref(1)
const listStatus = ref('loading')
const pageCount = ref('0')
const state = ref({
  s_match: 0,
  s_orderby: 'matchtime_desc',
})
const timeList = [
  { name: '状态不限', val: 0 },
  { name: '有匹配', val: 1 },
  { name: '未匹配', val: 2 },
]

const is_time = ref(false)

const changTime = () => {
  nextTick(() => {
    if (is_time.value) {
      is_time.value = false
      proxy.$refs.searchTime.close()
    } else {
      is_time.value = true
      proxy.$refs.searchTime.open()
    }
  })
}

const selectTime = val => {
  is_time.value = false
  state.value.s_match = val
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}

const closeItem = () => {
  is_time.value = false
}

const getList = (flag, callback) => {
  if (page.value == 0) return
  getAfterUser({
    page: page.value,
    ...state.value,
  }).then(res => {
    if (res.ret == 1) {
      console.log(res.result.data)
      res.result.data = res.result.data ? res.result.data : []
      if (flag) list.value = res.result.data
      else list.value = [...list.value, ...res.result.data]
      pageCount.value = res.result.total
      page.value = res.result.nextpage
      if (res.result.pagecount == 0) {
        listStatus.value = 'no_data'
      } else if (res.result.nextpage == 0) {
        listStatus.value = 'no_result'
      } else {
        listStatus.value = 'loading'
      }
      if (callback) callback()
    } else {
      OEUI.toast({
        text: res.msg || '系统繁忙，请稍后再试',
      })
    }
  })
}

const changOrderby = () => {
  if (state.value.s_orderby == 'matchtime_desc') {
    state.value.s_orderby = 'matchtime_asc'
  } else {
    state.value.s_orderby = 'matchtime_desc'
  }
  page.value = 1
  //list.value = []
  //listStatus.value = 'loading'
  getList(true)
}

const refresh = done => {
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true, done)
}
const is_pull = ref(true)
const scrollBottom = () => {
  if (!is_pull.value) return
  is_pull.value = false
  getList(false, () => {
    is_pull.value = true
  })
}

const btn_search = () => {
  proxy.$refs.userSearch.open()
}

const sendSearch = data => {
  for (let key in data) {
    state.value['s_' + key] = data[key]
  }
  page.value = 1
  list.value = []
  listStatus.value = 'loading'
  getList(true)
}
getList()
</script>

<style lang="scss" scoped>
.top_nav {
  background: url('~@/assets/images/bg_main.png') no-repeat;
  background-size: cover;
}

.list {
  padding: 0.1067rem 0.4267rem;

  .filter {
    height: 1.0667rem;
    border-radius: 0.32rem;
    margin-bottom: 0.32rem;
    background: rgba(255, 255, 255, 0.5);

    div {
      flex: 1;
      color: #2a2546;
      text-align: center;

      span {
        margin-right: 0.1333rem;
        font-size: 0.3733rem;
        line-height: 0.5333rem;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: normal;
      }

      .color_c3 {
        color: #c5c3c7;
      }

      .hide {
        transform: rotate(90deg);
      }

      .show {
        transform: rotate(-90deg);
        position: relative;
        top: 0.0267rem;
      }
    }
  }
}

.main {
  position: fixed;
  width: 100vw;
  min-height: 100%;
  box-sizing: border-box;
}
</style>

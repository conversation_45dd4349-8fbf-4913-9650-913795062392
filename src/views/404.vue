<template>
  <div class="oe_404">
    <oe-nav :istop="false"></oe-nav>
    <img class="icon" src="../assets/images/404.gif">
  </div>
</template>

<script>
import oeNav from '../components/nav.vue'
export default {
  components:{
    oeNav
  },
  setup() {
    return {

    }
  }
}
</script>
<style lang='scss' scoped>
.oe_404{
  position: relative;
  min-height:100%;
  background:#ffffff;
  box-sizing: border-box;
  text-align: center;
  padding-top:3.2rem;
  .icon{
    width:100%;
  }
}
</style>